com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置.xlsx sheet:文本配置
rows {
  content: "时装资源正在准备中，请稍后再试"
  stringId: "SuitSkill_Waiting_Pak_Ready"
}
rows {
  content: "扣叮数据已上传至云端"
  switch: 1
  stringId: "UGC_CoCreate_CodingMode_CodingDataUploadTips"
}
rows {
  content: "当前有另一面玩家正在进入目标场景，暂时无法发起跳转，请稍后重试"
  switch: 1
  stringId: "UGC_MultiScene_JumpFailed_Tips3"
}
rows {
  content: "{0}-累计中"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips9"
}
rows {
  content: "测试文本：可以提前装饰痛包噢"
  switch: 1
  stringId: "SummerVacation_Decoration_Txt"
}
rows {
  content: "测试文本：吧唧兑换商店也能购买噢"
  switch: 1
  stringId: "SummerVacation_Task_Txt"
}
rows {
  content: "1.不同资源包之间可能存在公共资源，公共资源只会下载一次，不会重复下载。/n2.公共资源不会随某一个资源包被清理，可清理大小和选择的资源包数量有关。/n3.每个资源包首次下载完成都可以领取一份奖励。/n4.部分资源包需要下载其前置资源包后才能进行下载。/n5.游戏内加载的时装资源、缓存资源统一保存在缓存文件。"
  switch: 1
  stringId: "Pak_DescriptionInterfaceText"
}
rows {
  content: "1.IOS10.3及以上&Android 10及以上系统版本支持该功能，注：Ipad与一加手机无法使用该功能。另外，因系统稳定性的关系，会出现部分手机更换图标不成功的问题，需要您重启iPhone手机即可修复该问题。/n2.因部分手机存在系统差异，会导致该功能不可用，还请谅解。/n3.卸载并重装元梦之星客户端，您自定义的应用图标将恢复为元梦之星官方默认图标。/n4.如有疑问，可联系元梦之星客服进行咨询。"
  switch: 1
  stringId: "App_Icon_Change"
}
rows {
  content: "入场展示动画下载中"
  switch: 1
  stringId: "Pak_PV_DownLoad_Toast"
}
rows {
  content: "已更换应用图标，若未生效请重启手机。"
  switch: 1
  stringId: "App_Icon_Ios"
}
rows {
  content: "已更换应用图标。由于系统设置，更换图标将关闭元梦之星，请重新启动游戏。"
  switch: 1
  stringId: "App_Icon_Android"
}
rows {
  content: "遇到错误({0})，小助手已断开连接，是否需要重连？"
  switch: 1
  stringId: "UGC_AiAssistant_ErrorCodeTips"
}
