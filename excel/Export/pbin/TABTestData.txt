com.tencent.wea.xlsRes.table_TABTestConfig
excel/xls/T_TAB实验配置.xlsx sheet:TABTest配置
rows {
  TestType: "5"
  GroupKey: "exp_dhp_m10_newbie_guide_v1"
  ModuleKey: "m10_newbie_v1"
  OpenStatus: 1
  RegisterTime {
    seconds: 1716652800
  }
  minVersion: "1.2.100.89"
}
rows {
  TestType: "6"
  GroupKey: "exp_dhp_m7_battle_warm_v1"
  ModuleKey: "m7_warm"
  OpenStatus: 0
}
rows {
  TestType: "7"
  GroupKey: "exp_dhp_m10_active_farmguide_v1"
  ModuleKey: "m10_active_farmguide"
  OpenStatus: 0
}
rows {
  TestType: "8"
  GroupKey: "exp_m7p_ugc_v1"
  ModuleKey: "m7plus_ugcmap"
  OpenStatus: 0
}
rows {
  TestType: "9"
  GroupKey: "exp_dhp_m9_return_warm_v1"
  ModuleKey: "m9_return_warm"
  OpenStatus: 0
}
rows {
  TestType: "10"
  GroupKey: "m9_subtab_layer_v1"
  ModuleKey: "m9_subtab_layer"
  OpenStatus: 1
}
rows {
  TestType: "11"
  GroupKey: "exp_dhp_m14_topbattle_farm_v1"
  ModuleKey: "newbie_topbattle_farm"
  OpenStatus: 1
  RegisterTime {
    seconds: 1731513600
  }
}
rows {
  TestType: "12"
  GroupKey: "exp_dhp_m9_return_task_v1"
  ModuleKey: "m9_return_task"
  OpenStatus: 1
}
rows {
  TestType: "13"
  GroupKey: "exp_dhp_m10_return_warm_v2"
  ModuleKey: "m10_dhp_return_wam_v2"
  OpenStatus: 1
  returnWarmRoundStatusOnly: true
}
rows {
  TestType: "14"
  GroupKey: "exp_minigame_iaa_v1"
  ModuleKey: "m11_minigame_iaa"
  OpenStatus: 1
  channelId: "59000005"
}
rows {
  TestType: "15"
  GroupKey: "exp_dhp_m11_map_detail_v1"
  ModuleKey: "m11_map_detail"
  OpenStatus: 1
}
rows {
  TestType: "16"
  GroupKey: "exp_minigame_iaa_enterstyle_v1"
  ModuleKey: "m11_minigame_iaa_enterstyle"
  OpenStatus: 1
  channelId: "59000005"
}
rows {
  TestType: "17"
  GroupKey: "exp_m11_npc_haohaoya_v1"
  ModuleKey: "m11_npc_haohaoya"
  OpenStatus: 0
}
rows {
  TestType: "18"
  GroupKey: "exp_m13_team_mode_v1"
  ModuleKey: "m13_team_mode"
  OpenStatus: 1
  channelId: "10012733"
}
rows {
  TestType: "19"
  GroupKey: "exp_m13_team_mode_app_v1"
  ModuleKey: "m13_team_mode_app"
  OpenStatus: 1
}
rows {
  TestType: "21"
  GroupKey: "exp_ugc_mingtutang_v1"
  ModuleKey: "ugc_mingtutang"
  OpenStatus: 1
}
rows {
  TestType: "22"
  GroupKey: "exp_ugc_platform_homepage_v1"
  ModuleKey: "ugc_platform_homepage"
  OpenStatus: 1
}
rows {
  TestType: "23"
  GroupKey: "exp_newbie_farm_page_v1"
  ModuleKey: "newbie_farm_page"
  OpenStatus: 1
}
rows {
  TestType: "24"
  GroupKey: "exp_app_newbie_skip_role_v1"
  ModuleKey: "app_newbie_skip_role"
  OpenStatus: 1
}
rows {
  TestType: "25"
  GroupKey: "exp_m14_survival_ratio_v1"
  ModuleKey: "m14_survival_ratio"
  OpenStatus: 1
}
rows {
  TestType: "26"
  GroupKey: "exp_m14_m14_newbie_reward_guide_v1"
  ModuleKey: "m14_newbie_reward_guide"
  OpenStatus: 1
}
rows {
  TestType: "27"
  GroupKey: "exp m14 moba5v5 v1"
  ModuleKey: "m14_moba5v5"
  OpenStatus: 1
}
rows {
  TestType: "28"
  GroupKey: "exp_m14_newbie_signin_system_v1"
  ModuleKey: "m14_newbie_signin_system"
  OpenStatus: 1
}
rows {
  TestType: "29"
  GroupKey: "exp_m14_app_newbie_hub_v1"
  ModuleKey: "m14_app_newbie_hub"
  OpenStatus: 0
  RegisterTime {
    seconds: 1734537600
  }
}
rows {
  TestType: "30"
  GroupKey: "exp_m14_moba5v5_v2"
  ModuleKey: "m14_moba5v5_v2"
  OpenStatus: 1
}
rows {
  TestType: "31"
  GroupKey: "exp_m14_newbie_mode_guide_v1"
  ModuleKey: "m14_newbie_mode_guide"
  OpenStatus: 1
  RegisterTime {
    seconds: 1736524800
  }
  channelId: "59000005"
}
rows {
  TestType: "32"
  GroupKey: "exp_m14_newbie_mode_choose_character_v1"
  ModuleKey: "m14_newbie_mode_choose_character"
  OpenStatus: 1
}
rows {
  TestType: "33"
  GroupKey: "exp_m14_newbie_mode_guide_v2"
  ModuleKey: "m14_newbie_mode_guide_v2"
  OpenStatus: 1
  RegisterTime {
    seconds: 1739462400
  }
  channelId: "59000005"
}
rows {
  TestType: "34"
  GroupKey: "exp_newbie_role_enter_farm_v1"
  ModuleKey: "m14_newbie_role_enter_farm"
  OpenStatus: 1
  RegisterTime {
    seconds: 1740067200
  }
}
rows {
  TestType: "35"
  GroupKey: "exp_s11_jinji_suiji_v1"
  ModuleKey: "s11_jinji_suiji_v1"
  OpenStatus: 1
}
rows {
  TestType: "36"
  GroupKey: "exp_s11_jinji_sanlun_v1"
  ModuleKey: "s11_jinji_sanlun_v1"
  OpenStatus: 1
}
rows {
  TestType: "37"
  GroupKey: "exp_s11_jinji_32_1lun_v1"
  ModuleKey: "s11_jinji_32_1lun_v1"
  OpenStatus: 1
}
rows {
  TestType: "38"
  GroupKey: "exp_s10_zhujiemian_login_v1"
  ModuleKey: "s10_zhujiemian_login_v1"
  OpenStatus: 1
  RegisterTime {
    seconds: 1741708800
  }
}
rows {
  TestType: "39"
  GroupKey: "exp_m14_newbie_mode_guide_v3"
  ModuleKey: "m14_newbie_mode_guide_v3"
  OpenStatus: 1
  RegisterTime {
    seconds: 1741708800
  }
}
rows {
  TestType: "40"
  GroupKey: "exp_ymxyx_ttjjs_new_danju_v1"
  ModuleKey: "ymxyx_ttjjs_new_danju_v1"
  OpenStatus: 1
  RegisterTime {
    seconds: 1742313600
  }
}
rows {
  TestType: "41"
  GroupKey: "exp_ymyy_hud_change"
  ModuleKey: "ymyy_hud_change"
  OpenStatus: 1
  RegisterTime {
    seconds: 1743609600
  }
}
rows {
  TestType: "42"
  GroupKey: "exp_novice_team_tutorial"
  ModuleKey: "exp_novice_team_tutorial"
  OpenStatus: 1
  RegisterTime {
    seconds: 1743609600
  }
}
rows {
  TestType: "43"
  GroupKey: "exp_special_optimization_experiment_for_novice_guide"
  ModuleKey: "exp_special_optimization_experiment_for_novice_guide"
  OpenStatus: 1
  RegisterTime {
    seconds: 1743609600
  }
}
rows {
  TestType: "44"
  GroupKey: "exp_ymyy_hud_change_v2"
  ModuleKey: "ymyy_hud_change_v2"
  OpenStatus: 1
}
rows {
  TestType: "45"
  GroupKey: "exp_first_page_change"
  ModuleKey: "exp_first_page_change"
  OpenStatus: 1
}
rows {
  TestType: "46"
  GroupKey: "exp_novice_chooses_hero_v3"
  ModuleKey: "novice_chooses_hero_v3"
  OpenStatus: 1
}
rows {
  TestType: "47"
  GroupKey: "exp_play_select_optimize"
  ModuleKey: "play_select_optimize"
  OpenStatus: 1
}
rows {
  TestType: "48"
  GroupKey: "exp_play_select_optimize_v2"
  ModuleKey: "play_select_optimize_v2"
  OpenStatus: 1
}
rows {
  TestType: "49"
  GroupKey: "exp_overseas_new_user_v1"
  ModuleKey: "overseas_new_user_v1"
  OpenStatus: 1
}
rows {
  TestType: "50"
  GroupKey: "exp_new_hud_bi"
  ModuleKey: "new_hud_bi"
  OpenStatus: 1
}
rows {
  TestType: "51"
  GroupKey: "exp_skip_role_v250428"
  ModuleKey: "skip_role_v250428"
  OpenStatus: 1
}
rows {
  TestType: "52"
  GroupKey: "exp_s12_random_v2"
  ModuleKey: "s12_random_v2"
  OpenStatus: 1
}
rows {
  TestType: "53"
  GroupKey: "exp_xf_resolution_720p"
  ModuleKey: "xf_resolution_720p"
  OpenStatus: 1
}
rows {
  TestType: "55"
  GroupKey: "exp_discover_activity_v250428"
  ModuleKey: "discover_activity_v250428"
  OpenStatus: 1
}
rows {
  TestType: "54"
  GroupKey: "exp_farm_back_v1"
  ModuleKey: "farm_back_v1"
  OpenStatus: 1
}
rows {
  TestType: "56"
  GroupKey: "exp_king_new_fixed_map"
  ModuleKey: "king_new_fixed_map"
  OpenStatus: 1
  RegisterTime {
    seconds: 1747670400
  }
}
rows {
  TestType: "57"
  GroupKey: "exp_king_new_fixed_role"
  ModuleKey: "king_new_fixed_role"
  OpenStatus: 1
  RegisterTime {
    seconds: 1747670400
  }
}
rows {
  TestType: "58"
  GroupKey: "exp_new_selected_promotion"
  ModuleKey: "new_selected_promotion"
  OpenStatus: 1
  RegisterTime {
    seconds: 1749744000
  }
  channelId: "59000005"
  clientFilterLabel {
    labelType: ABTCFLT_WX_NewPlayerGuide
    labelValue: "0"
  }
  enableMode: ABTEM_REGISTER
}
rows {
  TestType: "59"
  GroupKey: "exp_new_selected_scene"
  ModuleKey: "new_selected_scene"
  OpenStatus: 1
  RegisterTime {
    seconds: 1750780800
  }
  channelId: "59000005"
  clientFilterLabel {
    labelType: ABTCFLT_WX_NewPlayerGuide
    labelValue: "0"
  }
  enableMode: ABTEM_REGISTER
}
rows {
  TestType: "60"
  GroupKey: "exp_new_face_7th"
  ModuleKey: "new_face_7th"
  OpenStatus: 1
  RegisterTime {
    seconds: 1752163200
  }
  channelId: "59000005"
  clientFilterLabel {
    labelType: ABTCFLT_WX_NewPlayerGuide
    labelValue: "0"
  }
  enableMode: ABTEM_REGISTER
}
rows {
  TestType: "61"
  GroupKey: "exp_newbie_first_plaza"
  ModuleKey: "newbie_first_plaza"
  OpenStatus: 1
}
