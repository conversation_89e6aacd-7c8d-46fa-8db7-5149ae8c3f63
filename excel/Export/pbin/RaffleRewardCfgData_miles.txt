com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_miles.xlsx sheet:奖励
rows {
  rewardId: 40000001
  poolId: 40000001
  name: "嫦娥"
  itemId: 403460
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40000002
  poolId: 40000001
  name: "兔倚秋夕"
  itemId: 620481
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000003
  poolId: 40000001
  name: "月色瑶华"
  itemId: 630332
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000004
  poolId: 40000001
  name: "晶玉双蝶"
  itemId: 630273
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000005
  poolId: 40000001
  name: "皓月使者 丹桂"
  itemId: 403570
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
  extraItemIds: 405096
  extraItemNums: 1
}
rows {
  rewardId: 40000006
  poolId: 40000001
  name: "月宫灵兔 锦儿"
  itemId: 403580
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000007
  poolId: 40000001
  name: "玉叶琼枝"
  itemId: 610232
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000008
  poolId: 40000001
  name: "时光花语"
  itemId: 620475
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000009
  poolId: 40000001
  name: "金桂玉冠"
  itemId: 630329
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000010
  poolId: 40000001
  name: "猎小鹰"
  itemId: 403550
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000011
  poolId: 40000001
  name: "皎月吹纱"
  itemId: 840175
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 40000012
  poolId: 40000001
  name: "臻藏代币*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 40000013
  poolId: 40000001
  name: "臻藏代币*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 40000014
  poolId: 40000001
  name: "臻藏代币*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000015
  poolId: 40000001
  name: "臻藏代币*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 40000016
  poolId: 40000001
  name: "臻藏代币*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 40000017
  poolId: 40000001
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000018
  poolId: 40000001
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000019
  poolId: 40000001
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000020
  poolId: 40000001
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000021
  poolId: 40000001
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000022
  poolId: 40000001
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000023
  poolId: 40000001
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 2
}
rows {
  rewardId: 40000024
  poolId: 40000002
  name: "chiikawa-吉伊"
  itemId: 402870
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000025
  poolId: 40000002
  name: "chiikawa-小八"
  itemId: 402880
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000026
  poolId: 40000002
  name: "chiikawa-乌萨奇"
  itemId: 402890
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000027
  poolId: 40000002
  name: "吉伊"
  itemId: 410730
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000028
  poolId: 40000002
  name: "自拍神喵"
  itemId: 640160
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000029
  poolId: 40000002
  name: "乌萨奇"
  itemId: 410750
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000030
  poolId: 40000002
  name: "幸运小蛙"
  itemId: 630417
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000031
  poolId: 40000002
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 1
  limit: 13
}
rows {
  rewardId: 40000032
  poolId: 40000002
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 3
  limit: 13
}
rows {
  rewardId: 40000033
  poolId: 40000002
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 13
}
rows {
  rewardId: 40000034
  poolId: 40000002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 3
  limit: 13
}
rows {
  rewardId: 40000035
  poolId: 40000002
  name: "除草手册"
  itemId: 620628
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000036
  poolId: 40000002
  name: "树叶触角"
  itemId: 630414
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000037
  poolId: 40000002
  name: "表情自选礼盒"
  itemId: 330087
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 3
}
rows {
  rewardId: 40000038
  poolId: 40000002
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 3
  limit: 13
}
rows {
  rewardId: 40000039
  poolId: 40000002
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 13
}
rows {
  rewardId: 40000040
  poolId: 40000003
  name: "山河扇卷"
  itemId: 620478
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000041
  poolId: 40000003
  name: "翠羽幻瞳"
  itemId: 610181
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000042
  poolId: 40000003
  name: "星际电波"
  itemId: 630287
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000043
  poolId: 40000003
  name: "动态表情-纸醉金迷"
  itemId: 710255
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 40000044
  poolId: 40000003
  name: "紫色动作-望明月"
  itemId: 720769
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000045
  poolId: 40000003
  name: "表情-不可以"
  itemId: 710259
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000046
  poolId: 40000003
  name: "美味晨光上装"
  itemId: 510214
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000047
  poolId: 40000003
  name: "美味晨光夏装"
  itemId: 520151
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000048
  poolId: 40000003
  name: "美味晨光手套"
  itemId: 530127
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000049
  poolId: 400000040
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000050
  poolId: 400000040
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000051
  poolId: 400000040
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000052
  poolId: 400000040
  name: "进度4"
  itemId: 310110
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000053
  poolId: 400000040
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000054
  poolId: 400000040
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000055
  poolId: 400000040
  name: "进度7"
  itemId: 310111
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000056
  poolId: 400000040
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000057
  poolId: 400000040
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000058
  poolId: 400000040
  name: "进度10"
  itemId: 310112
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000059
  poolId: 400000040
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 40000060
  poolId: 400000041
  name: "星之子·龙霄"
  itemId: 730007
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40000061
  poolId: 400000041
  name: "背饰-繁花轻摇"
  itemId: 620443
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000062
  poolId: 400000041
  name: "头饰-热狗派对"
  itemId: 630336
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000063
  poolId: 400000041
  name: "面饰-飞翼视界"
  itemId: 610236
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000064
  poolId: 400000041
  name: "动态头像框-青龙戏珠"
  itemId: 840178
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40000065
  poolId: 400000041
  name: "碧玺龙纹"
  itemId: 820120
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40000066
  poolId: 400000041
  name: "表情-打电话"
  itemId: 710277
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000067
  poolId: 400000041
  name: "表情-贴贴"
  itemId: 710274
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000068
  poolId: 400000041
  name: "表情-汗如雨下"
  itemId: 710276
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000069
  poolId: 400000041
  name: "动作-葱葱舞"
  itemId: 720747
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000070
  poolId: 400000041
  name: "动作-倒地不起"
  itemId: 720770
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000071
  poolId: 400000041
  name: "动作-单人华尔兹"
  itemId: 720772
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000072
  poolId: 400000041
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000073
  poolId: 400000041
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000074
  poolId: 400000041
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000075
  poolId: 400000041
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000076
  poolId: 400000041
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000077
  poolId: 400000041
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000078
  poolId: 400000041
  name: "表情-惊呆了"
  itemId: 711045
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000079
  poolId: 400000041
  name: "表情-心有不甘"
  itemId: 711046
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000080
  poolId: 400000041
  name: "表情-欢呼"
  itemId: 711047
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000081
  poolId: 400000041
  name: "动作-哈哈大王"
  itemId: 720693
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000082
  poolId: 400000041
  name: "动作-浪起来吧"
  itemId: 720706
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000083
  poolId: 400000041
  name: "动作-硬核倒地"
  itemId: 720707
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000084
  poolId: 400000041
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000085
  poolId: 400000041
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000086
  poolId: 400000041
  name: "开心一刻上装"
  itemId: 510208
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40000087
  poolId: 400000041
  name: "开心一刻下装"
  itemId: 520147
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40000088
  poolId: 400000041
  name: "开心一刻手套"
  itemId: 530124
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40000089
  poolId: 400000041
  name: "披萨上装"
  itemId: 510248
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000090
  poolId: 400000041
  name: "披萨下装"
  itemId: 520174
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000091
  poolId: 400000041
  name: "披萨手套"
  itemId: 530150
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000092
  poolId: 400000041
  name: "波波奶茶上装"
  itemId: 510249
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000093
  poolId: 400000041
  name: "波波奶茶下装"
  itemId: 520175
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000094
  poolId: 400000041
  name: "波波奶茶手套"
  itemId: 530151
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000095
  poolId: 400000041
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000096
  poolId: 400000041
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000097
  poolId: 400000041
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000098
  poolId: 400000041
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000099
  poolId: 40000005
  name: "开心超人"
  itemId: 403980
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000100
  poolId: 40000005
  name: "迷你开心超人包"
  itemId: 620559
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000101
  poolId: 40000005
  name: "开心超人头像"
  itemId: 860118
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000102
  poolId: 40000005
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000103
  poolId: 40000005
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000104
  poolId: 40000005
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000105
  poolId: 40000005
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000106
  poolId: 40000005
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000107
  poolId: 40000005
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000108
  poolId: 40000005
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000109
  poolId: 40000005
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000110
  poolId: 40000005
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000111
  poolId: 40000006
  name: "甜心超人"
  itemId: 403990
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000112
  poolId: 40000006
  name: "甜心超人天使之翼"
  itemId: 620560
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000113
  poolId: 40000006
  name: "甜心超人头像"
  itemId: 860119
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000114
  poolId: 40000006
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000115
  poolId: 40000006
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000116
  poolId: 40000006
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000117
  poolId: 40000006
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000118
  poolId: 40000006
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000119
  poolId: 40000006
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000120
  poolId: 40000006
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000121
  poolId: 40000006
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000122
  poolId: 40000006
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000123
  poolId: 40000007
  name: "小心超人"
  itemId: 404000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000124
  poolId: 40000007
  name: "小心超人激光刃"
  itemId: 620561
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000125
  poolId: 40000007
  name: "小心超人头像"
  itemId: 860120
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000126
  poolId: 40000007
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000127
  poolId: 40000007
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000128
  poolId: 40000007
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000129
  poolId: 40000007
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000130
  poolId: 40000007
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000131
  poolId: 40000007
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000132
  poolId: 40000007
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000133
  poolId: 40000007
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000134
  poolId: 40000007
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 40000135
  poolId: 40000008
  name: "冰雪精灵"
  itemId: 404320
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40000136
  poolId: 40000008
  name: "迷雾之纱"
  itemId: 610284
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000137
  poolId: 40000008
  name: "海洋精灵"
  itemId: 620637
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000138
  poolId: 40000008
  name: "霜之韵律"
  itemId: 640084
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000139
  poolId: 40000008
  name: "大雪怪 伊拉"
  itemId: 404520
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000140
  poolId: 40000008
  name: "冰霜者 海沃"
  itemId: 404530
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000141
  poolId: 40000008
  name: "电焊面具"
  itemId: 610207
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000142
  poolId: 40000008
  name: "冰雪兔兔"
  itemId: 630423
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000143
  poolId: 40000008
  name: "法官锤子"
  itemId: 620434
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000144
  poolId: 40000008
  name: "可可豆"
  itemId: 402770
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000145
  poolId: 40000008
  name: "冰霜之华"
  itemId: 840213
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 40000146
  poolId: 40000008
  name: "臻藏代币*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 40000147
  poolId: 40000008
  name: "臻藏代币*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 40000148
  poolId: 40000008
  name: "臻藏代币*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000149
  poolId: 40000008
  name: "臻藏代币*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 40000150
  poolId: 40000008
  name: "臻藏代币*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 40000151
  poolId: 40000008
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000152
  poolId: 40000008
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000153
  poolId: 40000008
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000154
  poolId: 40000008
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000155
  poolId: 40000008
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000156
  poolId: 40000008
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 4
}
rows {
  rewardId: 40000157
  poolId: 40000008
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 2
}
rows {
  rewardId: 40000158
  poolId: 40000012
  name: "素雪千年 白落"
  itemId: 410100
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000159
  poolId: 40000012
  name: "灵蛇献瑞"
  itemId: 620752
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000160
  poolId: 40000012
  name: "白落头像"
  itemId: 860178
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000161
  poolId: 40000012
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000162
  poolId: 40000012
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000163
  poolId: 40000012
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000164
  poolId: 40000012
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000165
  poolId: 40000012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000166
  poolId: 40000012
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000167
  poolId: 40000013
  name: "碧水芙蓉 青颜"
  itemId: 410090
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000168
  poolId: 40000013
  name: "竹韵清风"
  itemId: 620740
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000169
  poolId: 40000013
  name: "青颜头像"
  itemId: 860177
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000170
  poolId: 40000013
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000171
  poolId: 40000013
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000172
  poolId: 40000013
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000173
  poolId: 40000013
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000174
  poolId: 40000013
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000175
  poolId: 40000013
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000209
  poolId: 40000010
  name: "幻光破界 菲朵"
  itemId: 404930
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000210
  poolId: 40000010
  name: "风车视界"
  itemId: 610306
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000211
  poolId: 40000010
  name: "菲朵头像"
  itemId: 860167
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000212
  poolId: 40000010
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000213
  poolId: 40000010
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000214
  poolId: 40000010
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000215
  poolId: 40000010
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000216
  poolId: 40000010
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000217
  poolId: 40000010
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000218
  poolId: 40000011
  name: "幻梦追光 费伊"
  itemId: 404920
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 40000219
  poolId: 40000011
  name: "多彩纽扣镜"
  itemId: 610303
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000220
  poolId: 40000011
  name: "费伊头像"
  itemId: 860166
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000221
  poolId: 40000011
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000222
  poolId: 40000011
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000223
  poolId: 40000011
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000224
  poolId: 40000011
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000225
  poolId: 40000011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000226
  poolId: 40000011
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 40000227
  poolId: 40000009
  name: "小金龙"
  itemId: 401430
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000228
  poolId: 40000009
  name: "浪漫星空"
  itemId: 845001
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000229
  poolId: 40000009
  name: "夜色烟火"
  itemId: 845003
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000230
  poolId: 40000009
  name: "夜晚游乐场"
  itemId: 845006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000231
  poolId: 40000009
  name: "丹枫秋意"
  itemId: 845007
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000232
  poolId: 40000009
  name: "绮梦灯（礼包）"
  itemId: 620134
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  extraItemIds: 725204
  extraItemNums: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000233
  poolId: 40000009
  name: "冰雪玫瑰"
  itemId: 620155
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000235
  poolId: 40000009
  name: "星愿币*120"
  itemId: 2
  itemNum: 120
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000236
  poolId: 40000009
  name: "幻梦币*120"
  itemId: 14
  itemNum: 120
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000237
  poolId: 40000009
  name: "星梦者"
  itemId: 400330
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000238
  poolId: 40000009
  name: "甜橙喵"
  itemId: 400450
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000239
  poolId: 40000009
  name: "双面天鹅 娜塔莉"
  itemId: 402200
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000240
  poolId: 40000009
  name: "玻璃精灵 格莉丝"
  itemId: 403590
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000241
  poolId: 40000009
  name: "海浪之翼"
  itemId: 620056
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000242
  poolId: 40000009
  name: "齐天大圣 孙悟空"
  itemId: 402220
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000243
  poolId: 40000009
  name: "金蝉子 唐三藏"
  itemId: 402970
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000244
  poolId: 40000009
  name: "天蓬元帅 猪悟能"
  itemId: 403320
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000245
  poolId: 40000009
  name: "欧米"
  itemId: 401090
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000246
  poolId: 40000009
  name: "枪火新星 闪电"
  itemId: 401700
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000247
  poolId: 40000009
  name: "星愿币*60"
  itemId: 2
  itemNum: 60
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000248
  poolId: 40000009
  name: "幻梦币*60"
  itemId: 14
  itemNum: 60
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000249
  poolId: 40000009
  name: "糖果果"
  itemId: 401800
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000250
  poolId: 40000009
  name: "大气探险家"
  itemId: 400250
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000251
  poolId: 40000009
  name: "西瓜奈奈"
  itemId: 402980
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000252
  poolId: 40000009
  name: "蒸汽可可"
  itemId: 403830
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000253
  poolId: 40000009
  name: "锦鲤扇子"
  itemId: 620141
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000254
  poolId: 40000009
  name: "青花茶盏"
  itemId: 630063
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000255
  poolId: 40000009
  name: "木芙蓉花"
  itemId: 610049
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000256
  poolId: 40000009
  name: "冰雪皇冠"
  itemId: 630081
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000257
  poolId: 40000009
  name: "一支玫瑰"
  itemId: 610052
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000258
  poolId: 40000009
  name: "甜橙能量汁"
  itemId: 620029
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000261
  poolId: 40000009
  name: "星愿币*12"
  itemId: 2
  itemNum: 12
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000262
  poolId: 40000009
  name: "幻梦币*12"
  itemId: 14
  itemNum: 12
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000263
  poolId: 40000009
  name: "绒绒草面饰"
  itemId: 610098
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000264
  poolId: 40000009
  name: "白八字胡"
  itemId: 610114
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000265
  poolId: 40000009
  name: "面饰小胡子*1"
  itemId: 610090
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000266
  poolId: 40000009
  name: "头饰星消息"
  itemId: 630102
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000267
  poolId: 40000009
  name: "破壳鸡"
  itemId: 630173
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000268
  poolId: 40000009
  name: "面饰先锋视角"
  itemId: 610125
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000269
  poolId: 40000009
  name: "头饰-无语气泡框"
  itemId: 630164
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000270
  poolId: 40000009
  name: "菜菜萌新"
  itemId: 630266
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000271
  poolId: 40000009
  name: "面饰-煤球面罩"
  itemId: 610191
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000272
  poolId: 40000009
  name: "好运来"
  itemId: 630290
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000273
  poolId: 40000009
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000274
  poolId: 40000009
  name: "幻梦币*2"
  itemId: 14
  itemNum: 2
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000275
  poolId: 400000140
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000276
  poolId: 400000140
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000277
  poolId: 400000140
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000278
  poolId: 400000140
  name: "进度4"
  itemId: 310104
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000279
  poolId: 400000140
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000280
  poolId: 400000140
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000281
  poolId: 400000140
  name: "进度7"
  itemId: 310105
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000282
  poolId: 400000140
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000283
  poolId: 400000140
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000284
  poolId: 400000140
  name: "进度10"
  itemId: 310106
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40000285
  poolId: 400000140
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 40000286
  poolId: 400000141
  name: "森之子·鲲宝"
  itemId: 730001
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40000287
  poolId: 400000141
  name: "背饰-魔方"
  itemId: 620169
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000288
  poolId: 400000141
  name: "魔法书"
  itemId: 620232
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000289
  poolId: 400000141
  name: "面饰-斗鸡眼眼镜"
  itemId: 610080
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000290
  poolId: 400000141
  name: "动态头像框-花海灵鲲"
  itemId: 840099
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40000291
  poolId: 400000141
  name: "漫溯花海"
  itemId: 820072
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40000292
  poolId: 400000141
  name: "表情-太牛了"
  itemId: 710128
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000293
  poolId: 400000141
  name: "表情-我可真帅"
  itemId: 710130
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000294
  poolId: 400000141
  name: "表情-真下饭"
  itemId: 710131
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000295
  poolId: 400000141
  name: "动作-翩翩少年"
  itemId: 720134
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000296
  poolId: 400000141
  name: "动作-化学实验"
  itemId: 720129
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000297
  poolId: 400000141
  name: "动作-手指转篮球"
  itemId: 720142
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000298
  poolId: 400000141
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000299
  poolId: 400000141
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000300
  poolId: 400000141
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000301
  poolId: 400000141
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000302
  poolId: 400000141
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000303
  poolId: 400000141
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000304
  poolId: 400000141
  name: "表情-剪刀手"
  itemId: 710178
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000305
  poolId: 400000141
  name: "表情-笑拉了家人们"
  itemId: 710132
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000306
  poolId: 400000141
  name: "表情-略略略"
  itemId: 710133
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000307
  poolId: 400000141
  name: "动作-摇手手"
  itemId: 720121
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000308
  poolId: 400000141
  name: "动作-猛男舞"
  itemId: 720123
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000309
  poolId: 400000141
  name: "动作-显摆显摆"
  itemId: 720126
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000310
  poolId: 400000141
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000311
  poolId: 400000141
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000312
  poolId: 400000141
  name: "家政服"
  itemId: 510102
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40000313
  poolId: 400000141
  name: "别来沾边"
  itemId: 510128
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000314
  poolId: 400000141
  name: "别来沾边"
  itemId: 520084
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000315
  poolId: 400000141
  name: "别来沾边"
  itemId: 530062
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000316
  poolId: 400000141
  name: "真的栓Q"
  itemId: 510129
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000317
  poolId: 400000141
  name: "真的栓Q"
  itemId: 520085
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000318
  poolId: 400000141
  name: "真的栓Q"
  itemId: 530063
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000319
  poolId: 400000141
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000320
  poolId: 400000141
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000321
  poolId: 400000141
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000322
  poolId: 400000141
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40000347
  poolId: 40000016
  name: "三彩逸士 青云"
  itemId: 403660
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
  extraItemIds: 405097
  extraItemNums: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000348
  poolId: 40000016
  name: "山河扇卷"
  itemId: 620478
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000349
  poolId: 40000016
  name: "手持物-兔影琼枝"
  itemId: 640048
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000350
  poolId: 40000016
  name: "星辰日晷"
  itemId: 740006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000351
  poolId: 40000016
  name: "冰雪玫瑰"
  itemId: 620155
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000352
  poolId: 40000016
  name: "夜色烟火"
  itemId: 845003
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000353
  poolId: 40000016
  name: "浪漫星空"
  itemId: 845001
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000354
  poolId: 40000016
  name: "星愿币*120"
  itemId: 2
  itemNum: 120
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000355
  poolId: 40000016
  name: "幸运币*40"
  itemId: 3
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 1
  sortName: "SS级"
}
rows {
  rewardId: 40000356
  poolId: 40000016
  name: "双面天鹅 娜塔莉"
  itemId: 402200
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000357
  poolId: 40000016
  name: "星梦者"
  itemId: 400330
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000358
  poolId: 40000016
  name: "露水精灵 嘟嘟"
  itemId: 403820
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000359
  poolId: 40000016
  name: "竹韵隐侠 萌萌"
  itemId: 403810
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000360
  poolId: 40000016
  name: "human"
  itemId: 400990
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000361
  poolId: 40000016
  name: "时装小橘子"
  itemId: 401780
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000362
  poolId: 40000016
  name: "染青烟"
  itemId: 640053
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000363
  poolId: 40000016
  name: "洇重霄"
  itemId: 640060
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000364
  poolId: 40000016
  name: "绘长歌"
  itemId: 620485
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000365
  poolId: 40000016
  name: "见龙吟"
  itemId: 630351
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000366
  poolId: 40000016
  name: "天鹅之心"
  itemId: 630195
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000367
  poolId: 40000016
  name: "星愿币*60"
  itemId: 2
  itemNum: 60
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000368
  poolId: 40000016
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 2
  sortName: "S级"
}
rows {
  rewardId: 40000369
  poolId: 40000016
  name: "木伊伊"
  itemId: 402230
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000370
  poolId: 40000016
  name: "红孩儿"
  itemId: 402450
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000371
  poolId: 40000016
  name: "牛魔王"
  itemId: 403140
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000372
  poolId: 40000016
  name: "铁扇公主"
  itemId: 403150
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000373
  poolId: 40000016
  name: "星灿灿"
  itemId: 402640
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000374
  poolId: 40000016
  name: "月缘缘"
  itemId: 402650
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000375
  poolId: 40000016
  name: "凤果果"
  itemId: 403530
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000376
  poolId: 40000016
  name: "星月之轮"
  itemId: 620216
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000377
  poolId: 40000016
  name: "雀翎羽"
  itemId: 630190
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000378
  poolId: 40000016
  name: "开屏眼罩"
  itemId: 610152
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000379
  poolId: 40000016
  name: "花影之刃"
  itemId: 620500
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000380
  poolId: 40000016
  name: "翠羽幻瞳"
  itemId: 610181
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000381
  poolId: 40000016
  name: "星际电波"
  itemId: 630287
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000382
  poolId: 40000016
  name: "星愿币*12"
  itemId: 2
  itemNum: 12
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000383
  poolId: 40000016
  name: "幸运币*4"
  itemId: 3
  itemNum: 4
  groupId: 3
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 3
  sortName: "A级"
}
rows {
  rewardId: 40000384
  poolId: 40000016
  name: "绒绒草面饰"
  itemId: 610098
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000385
  poolId: 40000016
  name: "白八字胡"
  itemId: 610114
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000386
  poolId: 40000016
  name: "面饰小胡子*1"
  itemId: 610090
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000387
  poolId: 40000016
  name: "头饰星消息"
  itemId: 630102
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000388
  poolId: 40000016
  name: "破壳鸡"
  itemId: 630173
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000389
  poolId: 40000016
  name: "面饰先锋视角"
  itemId: 610125
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000390
  poolId: 40000016
  name: "头饰-无语气泡框"
  itemId: 630164
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000391
  poolId: 40000016
  name: "好运来"
  itemId: 630290
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000392
  poolId: 40000016
  name: "面饰-樱桃眼镜"
  itemId: 610172
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000393
  poolId: 40000016
  name: "独眼小宝"
  itemId: 610214
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000394
  poolId: 40000016
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40000395
  poolId: 40000016
  name: "幸运币*2"
  itemId: 3
  itemNum: 2
  groupId: 4
  weight: 10
  limit: 1
  inGroupChooseEnhance {
    mustChoose: true
  }
  sortId: 4
  sortName: "B级"
}
rows {
  rewardId: 40001501
  poolId: 400000150
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001502
  poolId: 400000150
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001503
  poolId: 400000150
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001504
  poolId: 400000150
  name: "进度4"
  itemId: 310110
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001505
  poolId: 400000150
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001506
  poolId: 400000150
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001507
  poolId: 400000150
  name: "进度7"
  itemId: 310111
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001508
  poolId: 400000150
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001509
  poolId: 400000150
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001510
  poolId: 400000150
  name: "进度10"
  itemId: 310112
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 40001511
  poolId: 400000150
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 40001512
  poolId: 400000151
  name: "星之子·龙霄"
  itemId: 730007
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40001513
  poolId: 400000151
  name: "背饰-繁花轻摇"
  itemId: 620443
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40001514
  poolId: 400000151
  name: "头饰-热狗派对"
  itemId: 630336
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40001515
  poolId: 400000151
  name: "面饰-飞翼视界"
  itemId: 610236
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 40001516
  poolId: 400000151
  name: "动态头像框-青龙戏珠"
  itemId: 840178
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40001517
  poolId: 400000151
  name: "碧玺龙纹"
  itemId: 820120
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 40001518
  poolId: 400000151
  name: "表情-打电话"
  itemId: 710277
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001519
  poolId: 400000151
  name: "表情-贴贴"
  itemId: 710274
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001520
  poolId: 400000151
  name: "表情-汗如雨下"
  itemId: 710276
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001521
  poolId: 400000151
  name: "动作-葱葱舞"
  itemId: 720747
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001522
  poolId: 400000151
  name: "动作-倒地不起"
  itemId: 720770
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001523
  poolId: 400000151
  name: "动作-单人华尔兹"
  itemId: 720772
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 40001524
  poolId: 400000151
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001525
  poolId: 400000151
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001526
  poolId: 400000151
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001527
  poolId: 400000151
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001528
  poolId: 400000151
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001529
  poolId: 400000151
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001530
  poolId: 400000151
  name: "表情-惊呆了"
  itemId: 711045
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001531
  poolId: 400000151
  name: "表情-心有不甘"
  itemId: 711046
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001532
  poolId: 400000151
  name: "表情-欢呼"
  itemId: 711047
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001533
  poolId: 400000151
  name: "动作-哈哈大王"
  itemId: 720693
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001534
  poolId: 400000151
  name: "动作-浪起来吧"
  itemId: 720706
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001535
  poolId: 400000151
  name: "动作-硬核倒地"
  itemId: 720707
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001536
  poolId: 400000151
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001537
  poolId: 400000151
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001538
  poolId: 400000151
  name: "开心一刻上装"
  itemId: 510208
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40001539
  poolId: 400000151
  name: "开心一刻下装"
  itemId: 520147
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40001540
  poolId: 400000151
  name: "开心一刻手套"
  itemId: 530124
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 40001541
  poolId: 400000151
  name: "披萨上装"
  itemId: 510248
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001542
  poolId: 400000151
  name: "披萨下装"
  itemId: 520174
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001543
  poolId: 400000151
  name: "披萨手套"
  itemId: 530150
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001544
  poolId: 400000151
  name: "波波奶茶上装"
  itemId: 510249
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001545
  poolId: 400000151
  name: "波波奶茶下装"
  itemId: 520175
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001546
  poolId: 400000151
  name: "波波奶茶手套"
  itemId: 530151
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001547
  poolId: 400000151
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001548
  poolId: 400000151
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001549
  poolId: 400000151
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 40001550
  poolId: 400000151
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
