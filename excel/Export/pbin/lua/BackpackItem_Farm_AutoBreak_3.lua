--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_农场.xlsx: 农场道具

local data = {
[219420] = {
id = 219420,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "冒险眼镜",
desc = "（星宝农场宠物装饰）可以装扮你的宠物。",
icon = "Icon_Farm_Head_016_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SK_Farm_Head_016_Comm",
modelType = 2,
idleAnim = "SM_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219618] = {
id = 219618,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "魔法颈饰",
desc = "（星宝农场宠物装饰）可以装扮你的宠物。",
icon = "Icon_Farm_NeckSuit_005_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SK_Farm_NeckSuit_005_Comm",
modelType = 2,
idleAnim = "SM_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219816] = {
id = 219816,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "魔法身体",
desc = "（星宝农场宠物装饰）可以装扮你的宠物。",
icon = "Icon_Farm_BodySuit_005_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SK_Farm_BodySuit_005_Comm",
modelType = 2,
idleAnim = "SM_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219619] = {
id = 219619,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "奶牛颈饰",
desc = "（星宝农场宠物装饰）可以装扮你的宠物。",
icon = "Icon_Farm_Neck_012_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SK_Farm_Neck_012_Comm",
modelType = 2,
idleAnim = "SM_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219204] = {
id = 219204,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "小曲奇",
desc = "（星宝农场宠物）可以保护农场产物，重复获得会转化为云朵币。",
icon = "Icon_Farm_Poodle_002_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SK_Farm_Poodle_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Poodle_002_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[218210] = {
id = 218210,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "星露花台",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_CastleVegetableStall_001_A_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SM_Farm_BuComm_CastleVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 225,
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "0,0"
},
[218211] = {
id = 218211,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "天穹彩虹小店",
desc = "（星宝农场动物小铺装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_CastleXuMu_001_A_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SM_Farm_BuComm_CastleXuMu_001_Comm",
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 225,
buff = "动物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "0,0"
},
[218212] = {
id = 218212,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "灵泉圣亭",
desc = "（星宝农场水产摊装饰）可以降低鱼饵价格。",
icon = "CDN:Icon_Farm_BuComm_CastleFishStall_001_A_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 225,
buff = "鱼饵价格降低",
buffValue = "-2%",
buffViewOffset = "0,0"
},
[218003] = {
id = 218003,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "人才推荐信",
desc = "（农场餐厅道具）在农场餐厅高级人才市场处使用，可以用来刷新和雇佣高级员工",
icon = "CDN:Icon_Farm_PA_Cook_Talent_001_Comm",
getWay = "活动",
useType = "IUTO_SendToFarm",
bagType = "IBT_Farm"
},
[218213] = {
id = 218213,
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "云谣小亭",
icon = "CDN:Icon_Farm_BuComm_CastleDogHouse_001_A_Comm",
useType = "IUTO_SendToFarm",
resourceConf = {
model = "SM_Farm_BuComm_CastleDogHouse_001_Comm",
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = "0,0"
}
}

local mt = {
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data