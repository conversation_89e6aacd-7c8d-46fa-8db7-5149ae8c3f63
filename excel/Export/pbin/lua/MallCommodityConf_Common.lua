--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_兑换.xlsx: 兑换商品

local v0 = 4

local v1 = 30

local v2 = 34

local v3 = 38

local v4 = 104

local v5 = 107

local v6 = 140

local v7 = 162

local v8 = 153

local v9 = 145

local v10 = 152

local v11 = 193

local v12 = "闪耀烟花棒"

local v13 = "甜蜜初心"

local v14 = 1

local v15 = 7

local v16 = 101

local v17 = 203

local v18 = 205

local v19 = 3159

local v20 = 3134

local v21 = 211

local v22 = 216

local v23 = 2072

local v24 = 3407

local v25 = 218

local v26 = 10

local v27 = 20

local v28 = 60

local v29 = 300

local v30 = 45

local v31 = 5

local v32 = 50

local v33 = 40

local v34 = 3

local v35 = 150

local v36 = 100

local v37 = 80

local v38 = 180

local v39 = 240

local v40 = 520

local v41 = "MCL_None"

local v42 = 0

local v43 = {
200014
}

local v44 = {
725001
}

local v45 = {
725005
}

local v46 = {
seconds = 1702569600
}

local v47 = {
seconds = 1706198400
}

local v48 = {
seconds = 1710432000
}

local v49 = {
seconds = 1714060800
}

local v50 = {
seconds = 1717689600
}

local v51 = {
seconds = 1711641600
}

local v52 = {
seconds = 1706803200
}

local v53 = {
seconds = 1701878400
}

local v54 = {
seconds = 1715270400
}

local v55 = {
seconds = 1718899200
}

local v56 = {
seconds = 1722528000
}

local v57 = {
seconds = 1726761600
}

local v58 = {
seconds = 1730390400
}

local v59 = {
seconds = 1734019200
}

local v60 = {
seconds = 1738252800
}

local v61 = {
seconds = 1741795200
}

local v62 = {
seconds = 1747324800
}

local v63 = {
seconds = 1737043200
}

local v64 = {
seconds = 1717084800
}

local v65 = {
seconds = 1728316800
}

local v66 = {
seconds = 1706198399
}

local v67 = {
seconds = 1710431999
}

local v68 = {
seconds = 1714060799
}

local v69 = {
seconds = 1717689599
}

local v70 = {
seconds = 1721318399
}

local v71 = {
seconds = 1750953599
}

local v72 = 9999

local v73 = 99

local v74 = 9

local v75 = 8

local v76 = 6

local v77 = 2

local v78 = 160

local v79 = "本期奖励"

local v80 = "往期时装"

local v81 = "往期配饰"

local data = {
[1] = {
mallId = 17,
commodityId = 1,
commodityName = "星钻买星愿币",
coinType = 1,
price = 10,
limitType = v41,
gender = 0,
itemIds = {
310005
}
},
[2] = {
mallId = 17,
commodityId = 2,
commodityName = "星钻买元梦币",
coinType = 1,
price = 10,
limitType = v41,
gender = 0,
itemIds = {
310006
}
},
[3] = {
mallId = 17,
commodityId = 3,
commodityName = "星钻兑换云朵币",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
6
}
},
[11] = {
mallId = 17,
commodityId = 11,
commodityName = "星钻兑换乐之叶",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
501
},
itemNums = {
10
}
},
[12] = {
mallId = 17,
commodityId = 12,
commodityName = "星钻兑换梦之叶",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
502
},
itemNums = {
10
}
},
[13] = {
mallId = 17,
commodityId = 13,
commodityName = "云朵币兑换星光烟花",
coinType = 6,
price = 30,
limitType = v41,
beginTime = {
seconds = 1707235200
},
endTime = {
seconds = 1708793999
},
gender = 0,
itemIds = {
1010
}
},
[14] = {
mallId = 17,
commodityId = 14,
commodityName = "星钻买幻梦币",
coinType = 1,
price = 10,
limitType = v41,
gender = 0,
itemIds = {
310019
}
},
[21] = {
mallId = 80,
commodityId = 21,
commodityName = "星钻兑换水晶",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
601
},
itemNums = {
2
},
disableNtf = true
},
[22] = {
mallId = 80,
commodityId = 22,
commodityName = "星钻兑换兽人币",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
603
},
disableNtf = true
},
[23] = {
mallId = 80,
commodityId = 23,
commodityName = "兽人币兑换皇冠",
coinType = 603,
price = 20,
limitType = v41,
gender = 0,
itemIds = {
602
},
disableNtf = true
},
[31] = {
mallId = 80,
commodityId = 31,
commodityName = "星钻兑换元宝",
coinType = 1,
price = 1,
limitType = v41,
gender = 0,
itemIds = {
242005
}
},
[32] = {
mallId = 80,
commodityId = 32,
commodityName = "COC月卡",
coinType = 1,
price = 298,
limitType = v41,
gender = 0,
itemIds = {
242006
}
},
[33] = {
mallId = 80,
commodityId = 33,
commodityName = "coc加速资源收集-金币",
coinType = 1,
price = 60,
limitType = v41,
gender = 0,
itemIds = {
242003
}
},
[34] = {
mallId = 80,
commodityId = 34,
commodityName = "coc加速资源收集-圣水",
coinType = 1,
price = 60,
limitType = v41,
gender = 0,
itemIds = {
242004
}
},
[35] = {
mallId = 80,
commodityId = 35,
commodityName = "coc加速训练",
coinType = 1,
price = 30,
limitType = v41,
gender = 0,
itemIds = {
242007
}
},
[36] = {
mallId = 80,
commodityId = 36,
commodityName = "coc村民解锁1",
coinType = 1,
price = 300,
limitType = v41,
gender = 0,
itemIds = {
242008
}
},
[37] = {
mallId = 80,
commodityId = 37,
commodityName = "coc村民解锁2",
coinType = 1,
price = 680,
limitType = v41,
gender = 0,
itemIds = {
242009
}
},
[38] = {
mallId = 80,
commodityId = 38,
commodityName = "coc村民解锁3",
coinType = 1,
price = 1280,
limitType = v41,
gender = 0,
itemIds = {
242010
}
},
[39] = {
mallId = 80,
commodityId = 39,
commodityName = "coc建造工人小屋1",
coinType = 242005,
price = 500,
limitType = v41,
gender = 0,
itemIds = {
242011
}
},
[40] = {
mallId = 80,
commodityId = 40,
commodityName = "coc建造工人小屋2",
coinType = 242005,
price = 1000,
limitType = v41,
gender = 0,
itemIds = {
242013
}
},
[41] = {
mallId = 80,
commodityId = 41,
commodityName = "coc建造工人小屋3",
coinType = 242005,
price = 2000,
limitType = v41,
gender = 0,
itemIds = {
242014
}
},
[101] = {
mallId = 4,
commodityId = 101,
commodityName = "梨小棠",
coinType = 7,
price = 130,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
400680
}
},
[102] = {
mallId = 4,
commodityId = 102,
commodityName = "面饰-猫爪眼镜",
coinType = 7,
price = 85,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
610001
}
},
[103] = {
mallId = 4,
commodityId = 103,
commodityName = "动作-哈喽",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
720045
}
},
[104] = {
mallId = 4,
commodityId = 104,
commodityName = "动作-挥手",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
720014
}
},
[105] = {
mallId = 4,
commodityId = 105,
commodityName = "表情-赠花",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
710005
}
},
[106] = {
mallId = 4,
commodityId = 106,
commodityName = "表情-太酷啦",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
710034
}
},
[107] = {
mallId = 4,
commodityId = 107,
commodityName = "青柠气泡昵称框*1（id）",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
820002
}
},
[108] = {
mallId = 4,
commodityId = 108,
commodityName = "甜蜜初心（5点）",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = v43
},
[109] = {
mallId = 4,
commodityId = 109,
commodityName = "心心糖果（10点）",
coinType = 7,
price = 7,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
200015
}
},
[110] = {
mallId = 4,
commodityId = 110,
commodityName = "心心宝瓶",
coinType = 7,
price = 10,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
200016
}
},
[111] = {
mallId = 4,
commodityId = 111,
commodityName = "心心蜜罐",
coinType = 7,
price = 15,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
200017
}
},
[112] = {
mallId = 4,
commodityId = 112,
commodityName = "泡泡枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
725101
}
},
[113] = {
mallId = 4,
commodityId = 113,
commodityName = "钞票枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
725102
}
},
[114] = {
mallId = 4,
commodityId = 114,
commodityName = "花瓣球",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
725202
}
},
[115] = {
mallId = 4,
commodityId = 115,
commodityName = v12,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = v44
},
[116] = {
mallId = 4,
commodityId = 116,
commodityName = "璀璨烟花棒",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
725002
}
},
[117] = {
mallId = 4,
commodityId = 117,
commodityName = "星光碎片",
coinType = 6,
price = 50,
limitNum = 20,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
7
}
},
[121] = {
mallId = 4,
commodityId = 121,
commodityName = "冷艳高冷暗卫",
coinType = 7,
price = 130,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
401230
}
},
[122] = {
mallId = 4,
commodityId = 122,
commodityName = "面饰-绷带井字纱布",
coinType = 7,
price = 85,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
610059
}
},
[123] = {
mallId = 4,
commodityId = 123,
commodityName = "动作-单手俯卧撑",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
720067
}
},
[124] = {
mallId = 4,
commodityId = 124,
commodityName = "动作-加油",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
720068
}
},
[125] = {
mallId = 4,
commodityId = 125,
commodityName = "表情-寄刀片",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
710006
}
},
[126] = {
mallId = 4,
commodityId = 126,
commodityName = "表情-奸笑",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
710046
}
},
[127] = {
mallId = 4,
commodityId = 127,
commodityName = "冬日国风-昵称框",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
820028
}
},
[128] = {
mallId = 4,
commodityId = 128,
commodityName = "甜蜜初心（5点）",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = v43
},
[129] = {
mallId = 4,
commodityId = 129,
commodityName = "心心糖果（10点）",
coinType = 7,
price = 7,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
200015
}
},
[130] = {
mallId = 4,
commodityId = 130,
commodityName = "心心宝瓶",
coinType = 7,
price = 10,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
200016
}
},
[131] = {
mallId = 4,
commodityId = 131,
commodityName = "心心蜜罐",
coinType = 7,
price = 15,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
200017
}
},
[132] = {
mallId = 4,
commodityId = 132,
commodityName = "泡泡枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
725101
}
},
[133] = {
mallId = 4,
commodityId = 133,
commodityName = "钞票枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
725102
}
},
[134] = {
mallId = 4,
commodityId = 134,
commodityName = "花瓣球",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
725202
}
},
[135] = {
mallId = 4,
commodityId = 135,
commodityName = v12,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = v44
},
[136] = {
mallId = 4,
commodityId = 136,
commodityName = "璀璨烟花棒",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
725002
}
},
[137] = {
mallId = 4,
commodityId = 137,
commodityName = "星光碎片",
coinType = 6,
price = 50,
limitNum = 20,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
7
}
},
[141] = {
mallId = 4,
commodityId = 141,
commodityName = "羊彬彬",
coinType = 7,
price = 130,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
400830
}
},
[142] = {
mallId = 4,
commodityId = 142,
commodityName = "头饰—江湖过客",
coinType = 7,
price = 85,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
610092
}
},
[143] = {
mallId = 4,
commodityId = 143,
commodityName = "动作—这样不好",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
720091
}
},
[144] = {
mallId = 4,
commodityId = 144,
commodityName = "动作—啦啦队舞",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
720090
}
},
[145] = {
mallId = 4,
commodityId = 145,
commodityName = "表情—星宝无语",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
710095
}
},
[146] = {
mallId = 4,
commodityId = 146,
commodityName = "表情—默默流泪",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
710081
}
},
[147] = {
mallId = 4,
commodityId = 147,
commodityName = "春日头像框",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
840058
}
},
[148] = {
mallId = 4,
commodityId = 148,
commodityName = v13,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = v43
},
[149] = {
mallId = 4,
commodityId = 149,
commodityName = "心心糖果",
coinType = 7,
price = 7,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
200015
}
},
[150] = {
mallId = 4,
commodityId = 150,
commodityName = "心心宝瓶",
coinType = 7,
price = 10,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
200016
}
},
[151] = {
mallId = 4,
commodityId = 151,
commodityName = "心心蜜罐",
coinType = 7,
price = 15,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
200017
}
},
[152] = {
mallId = 4,
commodityId = 152,
commodityName = "泡泡枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
725101
}
},
[153] = {
mallId = 4,
commodityId = 153,
commodityName = "钞票枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
725102
}
},
[154] = {
mallId = 4,
commodityId = 154,
commodityName = "花瓣球",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
725202
}
},
[155] = {
mallId = 4,
commodityId = 155,
commodityName = v12,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = v44
},
[156] = {
mallId = 4,
commodityId = 156,
commodityName = "璀璨烟花棒",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
725002
}
},
[157] = {
mallId = 4,
commodityId = 157,
commodityName = "星光碎片",
coinType = 6,
price = 50,
limitNum = 20,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
7
}
},
[161] = {
mallId = 4,
commodityId = 161,
commodityName = "符禄禄",
coinType = 7,
price = 130,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
401772
}
},
[162] = {
mallId = 4,
commodityId = 162,
commodityName = "星光炸弹",
coinType = 7,
price = 85,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
630133
}
},
[163] = {
mallId = 4,
commodityId = 163,
commodityName = "广播体操一",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
720159
}
},
[164] = {
mallId = 4,
commodityId = 164,
commodityName = "广播体操二",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
720160
}
},
[165] = {
mallId = 4,
commodityId = 165,
commodityName = "听你的",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
710166
}
},
[166] = {
mallId = 4,
commodityId = 166,
commodityName = "好气啊",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
710167
}
},
[167] = {
mallId = 4,
commodityId = 167,
commodityName = "闪耀梦想",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
840096
}
},
[168] = {
mallId = 4,
commodityId = 168,
commodityName = v13,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = v43
},
[169] = {
mallId = 4,
commodityId = 169,
commodityName = "心心糖果",
coinType = 7,
price = 7,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
200015
}
},
[170] = {
mallId = 4,
commodityId = 170,
commodityName = "心心宝瓶",
coinType = 7,
price = 10,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
200016
}
},
[171] = {
mallId = 4,
commodityId = 171,
commodityName = "心心蜜罐",
coinType = 7,
price = 15,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
200017
}
},
[172] = {
mallId = 4,
commodityId = 172,
commodityName = "泡泡枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
725101
}
},
[173] = {
mallId = 4,
commodityId = 173,
commodityName = "钞票枪",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
725102
}
},
[174] = {
mallId = 4,
commodityId = 174,
commodityName = "花瓣球",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
725202
}
},
[175] = {
mallId = 4,
commodityId = 175,
commodityName = v12,
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = v44
},
[176] = {
mallId = 4,
commodityId = 176,
commodityName = "璀璨烟花棒",
coinType = 7,
price = 5,
limitNum = 9999,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
725002
}
},
[177] = {
mallId = 4,
commodityId = 177,
commodityName = "星光碎片",
coinType = 6,
price = 50,
limitNum = 20,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
7
}
},
[181] = {
mallId = 4,
commodityId = 181,
commodityName = "梵小高",
coinType = 7,
price = 130,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
401790
}
},
[182] = {
mallId = 4,
commodityId = 182,
commodityName = "彩虹伞",
coinType = 7,
price = 85,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
630137
}
},
[183] = {
mallId = 4,
commodityId = 183,
commodityName = "再说一次",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
710241
}
},
[184] = {
mallId = 4,
commodityId = 184,
commodityName = "婉拒了哈",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
710243
}
},
[185] = {
mallId = 4,
commodityId = 185,
commodityName = "肩部运动",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720161
}
},
[186] = {
mallId = 4,
commodityId = 186,
commodityName = "体侧运动",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720162
}
},
[187] = {
mallId = 4,
commodityId = 187,
commodityName = "夏日海歌头像框",
coinType = 7,
price = 45,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
820081
}
},
[188] = {
mallId = 4,
commodityId = 188,
commodityName = v13,
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = v43
},
[189] = {
mallId = 4,
commodityId = 189,
commodityName = "心心糖果",
coinType = 7,
price = 7,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
200015
}
},
[190] = {
mallId = 4,
commodityId = 190,
commodityName = "心心宝瓶",
coinType = 7,
price = 10,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
200016
}
},
[191] = {
mallId = 4,
commodityId = 191,
commodityName = "心心蜜罐",
coinType = 7,
price = 15,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
200017
}
},
[192] = {
mallId = 4,
commodityId = 192,
commodityName = "泡泡枪",
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
725101
}
},
[193] = {
mallId = 4,
commodityId = 193,
commodityName = "钞票枪",
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
725102
}
},
[194] = {
mallId = 4,
commodityId = 194,
commodityName = "花瓣球",
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
725202
}
},
[195] = {
mallId = 4,
commodityId = 195,
commodityName = v12,
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = v44
},
[196] = {
mallId = 4,
commodityId = 196,
commodityName = "璀璨烟花棒",
coinType = 7,
price = 5,
limitNum = 99,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
725002
}
},
[197] = {
mallId = 4,
commodityId = 197,
commodityName = "星光碎片",
coinType = 6,
price = 50,
limitNum = 20,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
7
}
},
[4961] = {
commodityId = 4961,
commodityName = "森小野",
price = 60,
limitNum = 1,
beginTime = v51,
order = 10,
itemIds = {
401590
}
},
[4962] = {
commodityId = 4962,
commodityName = "折纸风车",
price = 40,
limitNum = 1,
beginTime = v51,
order = 32,
itemIds = {
620200
}
},
[4963] = {
commodityId = 4963,
commodityName = "雪豹眼镜",
price = 40,
limitNum = 1,
beginTime = v51,
order = 33,
itemIds = {
610062
}
},
[4964] = {
commodityId = 4964,
commodityName = "拼贴风尚上装",
limitNum = 1,
beginTime = v51,
order = 173,
itemIds = {
510106
}
},
[4965] = {
commodityId = 4965,
commodityName = "拼贴风尚下装",
limitNum = 1,
beginTime = v51,
order = 174,
itemIds = {
520068
}
},
[4966] = {
commodityId = 4966,
commodityName = "拼贴风尚手套",
limitNum = 1,
beginTime = v51,
order = 175,
itemIds = {
530048
}
},
[4967] = {
commodityId = 4967,
commodityName = "日落海岛上装",
limitNum = 1,
beginTime = v51,
order = 176,
itemIds = {
510107
}
},
[4968] = {
commodityId = 4968,
commodityName = "日落海岛下装",
limitNum = 1,
beginTime = v51,
order = 177,
itemIds = {
520069
}
},
[4969] = {
commodityId = 4969,
commodityName = "日落海岛手套",
limitNum = 1,
beginTime = v51,
order = 178,
itemIds = {
530049
}
},
[4970] = {
commodityId = 4970,
commodityName = "热带风情上装",
limitNum = 1,
beginTime = v51,
order = 179,
itemIds = {
510108
}
},
[4971] = {
commodityId = 4971,
commodityName = "热带风情下装",
limitNum = 1,
beginTime = v51,
order = 180,
itemIds = {
520070
}
},
[4972] = {
commodityId = 4972,
commodityName = "热带风情手套",
limitNum = 1,
beginTime = v51,
order = 181,
itemIds = {
530050
}
},
[4973] = {
commodityId = 4973,
commodityName = "新奇撞色上装",
limitNum = 1,
beginTime = v51,
order = 182,
itemIds = {
510109
}
},
[4974] = {
commodityId = 4974,
commodityName = "新奇撞色下装",
limitNum = 1,
beginTime = v51,
order = 183,
itemIds = {
520071
}
},
[4975] = {
commodityId = 4975,
commodityName = "新奇撞色手套",
limitNum = 1,
beginTime = v51,
order = 184,
itemIds = {
530051
}
},
[4976] = {
commodityId = 4976,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v51,
endTime = {
seconds = 1715270399
},
itemIds = {
725002
}
},
[4977] = {
commodityId = 4977,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v51,
endTime = {
seconds = 1715270399
},
itemIds = {
725004
}
},
[4978] = {
commodityId = 4978,
price = 3,
limitNum = 10,
beginTime = v51,
endTime = {
seconds = 1715270399
},
itemIds = v45
},
[4979] = {
commodityId = 4979,
commodityName = "珍饺饺",
price = 60,
limitNum = 1,
beginTime = v52,
order = 11,
itemIds = {
400820
}
},
[4980] = {
commodityId = 4980,
commodityName = "喵喵糖",
price = 40,
limitNum = 1,
beginTime = v52,
order = 34,
itemIds = {
620166
}
},
[4981] = {
commodityId = 4981,
commodityName = "元气吐司",
price = 40,
limitNum = 1,
beginTime = v52,
order = 35,
itemIds = {
630087
}
},
[4982] = {
commodityId = 4982,
commodityName = "赞不绝手上装",
limitNum = 1,
beginTime = v52,
order = 185,
itemIds = {
510092
}
},
[4983] = {
commodityId = 4983,
commodityName = "赞不绝手下装",
limitNum = 1,
beginTime = v52,
order = 186,
itemIds = {
520061
}
},
[4984] = {
commodityId = 4984,
commodityName = "赞不绝手手套",
limitNum = 1,
beginTime = v52,
order = 187,
itemIds = {
530041
}
},
[4985] = {
commodityId = 4985,
commodityName = "寻味奇趣上装",
limitNum = 1,
beginTime = v52,
order = 188,
itemIds = {
510093
}
},
[4986] = {
commodityId = 4986,
commodityName = "寻味奇趣下装",
limitNum = 1,
beginTime = v52,
order = 189,
itemIds = {
520062
}
},
[4987] = {
commodityId = 4987,
commodityName = "寻味奇趣手套",
limitNum = 1,
beginTime = v52,
order = 190,
itemIds = {
530042
}
},
[4988] = {
commodityId = 4988,
commodityName = "红莓酥糖上装",
limitNum = 1,
beginTime = v52,
order = 191,
itemIds = {
510094
}
},
[4989] = {
commodityId = 4989,
commodityName = "红莓酥糖下装",
limitNum = 1,
beginTime = v52,
order = 192,
itemIds = {
520063
}
},
[4990] = {
commodityId = 4990,
commodityName = "红莓酥糖手套",
limitNum = 1,
beginTime = v52,
order = 193,
itemIds = {
530043
}
},
[4991] = {
commodityId = 4991,
commodityName = "好运南风上装",
limitNum = 1,
beginTime = v52,
order = 194,
itemIds = {
510077
}
},
[4992] = {
commodityId = 4992,
commodityName = "好运南风下装",
limitNum = 1,
beginTime = v52,
order = 195,
itemIds = {
520051
}
},
[4993] = {
commodityId = 4993,
commodityName = "好运南风手套",
limitNum = 1,
beginTime = v52,
order = 196,
itemIds = {
530030
}
},
[4994] = {
commodityId = 4994,
commodityName = "粉墨工匠上装",
limitNum = 1,
beginTime = v52,
order = 197,
itemIds = {
510026
}
},
[4995] = {
commodityId = 4995,
commodityName = "粉墨工匠下装",
limitNum = 1,
beginTime = v52,
order = 198,
itemIds = {
520024
}
},
[4996] = {
commodityId = 4996,
commodityName = "粉墨工匠手套",
limitNum = 1,
beginTime = v52,
order = 199,
itemIds = {
530019
}
},
[4997] = {
commodityId = 4997,
commodityName = v12,
price = 3,
limitNum = 10,
beginTime = v52,
endTime = {
seconds = 1715270399
},
itemIds = v44
},
[4998] = {
commodityId = 4998,
commodityName = "薄荷绿泡泡棒",
price = 3,
limitNum = 10,
beginTime = v52,
endTime = {
seconds = 1715270399
},
itemIds = {
725003
}
},
[4999] = {
commodityId = 4999,
commodityName = "黄小星荧光棒",
price = 3,
limitNum = 10,
beginTime = v52,
endTime = {
seconds = 1715270399
},
itemIds = {
725006
}
},
[5001] = {
commodityId = 5001,
commodityName = "鲨拉拉",
price = 60,
limitNum = 1,
beginTime = v53,
order = 12,
gender = 0,
itemIds = {
400200
}
},
[5002] = {
commodityId = 5002,
commodityName = "冰糕糕",
price = 60,
limitNum = 1,
beginTime = {
seconds = 1704988800
},
order = 13,
gender = 0,
itemIds = {
400230
},
beginShowTime = {
seconds = 1704384000
}
},
[5003] = {
commodityId = 5003,
commodityName = "问号头饰",
price = 40,
limitNum = 1,
beginTime = v53,
order = 36,
gender = 0,
itemIds = {
630005
}
},
[5004] = {
commodityId = 5004,
commodityName = "超级玩家眼镜",
price = 40,
limitNum = 1,
beginTime = v53,
order = 37,
gender = 0,
itemIds = {
610002
}
},
[5005] = {
commodityId = 5005,
commodityName = "灰色边缘上装",
limitNum = 1,
beginTime = v53,
order = 200,
gender = 0,
itemIds = {
510018
}
},
[5006] = {
commodityId = 5006,
commodityName = "灰色边缘下装",
limitNum = 1,
beginTime = v53,
order = 201,
gender = 0,
itemIds = {
520015
}
},
[5007] = {
commodityId = 5007,
commodityName = "灰色边缘手套",
limitNum = 1,
beginTime = v53,
order = 202,
gender = 0,
itemIds = {
530017
}
},
[5008] = {
commodityId = 5008,
commodityName = "最佳员工上装",
limitNum = 1,
beginTime = v53,
order = 203,
gender = 0,
itemIds = {
510005
}
},
[5009] = {
commodityId = 5009,
commodityName = "最佳员工下装",
limitNum = 1,
beginTime = v53,
order = 204,
gender = 0,
itemIds = {
520005
}
},
[5010] = {
commodityId = 5010,
commodityName = "最佳员工手套",
limitNum = 1,
beginTime = v53,
order = 205,
gender = 0,
itemIds = {
530007
}
},
[5011] = {
commodityId = 5011,
commodityName = "清爽运动上装",
limitNum = 1,
beginTime = v53,
order = 206,
gender = 0,
itemIds = {
510008
}
},
[5012] = {
commodityId = 5012,
commodityName = "清爽运动下装",
limitNum = 1,
beginTime = v53,
order = 207,
gender = 0,
itemIds = {
520008
}
},
[5013] = {
commodityId = 5013,
commodityName = "清爽运动手套",
limitNum = 1,
beginTime = v53,
order = 208,
gender = 0,
itemIds = {
530015
}
},
[5014] = {
commodityId = 5014,
commodityName = "红莓丝绒上装",
limitNum = 1,
beginTime = v53,
order = 209,
gender = 0,
itemIds = {
510032
}
},
[5015] = {
commodityId = 5015,
commodityName = "红莓丝绒下装",
limitNum = 1,
beginTime = v53,
order = 210,
gender = 0,
itemIds = {
520030
}
},
[5016] = {
commodityId = 5016,
commodityName = "彩虹旋律上装",
limitNum = 1,
beginTime = v53,
order = 211,
gender = 0,
itemIds = {
510028
}
},
[5017] = {
commodityId = 5017,
commodityName = "彩虹旋律下装",
limitNum = 1,
beginTime = v53,
order = 212,
gender = 0,
itemIds = {
520026
}
},
[5018] = {
commodityId = 5018,
commodityName = "摩登学院上装",
limitNum = 1,
beginTime = v53,
order = 213,
gender = 0,
itemIds = {
510030
}
},
[5019] = {
commodityId = 5019,
commodityName = "摩登学院下装",
limitNum = 1,
beginTime = v53,
order = 214,
gender = 0,
itemIds = {
520028
}
},
[5020] = {
commodityId = 5020,
commodityName = "黑莓可可上装",
limitNum = 1,
beginTime = v53,
order = 215,
gender = 0,
itemIds = {
510049
}
},
[5021] = {
commodityId = 5021,
commodityName = "黑莓可可下装",
limitNum = 1,
beginTime = v53,
order = 216,
gender = 0,
itemIds = {
520039
}
},
[5022] = {
commodityId = 5022,
commodityName = "蓝色童话上装",
limitNum = 1,
beginTime = v53,
order = 217,
gender = 0,
itemIds = {
510045
}
},
[5023] = {
commodityId = 5023,
commodityName = "蓝色童话下装",
limitNum = 1,
beginTime = v53,
order = 218,
gender = 0,
itemIds = {
520035
}
},
[5024] = {
commodityId = 5024,
commodityName = "蓝莓戚风上装",
limitNum = 1,
beginTime = v53,
order = 219,
gender = 0,
itemIds = {
510050
}
},
[5025] = {
commodityId = 5025,
commodityName = "蓝莓戚风下装",
limitNum = 1,
beginTime = v53,
order = 220,
gender = 0,
itemIds = {
520040
}
},
[5026] = {
commodityId = 5026,
commodityName = "三花摩卡上装",
limitNum = 1,
beginTime = v53,
order = 221,
gender = 0,
itemIds = {
510043
}
},
[5027] = {
commodityId = 5027,
commodityName = "三花摩卡下装",
limitNum = 1,
beginTime = v53,
order = 222,
gender = 0,
itemIds = {
520033
}
},
[5028] = {
commodityId = 5028,
commodityName = "烘焙甜心上装",
limitNum = 1,
beginTime = v53,
order = 223,
gender = 0,
itemIds = {
510029
}
},
[5029] = {
commodityId = 5029,
commodityName = "烘焙甜心下装",
limitNum = 1,
beginTime = v53,
order = 224,
gender = 0,
itemIds = {
520027
}
},
[5030] = {
commodityId = 5030,
commodityName = "白桃气泡上装",
limitNum = 1,
beginTime = v53,
order = 225,
gender = 0,
itemIds = {
510044
}
},
[5031] = {
commodityId = 5031,
commodityName = "白桃气泡下装",
limitNum = 1,
beginTime = v53,
order = 226,
gender = 0,
itemIds = {
520034
}
},
[5032] = {
commodityId = 5032,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v53,
endTime = {
seconds = 1711641599
},
gender = 0,
itemIds = {
725002
}
},
[5033] = {
commodityId = 5033,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v53,
endTime = {
seconds = 1711641599
},
gender = 0,
itemIds = {
725004
}
},
[5034] = {
commodityId = 5034,
price = 3,
limitNum = 10,
beginTime = v53,
endTime = {
seconds = 1711641599
},
gender = 0,
itemIds = v45
},
[5035] = {
commodityId = 5035,
commodityName = "列车长",
price = 60,
limitNum = 1,
beginTime = v54,
order = 9,
itemIds = {
401970
}
},
[5036] = {
commodityId = 5036,
commodityName = "波纹甜甜心",
price = 40,
limitNum = 1,
beginTime = v54,
order = 30,
itemIds = {
630156
}
},
[5037] = {
commodityId = 5037,
commodityName = "火星朋克",
price = 40,
limitNum = 1,
beginTime = v54,
order = 31,
itemIds = {
610117
}
},
[5038] = {
commodityId = 5038,
commodityName = "邻家学妹上装",
limitNum = 1,
beginTime = v54,
order = 158,
itemIds = {
510133
}
},
[5039] = {
commodityId = 5039,
commodityName = "邻家学妹下装",
limitNum = 1,
beginTime = v54,
order = 159,
itemIds = {
520088
}
},
[5040] = {
commodityId = 5040,
commodityName = "邻家学妹手套",
limitNum = 1,
beginTime = v54,
order = 160,
itemIds = {
530066
}
},
[5041] = {
commodityId = 5041,
commodityName = "精英学姐上装",
limitNum = 1,
beginTime = v54,
order = 161,
itemIds = {
510134
}
},
[5042] = {
commodityId = 5042,
commodityName = "精英学姐下装",
limitNum = 1,
beginTime = v54,
order = 162,
itemIds = {
520089
}
},
[5043] = {
commodityId = 5043,
commodityName = "精英学姐手套",
limitNum = 1,
beginTime = v54,
order = 163,
itemIds = {
530067
}
},
[5044] = {
commodityId = 5044,
commodityName = "隐藏富豪上装",
limitNum = 1,
beginTime = v54,
order = 164,
itemIds = {
510136
}
},
[5045] = {
commodityId = 5045,
commodityName = "隐藏富豪下装",
limitNum = 1,
beginTime = v54,
order = 165,
itemIds = {
520091
}
},
[5046] = {
commodityId = 5046,
commodityName = "隐藏富豪手套",
limitNum = 1,
beginTime = v54,
order = 166,
itemIds = {
530069
}
},
[5047] = {
commodityId = 5047,
commodityName = "年会爆款上装",
limitNum = 1,
beginTime = v54,
order = 167,
itemIds = {
510137
}
},
[5048] = {
commodityId = 5048,
commodityName = "年会爆款下装",
limitNum = 1,
beginTime = v54,
order = 168,
itemIds = {
520092
}
},
[5049] = {
commodityId = 5049,
commodityName = "年会爆款手套",
limitNum = 1,
beginTime = v54,
order = 169,
itemIds = {
530070
}
},
[5050] = {
commodityId = 5050,
commodityName = "进步青年上装",
limitNum = 1,
beginTime = v54,
order = 170,
itemIds = {
510138
}
},
[5051] = {
commodityId = 5051,
commodityName = "进步青年下装",
limitNum = 1,
beginTime = v54,
order = 171,
itemIds = {
520093
}
},
[5052] = {
commodityId = 5052,
commodityName = "进步青年手套",
limitNum = 1,
beginTime = v54,
order = 172,
itemIds = {
530071
}
},
[5053] = {
commodityId = 5053,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v54,
endTime = {
seconds = 1718899199
},
itemIds = {
725002
}
},
[5054] = {
commodityId = 5054,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v54,
endTime = {
seconds = 1718899199
},
itemIds = {
725004
}
},
[5055] = {
commodityId = 5055,
price = 3,
limitNum = 10,
beginTime = v54,
endTime = {
seconds = 1718899199
},
itemIds = v45
},
[5056] = {
commodityId = 5056,
commodityName = "萌星空乘",
price = 60,
limitNum = 1,
beginTime = v55,
order = 8,
itemIds = {
402070
}
},
[5057] = {
commodityId = 5057,
commodityName = "开心娃娃",
price = 40,
limitNum = 1,
beginTime = v55,
order = 28,
itemIds = {
620330
}
},
[5058] = {
commodityId = 5058,
commodityName = "掌白白",
price = 40,
limitNum = 1,
beginTime = v55,
order = 29,
itemIds = {
630218
}
},
[5059] = {
commodityId = 5059,
commodityName = "灰调潮流上装",
limitNum = 1,
beginTime = v55,
order = 143,
itemIds = {
510159
}
},
[5060] = {
commodityId = 5060,
commodityName = "灰调潮流下装",
limitNum = 1,
beginTime = v55,
order = 144,
itemIds = {
520110
}
},
[5061] = {
commodityId = 5061,
commodityName = "灰调潮流手套",
limitNum = 1,
beginTime = v55,
order = 145,
itemIds = {
530087
}
},
[5062] = {
commodityId = 5062,
commodityName = "灰粉嘻哈上装",
limitNum = 1,
beginTime = v55,
order = 146,
itemIds = {
510160
}
},
[5063] = {
commodityId = 5063,
commodityName = "灰粉嘻哈下装",
limitNum = 1,
beginTime = v55,
order = 147,
itemIds = {
520111
}
},
[5064] = {
commodityId = 5064,
commodityName = "灰粉嘻哈手套",
limitNum = 1,
beginTime = v55,
order = 148,
itemIds = {
530088
}
},
[5065] = {
commodityId = 5065,
commodityName = "多元时尚上装",
limitNum = 1,
beginTime = v55,
order = 149,
itemIds = {
510161
}
},
[5066] = {
commodityId = 5066,
commodityName = "多元时尚下装",
limitNum = 1,
beginTime = v55,
order = 150,
itemIds = {
520112
}
},
[5067] = {
commodityId = 5067,
commodityName = "多元时尚手套",
limitNum = 1,
beginTime = v55,
order = 151,
itemIds = {
530089
}
},
[5068] = {
commodityId = 5068,
commodityName = "古韵长衣上装",
limitNum = 1,
beginTime = v55,
order = 152,
itemIds = {
510153
}
},
[5069] = {
commodityId = 5069,
commodityName = "古韵长衣下装",
limitNum = 1,
beginTime = v55,
order = 153,
itemIds = {
520105
}
},
[5070] = {
commodityId = 5070,
commodityName = "古韵长衣手套",
limitNum = 1,
beginTime = v55,
order = 154,
itemIds = {
530082
}
},
[5071] = {
commodityId = 5071,
commodityName = "气息归元上装",
limitNum = 1,
beginTime = v55,
order = 155,
itemIds = {
510154
}
},
[5072] = {
commodityId = 5072,
commodityName = "气息归元下装",
limitNum = 1,
beginTime = v55,
order = 156,
itemIds = {
520106
}
},
[5073] = {
commodityId = 5073,
commodityName = "气息归元手套",
limitNum = 1,
beginTime = v55,
order = 157,
itemIds = {
530083
}
},
[5074] = {
commodityId = 5074,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v55,
endTime = {
seconds = 1722527999
},
itemIds = {
725002
}
},
[5075] = {
commodityId = 5075,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v55,
endTime = {
seconds = 1722527999
},
itemIds = {
725004
}
},
[5076] = {
commodityId = 5076,
price = 3,
limitNum = 10,
beginTime = v55,
endTime = {
seconds = 1722527999
},
itemIds = v45
},
[5077] = {
commodityId = 5077,
commodityName = "星牛仔",
price = 60,
limitNum = 1,
beginTime = v56,
order = 7,
itemIds = {
402350
}
},
[5078] = {
commodityId = 5078,
commodityName = "汪汪快乐罐",
price = 40,
limitNum = 1,
beginTime = v56,
order = 26,
itemIds = {
630260
}
},
[5079] = {
commodityId = 5079,
commodityName = "炫光朋克",
price = 40,
limitNum = 1,
beginTime = v56,
order = 27,
itemIds = {
610190
}
},
[5080] = {
commodityId = 5080,
commodityName = "夏意清新上装",
limitNum = 1,
beginTime = v56,
order = 128,
itemIds = {
510182
}
},
[5081] = {
commodityId = 5081,
commodityName = "夏意清新下装",
limitNum = 1,
beginTime = v56,
order = 129,
itemIds = {
520125
}
},
[5082] = {
commodityId = 5082,
commodityName = "夏意清新手套",
limitNum = 1,
beginTime = v56,
order = 130,
itemIds = {
530102
}
},
[5083] = {
commodityId = 5083,
commodityName = "盛夏果实上装",
limitNum = 1,
beginTime = v56,
order = 131,
itemIds = {
510183
}
},
[5084] = {
commodityId = 5084,
commodityName = "盛夏果实下装",
limitNum = 1,
beginTime = v56,
order = 132,
itemIds = {
520126
}
},
[5085] = {
commodityId = 5085,
commodityName = "盛夏果实手套",
limitNum = 1,
beginTime = v56,
order = 133,
itemIds = {
530103
}
},
[5086] = {
commodityId = 5086,
commodityName = "仲夏芳菲上装",
limitNum = 1,
beginTime = v56,
order = 134,
itemIds = {
510200
}
},
[5087] = {
commodityId = 5087,
commodityName = "仲夏芳菲下装",
limitNum = 1,
beginTime = v56,
order = 135,
itemIds = {
520139
}
},
[5088] = {
commodityId = 5088,
commodityName = "仲夏芳菲手套",
limitNum = 1,
beginTime = v56,
order = 136,
itemIds = {
530116
}
},
[5089] = {
commodityId = 5089,
commodityName = "童梦奇缘上装",
limitNum = 1,
beginTime = v56,
order = 137,
itemIds = {
510201
}
},
[5090] = {
commodityId = 5090,
commodityName = "童梦奇缘下装",
limitNum = 1,
beginTime = v56,
order = 138,
itemIds = {
520140
}
},
[5091] = {
commodityId = 5091,
commodityName = "童梦奇缘手套",
limitNum = 1,
beginTime = v56,
order = 139,
itemIds = {
530117
}
},
[5092] = {
commodityId = 5092,
commodityName = "青柠之恋上装",
limitNum = 1,
beginTime = v56,
order = 140,
itemIds = {
510202
}
},
[5093] = {
commodityId = 5093,
commodityName = "青柠之恋下装",
limitNum = 1,
beginTime = v56,
order = 141,
itemIds = {
520141
}
},
[5094] = {
commodityId = 5094,
commodityName = "青柠之恋手套",
limitNum = 1,
beginTime = v56,
order = 142,
itemIds = {
530118
}
},
[5095] = {
commodityId = 5095,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v56,
endTime = {
seconds = 1726156799
},
itemIds = {
725002
}
},
[5096] = {
commodityId = 5096,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v56,
endTime = {
seconds = 1726156799
},
itemIds = {
725004
}
},
[5097] = {
commodityId = 5097,
price = 3,
limitNum = 10,
beginTime = v56,
endTime = {
seconds = 1726156799
},
itemIds = v45
},
[5098] = {
commodityId = 5098,
commodityName = "逸书公子",
price = 60,
limitNum = 1,
beginTime = v57,
order = 6,
itemIds = {
403220
}
},
[5099] = {
commodityId = 5099,
commodityName = "珍藏心意",
price = 40,
limitNum = 1,
beginTime = v57,
order = 24,
itemIds = {
630311
}
},
[5100] = {
commodityId = 5100,
commodityName = "生机绿叉",
price = 40,
limitNum = 1,
beginTime = v57,
order = 25,
itemIds = {
620460
}
},
[5101] = {
commodityId = 5101,
commodityName = "专业态度上装",
limitNum = 1,
beginTime = v57,
order = 113,
itemIds = {
510216
}
},
[5102] = {
commodityId = 5102,
commodityName = "专业态度下装",
limitNum = 1,
beginTime = v57,
order = 114,
itemIds = {
520153
}
},
[5103] = {
commodityId = 5103,
commodityName = "专业态度手套",
limitNum = 1,
beginTime = v57,
order = 115,
itemIds = {
530129
}
},
[5104] = {
commodityId = 5104,
commodityName = "职场萌星上装",
limitNum = 1,
beginTime = v57,
order = 116,
itemIds = {
510233
}
},
[5105] = {
commodityId = 5105,
commodityName = "职场萌星下装",
limitNum = 1,
beginTime = v57,
order = 117,
itemIds = {
520162
}
},
[5106] = {
commodityId = 5106,
commodityName = "职场萌星手套",
limitNum = 1,
beginTime = v57,
order = 118,
itemIds = {
530138
}
},
[5107] = {
commodityId = 5107,
commodityName = "赛场飞驰上装",
limitNum = 1,
beginTime = v57,
order = 119,
itemIds = {
510219
}
},
[5108] = {
commodityId = 5108,
commodityName = "赛场飞驰下装",
limitNum = 1,
beginTime = v57,
order = 120,
itemIds = {
520155
}
},
[5109] = {
commodityId = 5109,
commodityName = "赛场飞驰手套",
limitNum = 1,
beginTime = v57,
order = 121,
itemIds = {
530131
}
},
[5110] = {
commodityId = 5110,
commodityName = "浅蓝节拍上装",
limitNum = 1,
beginTime = v57,
order = 122,
itemIds = {
510220
}
},
[5111] = {
commodityId = 5111,
commodityName = "浅蓝节拍下装",
limitNum = 1,
beginTime = v57,
order = 123,
itemIds = {
520156
}
},
[5112] = {
commodityId = 5112,
commodityName = "浅蓝节拍手套",
limitNum = 1,
beginTime = v57,
order = 124,
itemIds = {
530132
}
},
[5113] = {
commodityId = 5113,
commodityName = "春日运动上装",
limitNum = 1,
beginTime = v57,
order = 125,
itemIds = {
510221
}
},
[5114] = {
commodityId = 5114,
commodityName = "春日运动下装",
limitNum = 1,
beginTime = v57,
order = 126,
itemIds = {
520157
}
},
[5115] = {
commodityId = 5115,
commodityName = "春日运动手套",
limitNum = 1,
beginTime = v57,
order = 127,
itemIds = {
530133
}
},
[5116] = {
commodityId = 5116,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v57,
endTime = {
seconds = 1730390399
},
itemIds = {
725002
}
},
[5117] = {
commodityId = 5117,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v57,
endTime = {
seconds = 1730390399
},
itemIds = {
725004
}
},
[5118] = {
commodityId = 5118,
price = 3,
limitNum = 10,
beginTime = v57,
endTime = {
seconds = 1730390399
},
itemIds = v45
},
[5119] = {
commodityId = 5119,
commodityName = "甜心果",
price = 60,
limitNum = 1,
beginTime = v58,
order = 5,
itemIds = {
403180
}
},
[5120] = {
commodityId = 5120,
commodityName = "粉豹魅影",
price = 40,
limitNum = 1,
beginTime = v58,
order = 22,
itemIds = {
610249
}
},
[5121] = {
commodityId = 5121,
commodityName = "一击必中",
price = 40,
limitNum = 1,
beginTime = v58,
order = 23,
itemIds = {
620531
}
},
[5122] = {
commodityId = 5122,
commodityName = "粉焰旋风上装",
limitNum = 1,
beginTime = v58,
order = 98,
itemIds = {
510254
}
},
[5123] = {
commodityId = 5123,
commodityName = "粉焰旋风下装",
limitNum = 1,
beginTime = v58,
order = 99,
itemIds = {
520178
}
},
[5124] = {
commodityId = 5124,
commodityName = "粉焰旋风手套",
limitNum = 1,
beginTime = v58,
order = 100,
itemIds = {
530154
}
},
[5125] = {
commodityId = 5125,
commodityName = "蔚蓝闪电上装",
limitNum = 1,
beginTime = v58,
order = 101,
itemIds = {
510255
}
},
[5126] = {
commodityId = 5126,
commodityName = "蔚蓝闪电下装",
limitNum = 1,
beginTime = v58,
order = 102,
itemIds = {
520179
}
},
[5127] = {
commodityId = 5127,
commodityName = "蔚蓝闪电手套",
limitNum = 1,
beginTime = v58,
order = 103,
itemIds = {
530155
}
},
[5128] = {
commodityId = 5128,
commodityName = "逐梦狂飙上装",
limitNum = 1,
beginTime = v58,
order = 104,
itemIds = {
510256
}
},
[5129] = {
commodityId = 5129,
commodityName = "逐梦狂飙下装",
limitNum = 1,
beginTime = v58,
order = 105,
itemIds = {
520180
}
},
[5130] = {
commodityId = 5130,
commodityName = "逐梦狂飙手套",
limitNum = 1,
beginTime = v58,
order = 106,
itemIds = {
530156
}
},
[5131] = {
commodityId = 5131,
commodityName = "云龙武袍上装",
limitNum = 1,
beginTime = v58,
order = 107,
itemIds = {
510257
}
},
[5132] = {
commodityId = 5132,
commodityName = "云龙武袍下装",
limitNum = 1,
beginTime = v58,
order = 108,
itemIds = {
520181
}
},
[5133] = {
commodityId = 5133,
commodityName = "云龙武袍手套",
limitNum = 1,
beginTime = v58,
order = 109,
itemIds = {
530157
}
},
[5134] = {
commodityId = 5134,
commodityName = "青岚短衫上装",
limitNum = 1,
beginTime = v58,
order = 110,
itemIds = {
510268
}
},
[5135] = {
commodityId = 5135,
commodityName = "青岚短衫下装",
limitNum = 1,
beginTime = v58,
order = 111,
itemIds = {
520182
}
},
[5136] = {
commodityId = 5136,
commodityName = "青岚短衫手套",
limitNum = 1,
beginTime = v58,
order = 112,
itemIds = {
530158
}
},
[5137] = {
commodityId = 5137,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v58,
endTime = {
seconds = 1734019199
},
itemIds = {
725002
}
},
[5138] = {
commodityId = 5138,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v58,
endTime = {
seconds = 1734019199
},
itemIds = {
725004
}
},
[5139] = {
commodityId = 5139,
price = 3,
limitNum = 10,
beginTime = v58,
endTime = {
seconds = 1734019199
},
itemIds = v45
},
[5140] = {
commodityId = 5140,
commodityName = "小雪梨",
price = 60,
limitNum = 1,
beginTime = v59,
order = 4,
itemIds = {
404090
}
},
[5141] = {
commodityId = 5141,
commodityName = "粉羽风尚",
price = 40,
limitNum = 1,
beginTime = v59,
order = 20,
itemIds = {
610271
}
},
[5142] = {
commodityId = 5142,
commodityName = "斑点暖帽",
price = 40,
limitNum = 1,
beginTime = v59,
order = 21,
itemIds = {
630404
}
},
[5143] = {
commodityId = 5143,
commodityName = "桃心王子上装",
limitNum = 1,
beginTime = v59,
order = 83,
itemIds = {
510280
}
},
[5144] = {
commodityId = 5144,
commodityName = "桃心王子下装",
limitNum = 1,
beginTime = v59,
order = 84,
itemIds = {
520193
}
},
[5145] = {
commodityId = 5145,
commodityName = "桃心王子手套",
limitNum = 1,
beginTime = v59,
order = 85,
itemIds = {
530169
}
},
[5146] = {
commodityId = 5146,
commodityName = "梅花公爵上装",
limitNum = 1,
beginTime = v59,
order = 86,
itemIds = {
510281
}
},
[5147] = {
commodityId = 5147,
commodityName = "梅花公爵下装",
limitNum = 1,
beginTime = v59,
order = 87,
itemIds = {
520194
}
},
[5148] = {
commodityId = 5148,
commodityName = "梅花公爵手套",
limitNum = 1,
beginTime = v59,
order = 88,
itemIds = {
530170
}
},
[5149] = {
commodityId = 5149,
commodityName = "腾云一舞上装",
limitNum = 1,
beginTime = v59,
order = 89,
itemIds = {
510277
}
},
[5150] = {
commodityId = 5150,
commodityName = "腾云一舞下装",
limitNum = 1,
beginTime = v59,
order = 90,
itemIds = {
520190
}
},
[5151] = {
commodityId = 5151,
commodityName = "腾云一舞手套",
limitNum = 1,
beginTime = v59,
order = 91,
itemIds = {
530166
}
},
[5152] = {
commodityId = 5152,
commodityName = "竹韵清扬上装",
limitNum = 1,
beginTime = v59,
order = 92,
itemIds = {
510278
}
},
[5153] = {
commodityId = 5153,
commodityName = "竹韵清扬下装",
limitNum = 1,
beginTime = v59,
order = 93,
itemIds = {
520191
}
},
[5154] = {
commodityId = 5154,
commodityName = "竹韵清扬手套",
limitNum = 1,
beginTime = v59,
order = 94,
itemIds = {
530167
}
},
[5155] = {
commodityId = 5155,
commodityName = "墨韵乘云上装",
limitNum = 1,
beginTime = v59,
order = 95,
itemIds = {
510279
}
},
[5156] = {
commodityId = 5156,
commodityName = "墨韵乘云下装",
limitNum = 1,
beginTime = v59,
order = 96,
itemIds = {
520192
}
},
[5157] = {
commodityId = 5157,
commodityName = "墨韵乘云手套",
limitNum = 1,
beginTime = v59,
order = 97,
itemIds = {
530168
}
},
[5158] = {
commodityId = 5158,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v59,
endTime = {
seconds = 1738252799
},
itemIds = {
725002
}
},
[5159] = {
commodityId = 5159,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v59,
endTime = {
seconds = 1738252799
},
itemIds = {
725004
}
},
[5160] = {
commodityId = 5160,
price = 3,
limitNum = 10,
beginTime = v59,
endTime = {
seconds = 1738252799
},
itemIds = v45
},
[5161] = {
commodityId = 5161,
commodityName = "晴小岚",
price = 60,
limitNum = 1,
beginTime = v60,
order = 3,
itemIds = {
404280
}
},
[5162] = {
commodityId = 5162,
commodityName = "小小暖锅",
price = 40,
limitNum = 1,
beginTime = v60,
order = 18,
itemIds = {
620768
}
},
[5163] = {
commodityId = 5163,
commodityName = "忧郁清晨",
price = 40,
limitNum = 1,
beginTime = v60,
order = 19,
itemIds = {
630511
}
},
[5164] = {
commodityId = 5164,
commodityName = "鸭鸭护卫队上装",
limitNum = 1,
beginTime = v60,
order = 68,
itemIds = {
510300
}
},
[5165] = {
commodityId = 5165,
commodityName = "鸭鸭护卫队下装",
limitNum = 1,
beginTime = v60,
order = 69,
itemIds = {
520202
}
},
[5166] = {
commodityId = 5166,
commodityName = "鸭鸭护卫队手套",
limitNum = 1,
beginTime = v60,
order = 70,
itemIds = {
530178
}
},
[5167] = {
commodityId = 5167,
commodityName = "脆弱鸭鸭上装",
limitNum = 1,
beginTime = v60,
order = 71,
itemIds = {
510301
}
},
[5168] = {
commodityId = 5168,
commodityName = "脆弱鸭鸭下装",
limitNum = 1,
beginTime = v60,
order = 72,
itemIds = {
520203
}
},
[5169] = {
commodityId = 5169,
commodityName = "脆弱鸭鸭手套",
limitNum = 1,
beginTime = v60,
order = 73,
itemIds = {
530179
}
},
[5170] = {
commodityId = 5170,
commodityName = "冬日暖喵上装",
limitNum = 1,
beginTime = v60,
order = 74,
itemIds = {
510297
}
},
[5171] = {
commodityId = 5171,
commodityName = "冬日暖喵下装",
limitNum = 1,
beginTime = v60,
order = 75,
itemIds = {
520199
}
},
[5172] = {
commodityId = 5172,
commodityName = "冬日暖喵手套",
limitNum = 1,
beginTime = v60,
order = 76,
itemIds = {
530175
}
},
[5173] = {
commodityId = 5173,
commodityName = "林深如墨上装",
limitNum = 1,
beginTime = v60,
order = 77,
itemIds = {
510298
}
},
[5174] = {
commodityId = 5174,
commodityName = "林深如墨下装",
limitNum = 1,
beginTime = v60,
order = 78,
itemIds = {
520200
}
},
[5175] = {
commodityId = 5175,
commodityName = "林深如墨手套",
limitNum = 1,
beginTime = v60,
order = 79,
itemIds = {
530176
}
},
[5176] = {
commodityId = 5176,
commodityName = "冬夜星火上装",
limitNum = 1,
beginTime = v60,
order = 80,
itemIds = {
510299
}
},
[5177] = {
commodityId = 5177,
commodityName = "冬夜星火下装",
limitNum = 1,
beginTime = v60,
order = 81,
itemIds = {
520201
}
},
[5178] = {
commodityId = 5178,
commodityName = "冬夜星火手套",
limitNum = 1,
beginTime = v60,
order = 82,
itemIds = {
530177
}
},
[5179] = {
commodityId = 5179,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v60,
endTime = {
seconds = 1743091199
},
itemIds = {
725002
}
},
[5180] = {
commodityId = 5180,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v60,
endTime = {
seconds = 1743091199
},
itemIds = {
725004
}
},
[5181] = {
commodityId = 5181,
price = 3,
limitNum = 10,
beginTime = v60,
endTime = {
seconds = 1743091199
},
itemIds = v45
},
[5182] = {
commodityId = 5182,
commodityName = "蛋小龙",
price = 60,
limitNum = 1,
beginTime = v61,
order = 2,
itemIds = {
410200
}
},
[5183] = {
commodityId = 5183,
commodityName = "梦幻星球",
price = 40,
limitNum = 1,
beginTime = v61,
order = 16,
itemIds = {
630546
}
},
[5184] = {
commodityId = 5184,
commodityName = "向右看齐",
price = 40,
limitNum = 1,
beginTime = v61,
order = 17,
itemIds = {
610348
}
},
[5185] = {
commodityId = 5185,
commodityName = "牛油果之友上装",
limitNum = 1,
beginTime = v61,
order = 53,
itemIds = {
510335
}
},
[5186] = {
commodityId = 5186,
commodityName = "牛油果之友下装",
limitNum = 1,
beginTime = v61,
order = 54,
itemIds = {
520224
}
},
[5187] = {
commodityId = 5187,
commodityName = "牛油果之友手套",
limitNum = 1,
beginTime = v61,
order = 55,
itemIds = {
530200
}
},
[5188] = {
commodityId = 5188,
commodityName = "果冻熊之友上装",
limitNum = 1,
beginTime = v61,
order = 56,
itemIds = {
510336
}
},
[5189] = {
commodityId = 5189,
commodityName = "果冻熊之友下装",
limitNum = 1,
beginTime = v61,
order = 57,
itemIds = {
520225
}
},
[5190] = {
commodityId = 5190,
commodityName = "果冻熊之友手套",
limitNum = 1,
beginTime = v61,
order = 58,
itemIds = {
530201
}
},
[5191] = {
commodityId = 5191,
commodityName = "棉花狗之友上装",
limitNum = 1,
beginTime = v61,
order = 59,
itemIds = {
510337
}
},
[5192] = {
commodityId = 5192,
commodityName = "棉花狗之友下装",
limitNum = 1,
beginTime = v61,
order = 60,
itemIds = {
520226
}
},
[5193] = {
commodityId = 5193,
commodityName = "棉花狗之友手套",
limitNum = 1,
beginTime = v61,
order = 61,
itemIds = {
530202
}
},
[5194] = {
commodityId = 5194,
commodityName = "海岛风情上装",
limitNum = 1,
beginTime = v61,
order = 62,
itemIds = {
510350
}
},
[5195] = {
commodityId = 5195,
commodityName = "海岛风情下装",
limitNum = 1,
beginTime = v61,
order = 63,
itemIds = {
520233
}
},
[5196] = {
commodityId = 5196,
commodityName = "海岛风情手套",
limitNum = 1,
beginTime = v61,
order = 64,
itemIds = {
530209
}
},
[5197] = {
commodityId = 5197,
commodityName = "花舞樱樱上装",
limitNum = 1,
beginTime = v61,
order = 65,
itemIds = {
510351
}
},
[5198] = {
commodityId = 5198,
commodityName = "花舞樱樱下装",
limitNum = 1,
beginTime = v61,
order = 66,
itemIds = {
520234
}
},
[5199] = {
commodityId = 5199,
commodityName = "花舞樱樱手套",
limitNum = 1,
beginTime = v61,
order = 67,
itemIds = {
530210
}
},
[5200] = {
commodityId = 5200,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v61,
endTime = {
seconds = 1747324799
},
order = 227,
itemIds = {
725002
}
},
[5201] = {
commodityId = 5201,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v61,
endTime = {
seconds = 1747324799
},
order = 228,
itemIds = {
725004
}
},
[5202] = {
commodityId = 5202,
price = 3,
limitNum = 10,
beginTime = v61,
endTime = {
seconds = 1747324799
},
order = 229,
itemIds = v45
},
[5203] = {
commodityId = 5203,
commodityName = "林小泽",
price = 60,
limitNum = 1,
beginTime = v62,
order = 1,
itemIds = {
403190
}
},
[5204] = {
commodityId = 5204,
commodityName = "抬杠小包",
price = 40,
limitNum = 1,
beginTime = v62,
order = 14,
itemIds = {
620910
}
},
[5205] = {
commodityId = 5205,
commodityName = "初日眼镜",
price = 40,
limitNum = 1,
beginTime = v62,
order = 15,
itemIds = {
610397
}
},
[5206] = {
commodityId = 5206,
commodityName = "甜蜜梦乡上装",
limitNum = 1,
beginTime = v62,
order = 38,
itemIds = {
510371
}
},
[5207] = {
commodityId = 5207,
commodityName = "甜蜜梦乡下装",
limitNum = 1,
beginTime = v62,
order = 39,
itemIds = {
520246
}
},
[5208] = {
commodityId = 5208,
commodityName = "甜蜜梦乡手套",
limitNum = 1,
beginTime = v62,
order = 40,
itemIds = {
530222
}
},
[5209] = {
commodityId = 5209,
commodityName = "花间睡服上装",
limitNum = 1,
beginTime = v62,
order = 41,
itemIds = {
510372
}
},
[5210] = {
commodityId = 5210,
commodityName = "花间睡服下装",
limitNum = 1,
beginTime = v62,
order = 42,
itemIds = {
520247
}
},
[5211] = {
commodityId = 5211,
commodityName = "花间睡服手套",
limitNum = 1,
beginTime = v62,
order = 43,
itemIds = {
530223
}
},
[5212] = {
commodityId = 5212,
commodityName = "星河入梦上装",
limitNum = 1,
beginTime = v62,
order = 44,
itemIds = {
510373
}
},
[5213] = {
commodityId = 5213,
commodityName = "星河入梦下装",
limitNum = 1,
beginTime = v62,
order = 45,
itemIds = {
520248
}
},
[5214] = {
commodityId = 5214,
commodityName = "星河入梦手套",
limitNum = 1,
beginTime = v62,
order = 46,
itemIds = {
530224
}
},
[5215] = {
commodityId = 5215,
commodityName = "无贝不宝上装",
limitNum = 1,
beginTime = v62,
order = 47,
itemIds = {
510375
}
},
[5216] = {
commodityId = 5216,
commodityName = "无贝不宝下装",
limitNum = 1,
beginTime = v62,
order = 48,
itemIds = {
520249
}
},
[5217] = {
commodityId = 5217,
commodityName = "无贝不宝手套",
limitNum = 1,
beginTime = v62,
order = 49,
itemIds = {
530225
}
},
[5218] = {
commodityId = 5218,
commodityName = "无宝不贝上装",
limitNum = 1,
beginTime = v62,
order = 50,
itemIds = {
510376
}
},
[5219] = {
commodityId = 5219,
commodityName = "无宝不贝下装",
limitNum = 1,
beginTime = v62,
order = 51,
itemIds = {
520250
}
},
[5220] = {
commodityId = 5220,
commodityName = "无宝不贝手套",
limitNum = 1,
beginTime = v62,
order = 52,
itemIds = {
530226
}
},
[5221] = {
commodityId = 5221,
commodityName = "璀璨烟花棒",
price = 3,
limitNum = 10,
beginTime = v62,
endTime = {
seconds = 1752163199
},
order = 230,
itemIds = {
725002
}
},
[5222] = {
commodityId = 5222,
commodityName = "水蜜桃泡泡棒",
price = 3,
limitNum = 10,
beginTime = v62,
endTime = {
seconds = 1752163199
},
order = 231,
itemIds = {
725004
}
},
[5223] = {
commodityId = 5223,
price = 3,
limitNum = 10,
beginTime = v62,
endTime = {
seconds = 1752163199
},
order = 232,
itemIds = v45
},
[7001] = {
mallId = 29,
commodityId = 7001,
commodityName = "致命啄客",
coinType = 101,
price = 600,
limitNum = 1,
gender = 0,
itemIds = {
400190
}
},
[7002] = {
mallId = 29,
commodityId = 7002,
commodityName = "星愿币",
coinType = 101,
price = 10,
limitNum = 6,
gender = 0,
itemIds = {
2
}
},
[7003] = {
mallId = 29,
commodityId = 7003,
commodityName = "深度学习",
coinType = 101,
price = 450,
limitNum = 1,
gender = 0,
itemIds = {
720017
}
},
[7004] = {
mallId = 29,
commodityId = 7004,
commodityName = "含情脉脉",
coinType = 101,
price = 450,
limitNum = 1,
gender = 0,
itemIds = {
710011
}
},
[7005] = {
mallId = 29,
commodityId = 7005,
commodityName = "巡游新星",
coinType = 101,
price = 300,
limitNum = 1,
gender = 0,
itemIds = {
840009
}
},
[7006] = {
mallId = 29,
commodityId = 7006,
commodityName = "闪电封锁",
coinType = 101,
price = 300,
limitNum = 1,
gender = 0,
itemIds = {
820001
}
},
[7007] = {
mallId = 29,
commodityId = 7007,
commodityName = "小熊爪",
coinType = 101,
price = 250,
limitNum = 1,
gender = 0,
itemIds = {
630001
}
},
[7008] = {
mallId = 29,
commodityId = 7008,
commodityName = "赛博墨镜",
coinType = 101,
price = 250,
limitNum = 1,
gender = 0,
itemIds = {
610005
}
},
[7009] = {
mallId = 29,
commodityId = 7009,
commodityName = "芝士桃桃上装",
coinType = 101,
price = 150,
limitNum = 1,
gender = 0,
itemIds = {
510041
}
},
[7010] = {
mallId = 29,
commodityId = 7010,
commodityName = "芝士桃桃下装",
coinType = 101,
price = 150,
limitNum = 1,
gender = 0,
itemIds = {
520031
}
},
[7011] = {
mallId = 29,
commodityId = 7011,
commodityName = "清爽运动上装",
coinType = 101,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
510008
}
},
[7012] = {
mallId = 29,
commodityId = 7012,
commodityName = "清爽运动下装",
coinType = 101,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
520008
}
},
[7013] = {
mallId = 29,
commodityId = 7013,
commodityName = "清爽运动手套",
coinType = 101,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
530015
}
},
[7014] = {
mallId = 29,
commodityId = 7014,
commodityName = v13,
coinType = 101,
price = 30,
limitNum = 99,
gender = 0,
itemIds = v43
},
[7015] = {
mallId = 29,
commodityId = 7015,
commodityName = "云朵币",
coinType = 101,
price = 5,
limitNum = 20,
gender = 0,
itemIds = {
6
},
itemNums = {
5
}
},
[7019] = {
mallId = 30,
commodityId = 7019,
commodityName = "篮球少年手套",
coinType = 101,
price = 100,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
530004
}
},
[7020] = {
mallId = 30,
commodityId = 7020,
commodityName = "篮球少年下装",
coinType = 101,
price = 360,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
520023
}
},
[7021] = {
mallId = 30,
commodityId = 7021,
commodityName = "篮球少年上装",
coinType = 101,
price = 360,
limitNum = 1,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
510025
}
},
[7022] = {
mallId = 30,
commodityId = 7022,
commodityName = "饰品调色盘",
coinType = 101,
price = 72,
limitNum = 5,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
200008
}
},
[7023] = {
mallId = 30,
commodityId = 7023,
commodityName = "云朵币",
coinType = 101,
price = 36,
limitNum = 10,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
6
},
itemNums = {
10
}
},
[7024] = {
mallId = 30,
commodityId = 7024,
commodityName = v12,
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = v44
},
[7025] = {
mallId = 30,
commodityId = 7025,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = {
725003
}
},
[7026] = {
mallId = 30,
commodityId = 7026,
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = v45
},
[7027] = {
mallId = 30,
commodityId = 7027,
commodityName = v13,
coinType = 101,
price = 50,
limitNum = 100,
beginTime = v46,
endTime = v66,
gender = 0,
itemIds = v43
},
[7028] = {
mallId = 30,
commodityId = 7028,
commodityName = "致命啄客",
coinType = 101,
price = 1000,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
400190
}
},
[7029] = {
mallId = 30,
commodityId = 7029,
commodityName = "深度学习",
coinType = 101,
price = 500,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
720017
}
},
[7030] = {
mallId = 30,
commodityId = 7030,
commodityName = "哄不好头饰",
coinType = 101,
price = 400,
limitNum = 1,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
630004
}
},
[7031] = {
mallId = 30,
commodityId = 7031,
commodityName = v12,
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = v44
},
[7032] = {
mallId = 30,
commodityId = 7032,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = {
725003
}
},
[7033] = {
mallId = 30,
commodityId = 7033,
coinType = 101,
price = 36,
limitNum = 20,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = v45
},
[7034] = {
mallId = 30,
commodityId = 7034,
commodityName = v13,
coinType = 101,
price = 50,
limitNum = 100,
beginTime = v47,
endTime = v67,
gender = 0,
itemIds = v43
},
[7035] = {
mallId = 30,
commodityId = 7035,
commodityName = "摩羯星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
401250
}
},
[7036] = {
mallId = 30,
commodityId = 7036,
commodityName = "致命啄客",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
400190
}
},
[7037] = {
mallId = 30,
commodityId = 7037,
commodityName = "彩虹漩涡",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
720108
}
},
[7038] = {
mallId = 30,
commodityId = 7038,
commodityName = "深度学习",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
720017
}
},
[7039] = {
mallId = 30,
commodityId = 7039,
commodityName = "惊叹号",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
630072
}
},
[7040] = {
mallId = 30,
commodityId = 7040,
commodityName = "哄不好头饰",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
630004
}
},
[7041] = {
mallId = 30,
commodityId = 7041,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = v44
},
[7042] = {
mallId = 30,
commodityId = 7042,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = {
725003
}
},
[7043] = {
mallId = 30,
commodityId = 7043,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = v45
},
[7044] = {
mallId = 30,
commodityId = 7044,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 100,
beginTime = v48,
endTime = v68,
gender = 0,
itemIds = v43
},
[7045] = {
mallId = 30,
commodityId = 7045,
commodityName = "水瓶星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
401670
}
},
[7046] = {
mallId = 30,
commodityId = 7046,
commodityName = "摩羯星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
401250
}
},
[7047] = {
mallId = 30,
commodityId = 7047,
commodityName = "致命啄客",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
400190
}
},
[7048] = {
mallId = 30,
commodityId = 7048,
commodityName = "地板动作",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
720141
}
},
[7049] = {
mallId = 30,
commodityId = 7049,
commodityName = "彩虹漩涡",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
720108
}
},
[7050] = {
mallId = 30,
commodityId = 7050,
commodityName = "深度学习",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
720017
}
},
[7051] = {
mallId = 30,
commodityId = 7051,
commodityName = "海王星",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
630103
}
},
[7052] = {
mallId = 30,
commodityId = 7052,
commodityName = "惊叹号",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
630072
}
},
[7053] = {
mallId = 30,
commodityId = 7053,
commodityName = "哄不好头饰",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
630004
}
},
[7054] = {
mallId = 30,
commodityId = 7054,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
200008
}
},
[7055] = {
mallId = 30,
commodityId = 7055,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = v44
},
[7056] = {
mallId = 30,
commodityId = 7056,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = {
725003
}
},
[7057] = {
mallId = 30,
commodityId = 7057,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = v45
},
[7058] = {
mallId = 30,
commodityId = 7058,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 100,
beginTime = v49,
endTime = v69,
gender = 0,
itemIds = v43
},
[7059] = {
mallId = 30,
commodityId = 7059,
commodityName = "双鱼星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
401940
}
},
[7060] = {
mallId = 30,
commodityId = 7060,
commodityName = "水瓶星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
401670
}
},
[7061] = {
mallId = 30,
commodityId = 7061,
commodityName = "摩羯星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
401250
}
},
[7062] = {
mallId = 30,
commodityId = 7062,
commodityName = "致命啄客",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
400190
}
},
[7063] = {
mallId = 30,
commodityId = 7063,
commodityName = "坚持自律",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720165
}
},
[7064] = {
mallId = 30,
commodityId = 7064,
commodityName = "地板动作",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720141
}
},
[7065] = {
mallId = 30,
commodityId = 7065,
commodityName = "彩虹漩涡",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720108
}
},
[7066] = {
mallId = 30,
commodityId = 7066,
commodityName = "深度学习",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
720017
}
},
[7067] = {
mallId = 30,
commodityId = 7067,
commodityName = "整点薯条",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
620249
}
},
[7068] = {
mallId = 30,
commodityId = 7068,
commodityName = "海王星",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
630103
}
},
[7069] = {
mallId = 30,
commodityId = 7069,
commodityName = "惊叹号",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
630072
}
},
[7070] = {
mallId = 30,
commodityId = 7070,
commodityName = "哄不好头饰",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
630004
}
},
[7071] = {
mallId = 30,
commodityId = 7071,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
200008
}
},
[7072] = {
mallId = 30,
commodityId = 7072,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = v44
},
[7073] = {
mallId = 30,
commodityId = 7073,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = {
725003
}
},
[7074] = {
mallId = 30,
commodityId = 7074,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = v45
},
[7075] = {
mallId = 30,
commodityId = 7075,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 100,
beginTime = v50,
endTime = v70,
gender = 0,
itemIds = v43
},
[7076] = {
mallId = 30,
commodityId = 7076,
commodityName = "白羊星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = {
402240
}
},
[7077] = {
mallId = 30,
commodityId = 7077,
commodityName = "仰卧高手",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = {
720636
}
},
[7078] = {
mallId = 30,
commodityId = 7078,
commodityName = "可乐背包",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = {
620334
}
},
[7079] = {
mallId = 30,
commodityId = 7079,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = {
200008
}
},
[7080] = {
mallId = 30,
commodityId = 7080,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = v44
},
[7081] = {
mallId = 30,
commodityId = 7081,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = {
725003
}
},
[7082] = {
mallId = 30,
commodityId = 7082,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = v45
},
[7083] = {
mallId = 30,
commodityId = 7083,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
gender = 0,
itemIds = v43
},
[7084] = {
mallId = 30,
commodityId = 7084,
commodityName = "金牛星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = {
402660
}
},
[7085] = {
mallId = 30,
commodityId = 7085,
commodityName = "热身运动",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = {
720674
}
},
[7086] = {
mallId = 30,
commodityId = 7086,
commodityName = "星乐杯",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = {
620401
}
},
[7087] = {
mallId = 30,
commodityId = 7087,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = {
200008
}
},
[7088] = {
mallId = 30,
commodityId = 7088,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = v44
},
[7089] = {
mallId = 30,
commodityId = 7089,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = {
725003
}
},
[7090] = {
mallId = 30,
commodityId = 7090,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = v45
},
[7091] = {
mallId = 30,
commodityId = 7091,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
gender = 0,
itemIds = v43
},
[7092] = {
mallId = 30,
commodityId = 7092,
commodityName = "天秤星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = {
403380
}
},
[7093] = {
mallId = 30,
commodityId = 7093,
commodityName = "拳击动作",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = {
720753
}
},
[7094] = {
mallId = 30,
commodityId = 7094,
commodityName = "炸鸡乐园",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = {
620450
}
},
[7095] = {
mallId = 30,
commodityId = 7095,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = {
200008
}
},
[7096] = {
mallId = 30,
commodityId = 7096,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = v44
},
[7097] = {
mallId = 30,
commodityId = 7097,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = {
725003
}
},
[7098] = {
mallId = 30,
commodityId = 7098,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = v45
},
[7099] = {
mallId = 30,
commodityId = 7099,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
gender = 0,
itemIds = v43
},
[7100] = {
mallId = 30,
commodityId = 7100,
commodityName = "天蝎星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = {
403730
}
},
[7101] = {
mallId = 30,
commodityId = 7101,
commodityName = "干饭步伐",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = {
720807
}
},
[7102] = {
mallId = 30,
commodityId = 7102,
commodityName = "棋王小帅",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = {
620536
}
},
[7103] = {
mallId = 30,
commodityId = 7103,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = {
200008
}
},
[7104] = {
mallId = 30,
commodityId = 7104,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = v44
},
[7105] = {
mallId = 30,
commodityId = 7105,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = {
725003
}
},
[7106] = {
mallId = 30,
commodityId = 7106,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = v45
},
[7107] = {
mallId = 30,
commodityId = 7107,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
gender = 0,
itemIds = v43
},
[7108] = {
mallId = 30,
commodityId = 7108,
commodityName = "射手星",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = {
404300
}
},
[7109] = {
mallId = 30,
commodityId = 7109,
commodityName = "优雅吃货",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = {
720874
}
},
[7110] = {
mallId = 30,
commodityId = 7110,
commodityName = "神之一将",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = {
620614
}
},
[7111] = {
mallId = 30,
commodityId = 7111,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = {
200008
}
},
[7112] = {
mallId = 30,
commodityId = 7112,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = v44
},
[7113] = {
mallId = 30,
commodityId = 7113,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = {
725003
}
},
[7114] = {
mallId = 30,
commodityId = 7114,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = v45
},
[7115] = {
mallId = 30,
commodityId = 7115,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = v63,
endTime = {
seconds = 1741881599
},
gender = 0,
itemIds = v43
},
[7116] = {
mallId = 30,
commodityId = 7116,
commodityName = "ESTP行动派",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = {
404710
}
},
[7117] = {
mallId = 30,
commodityId = 7117,
commodityName = "有点功夫",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = {
720938
}
},
[7118] = {
mallId = 30,
commodityId = 7118,
commodityName = "麻将幺鸡",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = {
620669
}
},
[7119] = {
mallId = 30,
commodityId = 7119,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = {
200008
}
},
[7120] = {
mallId = 30,
commodityId = 7120,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = v44
},
[7121] = {
mallId = 30,
commodityId = 7121,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = {
725003
}
},
[7122] = {
mallId = 30,
commodityId = 7122,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = v45
},
[7123] = {
mallId = 30,
commodityId = 7123,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
gender = 0,
itemIds = v43
},
[7124] = {
mallId = 30,
commodityId = 7124,
commodityName = "INFP小蝴蝶",
coinType = 101,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = {
410580
}
},
[7125] = {
mallId = 30,
commodityId = 7125,
commodityName = "慢摇美餐",
coinType = 101,
price = 600,
limitNum = 1,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = {
720988
}
},
[7126] = {
mallId = 30,
commodityId = 7126,
commodityName = "兵卒一枚",
coinType = 101,
price = 480,
limitNum = 1,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = {
620790
}
},
[7127] = {
mallId = 30,
commodityId = 7127,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = {
200008
}
},
[7128] = {
mallId = 30,
commodityId = 7128,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = v44
},
[7129] = {
mallId = 30,
commodityId = 7129,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = {
725003
}
},
[7130] = {
mallId = 30,
commodityId = 7130,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = v45
},
[7131] = {
mallId = 30,
commodityId = 7131,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1746115200
},
endTime = v71,
gender = 0,
itemIds = v43
},
[7132] = {
mallId = 30,
commodityId = 7132,
commodityName = "饰品调色盘",
coinType = 101,
price = 300,
limitNum = 2,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
gender = 0,
itemIds = {
200008
}
},
[7133] = {
mallId = 30,
commodityId = 7133,
commodityName = v12,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
gender = 0,
itemIds = v44
},
[7134] = {
mallId = 30,
commodityId = 7134,
commodityName = "薄荷绿泡泡棒",
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
gender = 0,
itemIds = {
725003
}
},
[7135] = {
mallId = 30,
commodityId = 7135,
coinType = 101,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
gender = 0,
itemIds = v45
},
[7136] = {
mallId = 30,
commodityId = 7136,
commodityName = v13,
coinType = 101,
price = 60,
limitNum = 60,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
gender = 0,
itemIds = v43
},
[8001] = {
mallId = 33,
commodityId = 8001,
commodityName = "月光女神",
coinType = 203,
price = 320,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 1,
gender = 0,
itemIds = {
400630
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8002] = {
mallId = 33,
commodityId = 8002,
commodityName = "月光女神面纱",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 2,
gender = 0,
itemIds = {
610044
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8003] = {
mallId = 33,
commodityId = 8003,
commodityName = "月光权杖",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 3,
gender = 0,
itemIds = {
620088
}
},
[8004] = {
mallId = 33,
commodityId = 8004,
commodityName = "悬浮星球",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 4,
gender = 0,
itemIds = {
630044
}
},
[8005] = {
mallId = 33,
commodityId = 8005,
commodityName = "四叶草精灵",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 5,
gender = 0,
itemIds = {
401060
}
},
[8006] = {
mallId = 33,
commodityId = 8006,
commodityName = "魔法师哈奇",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 6,
gender = 0,
itemIds = {
401040
},
AvailableTips = 1
},
[8007] = {
mallId = 33,
commodityId = 8007,
commodityName = "复古单片镜",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 7,
gender = 0,
itemIds = {
610027
}
},
[8008] = {
mallId = 33,
commodityId = 8008,
commodityName = "蜜罐儿",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 8,
gender = 0,
itemIds = {
620104
}
},
[8009] = {
mallId = 33,
commodityId = 8009,
commodityName = "橄榄花环",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 9,
gender = 0,
itemIds = {
630025
}
},
[8010] = {
mallId = 33,
commodityId = 8010,
commodityName = "炫彩独角兽",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 10,
gender = 0,
itemIds = {
401200
},
AvailableTips = 1
},
[8011] = {
mallId = 33,
commodityId = 8011,
commodityName = "蝶舞幽梦",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
order = 11,
gender = 0,
itemIds = {
840053
},
AvailableTips = 1
},
[8012] = {
mallId = 33,
commodityId = 8012,
commodityName = "星愿币*1",
coinType = 203,
price = 1,
limitNum = 9999,
order = 12,
gender = 0,
itemIds = {
2
}
},
[8013] = {
mallId = 33,
commodityId = 8013,
commodityName = "荧光棒2",
coinType = 203,
price = 10,
limitNum = 99,
order = 13,
gender = 0,
itemIds = {
725006
}
},
[8014] = {
mallId = 101,
commodityId = 8014,
commodityName = "黑暗王子",
coinType = 203,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
400300
}
},
[8015] = {
mallId = 101,
commodityId = 8015,
commodityName = "蓝幽夜弓",
coinType = 203,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
620020
}
},
[8016] = {
mallId = 101,
commodityId = 8016,
commodityName = "舞会假面",
coinType = 203,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
610036
}
},
[8017] = {
mallId = 101,
commodityId = 8017,
commodityName = "使魔",
coinType = 203,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
630092
}
},
[8018] = {
mallId = 101,
commodityId = 8018,
commodityName = "星光游侠 艾莉娜",
coinType = 203,
price = 80,
limitNum = 1,
jumpId = 707,
gender = 0,
itemIds = {
401810
},
AvailableTips = 1
},
[8019] = {
mallId = 101,
commodityId = 8019,
commodityName = "皮皮火 泰比",
coinType = 203,
price = 80,
limitNum = 1,
jumpId = 707,
gender = 0,
itemIds = {
401820
},
AvailableTips = 1
},
[8020] = {
mallId = 101,
commodityId = 8020,
commodityName = "夜猫子眼镜",
coinType = 203,
price = 40,
limitNum = 1,
jumpId = 707,
gender = 0,
itemIds = {
610096
},
AvailableTips = 1
},
[8021] = {
mallId = 101,
commodityId = 8021,
commodityName = "星光之矢",
coinType = 203,
price = 40,
limitNum = 1,
jumpId = 707,
gender = 0,
itemIds = {
620176
},
AvailableTips = 1
},
[8022] = {
mallId = 101,
commodityId = 8022,
commodityName = "草莓塔塔",
coinType = 203,
price = 40,
limitNum = 1,
jumpId = 707,
gender = 0,
itemIds = {
630127
},
AvailableTips = 1
},
[8023] = {
mallId = 101,
commodityId = 8023,
commodityName = "观星者",
coinType = 203,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
401730
},
AvailableTips = 1
},
[8024] = {
mallId = 101,
commodityId = 8024,
commodityName = "雪夜星辰",
coinType = 203,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
840081
}
},
[8025] = {
mallId = 101,
commodityId = 8025,
commodityName = "星愿币",
coinType = 203,
price = 1,
limitNum = 9999,
gender = 0,
itemIds = {
2
}
},
[8087] = {
mallId = 34,
commodityId = 8087,
commodityName = "夜莺之声 艾娜",
coinType = 205,
price = 180,
limitNum = 1,
recommendTag = {
1
},
beginTime = {
seconds = 1745942400
},
order = 1,
gender = 0,
itemIds = {
404600
}
},
[8084] = {
mallId = 34,
commodityId = 8084,
commodityName = "执念清除师 离火",
coinType = 205,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1742572800
},
order = 2,
gender = 0,
itemIds = {
410340
}
},
[8081] = {
mallId = 34,
commodityId = 8081,
commodityName = "荆棘鸟 菲奥娜",
coinType = 205,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
order = 3,
gender = 0,
itemIds = {
404940
}
},
[8078] = {
mallId = 34,
commodityId = 8078,
commodityName = "糖果女巫",
coinType = 205,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1729872000
},
order = 4,
gender = 0,
itemIds = {
404060
}
},
[8076] = {
mallId = 34,
commodityId = 8076,
commodityName = "梦想收集者",
coinType = 205,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1729526400
},
order = 5,
gender = 0,
itemIds = {
403250
}
},
[8072] = {
mallId = 34,
commodityId = 8072,
commodityName = "浪漫笛音 笛娜",
coinType = 205,
price = 180,
limitNum = 1,
order = 6,
jumpId = 707,
gender = 0,
itemIds = {
402050
}
},
[8040] = {
mallId = 34,
commodityId = 8040,
commodityName = "木偶王子 安德尔",
coinType = 205,
price = 180,
limitNum = 1,
order = 7,
jumpId = 707,
gender = 0,
itemIds = {
401140
}
},
[8061] = {
mallId = 34,
commodityId = 8061,
commodityName = "天才甜点师 莱杰",
coinType = 205,
price = 180,
limitNum = 1,
order = 8,
jumpId = 707,
gender = 0,
itemIds = {
401620
}
},
[8085] = {
mallId = 34,
commodityId = 8085,
commodityName = "红尘铜障",
coinType = 205,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1742572800
},
order = 9,
gender = 0,
itemIds = {
610344
}
},
[8041] = {
mallId = 34,
commodityId = 8041,
commodityName = "龟蜜",
coinType = 205,
price = 40,
limitNum = 1,
order = 10,
gender = 0,
itemIds = {
400930
}
},
[8042] = {
mallId = 34,
commodityId = 8042,
commodityName = "蘑咕咕",
coinType = 205,
price = 40,
limitNum = 1,
order = 11,
gender = 0,
itemIds = {
400160
}
},
[8088] = {
mallId = 34,
commodityId = 8088,
commodityName = "几何视界",
coinType = 205,
price = 40,
limitNum = 1,
recommendTag = {
1
},
beginTime = {
seconds = 1745942400
},
order = 12,
gender = 0,
itemIds = {
610291
}
},
[8086] = {
mallId = 34,
commodityId = 8086,
commodityName = "裁梦小剪",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1742572800
},
order = 13,
jumpId = 707,
gender = 0,
itemIds = {
620702
}
},
[8082] = {
mallId = 34,
commodityId = 8082,
commodityName = "社恐小蛛",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
order = 14,
jumpId = 707,
gender = 0,
itemIds = {
630464
}
},
[8079] = {
mallId = 34,
commodityId = 8079,
commodityName = "南瓜糖盒",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1729872000
},
order = 15,
gender = 0,
itemIds = {
620553
}
},
[8077] = {
mallId = 34,
commodityId = 8077,
commodityName = "小粉猫",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1729526400
},
order = 16,
gender = 0,
itemIds = {
620353
}
},
[8043] = {
mallId = 34,
commodityId = 8043,
commodityName = "漂泊之钥匙",
coinType = 205,
price = 40,
limitNum = 1,
order = 17,
gender = 0,
itemIds = {
620131
}
},
[8044] = {
mallId = 34,
commodityId = 8044,
commodityName = "狼系少年",
coinType = 205,
price = 40,
limitNum = 1,
order = 18,
gender = 0,
itemIds = {
630035
}
},
[8045] = {
mallId = 34,
commodityId = 8045,
commodityName = "苹果之芯",
coinType = 205,
price = 40,
limitNum = 1,
order = 19,
gender = 0,
itemIds = {
620073
}
},
[8073] = {
mallId = 34,
commodityId = 8073,
commodityName = "浮光绘羽",
coinType = 205,
price = 40,
limitNum = 1,
order = 20,
gender = 0,
itemIds = {
620229
}
},
[8083] = {
mallId = 34,
commodityId = 8083,
commodityName = "不行不行",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
order = 21,
gender = 0,
itemIds = {
720974
}
},
[8080] = {
mallId = 34,
commodityId = 8080,
commodityName = "抓小星星",
coinType = 205,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1729872000
},
order = 22,
gender = 0,
itemIds = {
720842
}
},
[8049] = {
mallId = 34,
commodityId = 8049,
commodityName = "水枪警告",
coinType = 205,
price = 40,
limitNum = 1,
order = 23,
gender = 0,
itemIds = {
720055
}
},
[8050] = {
mallId = 34,
commodityId = 8050,
commodityName = "瑟瑟发抖",
coinType = 205,
price = 40,
limitNum = 1,
order = 24,
gender = 0,
itemIds = {
720002
}
},
[8062] = {
mallId = 34,
commodityId = 8062,
commodityName = "木偶王子动态头像框",
coinType = 205,
price = 40,
limitNum = 1,
order = 25,
gender = 0,
itemIds = {
840061
}
},
[8063] = {
mallId = 34,
commodityId = 8063,
commodityName = "木偶王子昵称框",
coinType = 205,
price = 40,
limitNum = 1,
order = 26,
gender = 0,
itemIds = {
820041
}
},
[8046] = {
mallId = 34,
commodityId = 8046,
commodityName = "金钱眼镜",
coinType = 205,
price = 20,
limitNum = 1,
order = 27,
gender = 0,
itemIds = {
610078
}
},
[8047] = {
mallId = 34,
commodityId = 8047,
commodityName = "像素眼镜",
coinType = 205,
price = 20,
limitNum = 1,
order = 28,
gender = 0,
itemIds = {
610077
}
},
[8048] = {
mallId = 34,
commodityId = 8048,
commodityName = "黑框眼镜",
coinType = 205,
price = 20,
limitNum = 1,
order = 29,
gender = 0,
itemIds = {
610043
}
},
[8051] = {
mallId = 34,
commodityId = 8051,
commodityName = "华丽后空翻",
coinType = 205,
price = 20,
limitNum = 1,
order = 30,
gender = 0,
itemIds = {
720111
}
},
[8064] = {
mallId = 34,
commodityId = 8064,
commodityName = "木偶王子头像",
coinType = 205,
price = 20,
limitNum = 1,
order = 31,
gender = 0,
itemIds = {
860036
}
},
[8065] = {
mallId = 34,
commodityId = 8065,
commodityName = "蘑菇菇头像",
coinType = 205,
price = 20,
limitNum = 1,
order = 32,
gender = 0,
itemIds = {
860035
}
},
[8066] = {
mallId = 34,
commodityId = 8066,
commodityName = "龟蜜头像",
coinType = 205,
price = 20,
limitNum = 1,
order = 33,
gender = 0,
itemIds = {
860034
}
},
[8067] = {
mallId = 34,
commodityId = 8067,
commodityName = "表情-百发百中",
coinType = 205,
price = 20,
limitNum = 1,
order = 34,
gender = 0,
itemIds = {
710134
}
},
[8068] = {
mallId = 34,
commodityId = 8068,
commodityName = "表情-你可真棒",
coinType = 205,
price = 20,
limitNum = 1,
order = 35,
gender = 0,
itemIds = {
710135
}
},
[8069] = {
mallId = 34,
commodityId = 8069,
commodityName = "表情-我想开了",
coinType = 205,
price = 20,
limitNum = 1,
order = 36,
gender = 0,
itemIds = {
710136
}
},
[8070] = {
mallId = 34,
commodityId = 8070,
commodityName = "表情-危",
coinType = 205,
price = 20,
limitNum = 1,
order = 37,
gender = 0,
itemIds = {
710137
}
},
[8071] = {
mallId = 34,
commodityId = 8071,
commodityName = "表情-急了急了",
coinType = 205,
price = 20,
limitNum = 1,
order = 38,
gender = 0,
itemIds = {
710138
}
},
[8074] = {
mallId = 34,
commodityId = 8074,
commodityName = "晕倒",
coinType = 205,
price = 20,
limitNum = 1,
order = 39,
gender = 0,
itemIds = {
720133
}
},
[8075] = {
mallId = 34,
commodityId = 8075,
commodityName = "锤子舞",
coinType = 205,
price = 20,
limitNum = 1,
order = 40,
gender = 0,
itemIds = {
720135
}
},
[8100] = {
mallId = 36,
commodityId = 8100,
commodityName = "蓝色配饰—糖葫芦",
coinType = 2039,
price = 600,
limitNum = 1,
gender = 0,
itemIds = {
620109
}
},
[8101] = {
mallId = 36,
commodityId = 8101,
commodityName = "头像框 书写青春",
coinType = 2039,
price = 400,
limitNum = 1,
gender = 0,
itemIds = {
840051
}
},
[8102] = {
mallId = 36,
commodityId = 8102,
commodityName = "心心糖果",
coinType = 2039,
price = 50,
limitNum = 5,
gender = 0,
itemIds = {
200015
}
},
[8103] = {
mallId = 36,
commodityId = 8103,
commodityName = "泡泡枪",
coinType = 2039,
price = 50,
limitNum = 8,
gender = 0,
itemIds = {
725101
}
},
[8104] = {
mallId = 36,
commodityId = 8104,
commodityName = "钞票枪",
coinType = 2039,
price = 50,
limitNum = 8,
gender = 0,
itemIds = {
725102
}
},
[8105] = {
mallId = 36,
commodityId = 8105,
commodityName = v12,
coinType = 2039,
price = 35,
limitNum = 10,
gender = 0,
itemIds = v44,
packageIcon = "Review_Isolated"
},
[8150] = {
mallId = 41,
commodityId = 8150,
commodityName = "初心礼盒",
coinType = 2043,
price = 1,
limitNum = 9999,
gender = 0,
itemIds = {
321008
},
packageIcon = "Review_Isolated"
},
[8201] = {
mallId = 38,
commodityId = 8201,
commodityName = "紫配-龙崽福袋",
coinType = 2033,
price = 300,
limitNum = 1,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = {
620116
},
packageIcon = "Review_Isolated"
},
[8202] = {
mallId = 38,
commodityId = 8202,
commodityName = "绿装-樱桃甜心",
coinType = 2033,
price = 150,
limitNum = 1,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = {
510078
},
packageIcon = "Review_Isolated"
},
[8203] = {
mallId = 38,
commodityId = 8203,
commodityName = v12,
coinType = 2033,
price = 15,
limitNum = 10,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = v44
},
[8204] = {
mallId = 38,
commodityId = 8204,
commodityName = "薄荷绿泡泡棒",
coinType = 2033,
price = 15,
limitNum = 10,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = {
725003
}
},
[8205] = {
mallId = 38,
commodityId = 8205,
coinType = 2033,
price = 15,
limitNum = 10,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = v45
},
[8206] = {
mallId = 38,
commodityId = 8206,
commodityName = v13,
coinType = 2033,
price = 15,
limitNum = 10,
beginTime = v64,
endTime = v69,
gender = 0,
itemIds = v43
},
[8207] = {
mallId = 38,
commodityId = 8207,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 1,
gender = 0,
itemIds = {
402810
}
},
[8208] = {
mallId = 38,
commodityId = 8208,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 6,
gender = 0,
itemIds = {
510177
}
},
[8209] = {
mallId = 38,
commodityId = 8209,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 7,
gender = 0,
itemIds = {
510078
},
packageIcon = "Review_Isolated"
},
[8210] = {
mallId = 38,
commodityId = 8210,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 2,
gender = 0,
itemIds = {
620116
},
packageIcon = "Review_Isolated"
},
[8211] = {
mallId = 38,
commodityId = 8211,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 10,
beginTime = v64,
endTime = v69,
order = 8,
gender = 0,
itemIds = {
2
},
itemNums = {
2
},
packageIcon = "Review_Isolated"
},
[8212] = {
mallId = 38,
commodityId = 8212,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 10,
beginTime = v64,
endTime = v69,
order = 9,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
},
packageIcon = "Review_Isolated"
},
[8213] = {
mallId = 38,
commodityId = 8213,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 10,
beginTime = v64,
endTime = v69,
order = 10,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
},
packageIcon = "Review_Isolated"
},
[8214] = {
mallId = 38,
commodityId = 8214,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 3,
gender = 0,
itemIds = {
510139
}
},
[8215] = {
mallId = 38,
commodityId = 8215,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 4,
gender = 0,
itemIds = {
520094
}
},
[8216] = {
mallId = 38,
commodityId = 8216,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 5,
gender = 0,
itemIds = {
530072
}
},
[8217] = {
mallId = 38,
commodityId = 8217,
commodityName = "蓝装-汉堡贝贝",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 1,
gender = 0,
itemIds = {
403540
}
},
[8218] = {
mallId = 38,
commodityId = 8218,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 2,
gender = 0,
itemIds = {
402810
}
},
[8219] = {
mallId = 38,
commodityId = 8219,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 3,
gender = 0,
itemIds = {
620116
}
},
[8220] = {
mallId = 38,
commodityId = 8220,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 4,
gender = 0,
itemIds = {
510177
}
},
[8221] = {
mallId = 38,
commodityId = 8221,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 5,
gender = 0,
itemIds = {
510078
}
},
[8222] = {
mallId = 38,
commodityId = 8222,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 6,
gender = 0,
itemIds = {
510139
}
},
[8223] = {
mallId = 38,
commodityId = 8223,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 7,
gender = 0,
itemIds = {
520094
}
},
[8224] = {
mallId = 38,
commodityId = 8224,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 8,
gender = 0,
itemIds = {
530072
}
},
[8225] = {
mallId = 38,
commodityId = 8225,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 9,
gender = 0,
itemIds = {
2
},
itemNums = {
2
}
},
[8226] = {
mallId = 38,
commodityId = 8226,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 10,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
}
},
[8227] = {
mallId = 38,
commodityId = 8227,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1731254399
},
order = 11,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
}
},
[8228] = {
mallId = 38,
commodityId = 8228,
commodityName = "蓝色手持物-生活记录者",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 1,
gender = 0,
itemIds = {
640083
}
},
[8229] = {
mallId = 38,
commodityId = 8229,
commodityName = "蓝装-汉堡贝贝",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 2,
gender = 0,
itemIds = {
403540
}
},
[8230] = {
mallId = 38,
commodityId = 8230,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 3,
gender = 0,
itemIds = {
402810
}
},
[8231] = {
mallId = 38,
commodityId = 8231,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 4,
gender = 0,
itemIds = {
620116
}
},
[8232] = {
mallId = 38,
commodityId = 8232,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 5,
gender = 0,
itemIds = {
510177
},
AvailableTips = 1
},
[8233] = {
mallId = 38,
commodityId = 8233,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 6,
gender = 0,
itemIds = {
510078
},
AvailableTips = 1
},
[8234] = {
mallId = 38,
commodityId = 8234,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 7,
gender = 0,
itemIds = {
510139
},
AvailableTips = 1
},
[8235] = {
mallId = 38,
commodityId = 8235,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 8,
gender = 0,
itemIds = {
520094
},
AvailableTips = 1
},
[8236] = {
mallId = 38,
commodityId = 8236,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 9,
gender = 0,
itemIds = {
530072
},
AvailableTips = 1
},
[8237] = {
mallId = 38,
commodityId = 8237,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 10,
gender = 0,
itemIds = {
2
},
itemNums = {
2
}
},
[8238] = {
mallId = 38,
commodityId = 8238,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 11,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
}
},
[8239] = {
mallId = 38,
commodityId = 8239,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
order = 12,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
}
},
[8240] = {
mallId = 38,
commodityId = 8240,
commodityName = "茶末末",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 1,
gender = 0,
itemIds = {
402260
}
},
[8241] = {
mallId = 38,
commodityId = 8241,
commodityName = "蓝色手持物-生活记录者",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 2,
gender = 0,
itemIds = {
640083
}
},
[8242] = {
mallId = 38,
commodityId = 8242,
commodityName = "蓝装-汉堡贝贝",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 3,
gender = 0,
itemIds = {
403540
}
},
[8243] = {
mallId = 38,
commodityId = 8243,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 4,
gender = 0,
itemIds = {
402810
}
},
[8244] = {
mallId = 38,
commodityId = 8244,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 5,
gender = 0,
itemIds = {
620116
}
},
[8245] = {
mallId = 38,
commodityId = 8245,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 6,
gender = 0,
itemIds = {
510177
},
AvailableTips = 1
},
[8246] = {
mallId = 38,
commodityId = 8246,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 7,
gender = 0,
itemIds = {
510078
}
},
[8247] = {
mallId = 38,
commodityId = 8247,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 8,
gender = 0,
itemIds = {
510139
}
},
[8248] = {
mallId = 38,
commodityId = 8248,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 9,
gender = 0,
itemIds = {
520094
}
},
[8249] = {
mallId = 38,
commodityId = 8249,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 10,
gender = 0,
itemIds = {
530072
}
},
[8250] = {
mallId = 38,
commodityId = 8250,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 11,
gender = 0,
itemIds = {
2
},
itemNums = {
2
}
},
[8251] = {
mallId = 38,
commodityId = 8251,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 12,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
}
},
[8252] = {
mallId = 38,
commodityId = 8252,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = v63,
endTime = {
seconds = 1738684799
},
order = 13,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
}
},
[8253] = {
mallId = 38,
commodityId = 8253,
commodityName = "蓝装-阿力力",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 1,
gender = 0,
itemIds = {
410520
}
},
[8254] = {
mallId = 38,
commodityId = 8254,
commodityName = "茶末末",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 2,
gender = 0,
itemIds = {
402260
}
},
[8255] = {
mallId = 38,
commodityId = 8255,
commodityName = "蓝色手持物-生活记录者",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 3,
gender = 0,
itemIds = {
640083
}
},
[8256] = {
mallId = 38,
commodityId = 8256,
commodityName = "蓝装-汉堡贝贝",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 4,
gender = 0,
itemIds = {
403540
}
},
[8257] = {
mallId = 38,
commodityId = 8257,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 5,
gender = 0,
itemIds = {
402810
}
},
[8258] = {
mallId = 38,
commodityId = 8258,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 6,
gender = 0,
itemIds = {
620116
}
},
[8259] = {
mallId = 38,
commodityId = 8259,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 7,
gender = 0,
itemIds = {
510177
},
AvailableTips = 1
},
[8260] = {
mallId = 38,
commodityId = 8260,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 8,
gender = 0,
itemIds = {
510078
}
},
[8261] = {
mallId = 38,
commodityId = 8261,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 9,
gender = 0,
itemIds = {
510139
}
},
[8262] = {
mallId = 38,
commodityId = 8262,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 10,
gender = 0,
itemIds = {
520094
}
},
[8263] = {
mallId = 38,
commodityId = 8263,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 11,
gender = 0,
itemIds = {
530072
}
},
[8264] = {
mallId = 38,
commodityId = 8264,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 12,
gender = 0,
itemIds = {
2
},
itemNums = {
2
}
},
[8265] = {
mallId = 38,
commodityId = 8265,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 13,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
}
},
[8266] = {
mallId = 38,
commodityId = 8266,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1744559999
},
order = 14,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
}
},
[8268] = {
mallId = 38,
commodityId = 8268,
commodityName = "蓝装-阿力力",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 1,
gender = 0,
itemIds = {
410520
}
},
[8269] = {
mallId = 38,
commodityId = 8269,
commodityName = "茶末末",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 2,
gender = 0,
itemIds = {
402260
}
},
[8270] = {
mallId = 38,
commodityId = 8270,
commodityName = "蓝色手持物-生活记录者",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 3,
gender = 0,
itemIds = {
640083
}
},
[8271] = {
mallId = 38,
commodityId = 8271,
commodityName = "蓝装-汉堡贝贝",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 4,
gender = 0,
itemIds = {
403540
}
},
[8272] = {
mallId = 38,
commodityId = 8272,
commodityName = "蓝装-糖小乐",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 5,
gender = 0,
itemIds = {
402810
}
},
[8273] = {
mallId = 38,
commodityId = 8273,
commodityName = "紫配-龙崽福袋",
coinType = 3159,
price = 240,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 6,
gender = 0,
itemIds = {
620116
}
},
[8274] = {
mallId = 38,
commodityId = 8274,
commodityName = "绿装-桃心小猫",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 7,
gender = 0,
itemIds = {
510177
},
AvailableTips = 1
},
[8275] = {
mallId = 38,
commodityId = 8275,
commodityName = "绿装-樱桃甜心",
coinType = 3159,
price = 150,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 8,
gender = 0,
itemIds = {
510078
}
},
[8276] = {
mallId = 38,
commodityId = 8276,
commodityName = "绿装-职业轻装上装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 9,
gender = 0,
itemIds = {
510139
},
AvailableTips = 1
},
[8277] = {
mallId = 38,
commodityId = 8277,
commodityName = "绿装-职业轻装下装",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 10,
gender = 0,
itemIds = {
520094
},
AvailableTips = 1
},
[8278] = {
mallId = 38,
commodityId = 8278,
commodityName = "绿装-职业轻装手套",
coinType = 3159,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 11,
gender = 0,
itemIds = {
530072
}
},
[8279] = {
mallId = 38,
commodityId = 8279,
commodityName = "星愿币*2",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 12,
gender = 0,
itemIds = {
2
},
itemNums = {
2
}
},
[8280] = {
mallId = 38,
commodityId = 8280,
commodityName = "排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 13,
gender = 0,
itemIds = {
200020
},
itemNums = {
5
}
},
[8281] = {
mallId = 38,
commodityId = 8281,
commodityName = "娱乐排位赛升星券",
coinType = 3159,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1747583999
},
order = 14,
gender = 0,
itemIds = {
203001
},
itemNums = {
5
}
},
[8301] = {
mallId = 102,
commodityId = 8301,
commodityName = "抱抱花苞",
coinType = 2073,
price = 150,
limitNum = 1,
gender = 0,
itemIds = {
620219
}
},
[8302] = {
mallId = 102,
commodityId = 8302,
commodityName = "表情-尊嘟假嘟",
coinType = 2073,
price = 50,
limitNum = 1,
gender = 0,
itemIds = {
710119
}
},
[8307] = {
mallId = 102,
commodityId = 8307,
commodityName = "找搭子",
coinType = 2073,
price = 150,
limitNum = 1,
gender = 0,
itemIds = {
630153
}
},
[8308] = {
mallId = 102,
commodityId = 8308,
commodityName = "表情-爱你哟",
coinType = 2073,
price = 50,
limitNum = 1,
gender = 0,
itemIds = {
710155
}
},
[8309] = {
mallId = 102,
commodityId = 8309,
commodityName = "云朵币",
coinType = 2073,
price = 5,
limitNum = 3,
gender = 0,
itemIds = {
6
},
itemNums = {
5
}
},
[8310] = {
mallId = 102,
commodityId = 8310,
commodityName = "星宝印章",
coinType = 2073,
price = 1,
limitNum = 15,
gender = 0,
itemIds = {
4
},
itemNums = {
40
}
},
[8311] = {
mallId = 102,
commodityId = 8311,
commodityName = v13,
coinType = 2073,
price = 5,
limitNum = 50,
gender = 0,
itemIds = v43
},
[8312] = {
mallId = 102,
commodityId = 8312,
commodityName = "泡泡枪",
coinType = 2073,
price = 5,
limitNum = 50,
gender = 0,
itemIds = {
725101
}
},
[8820] = {
mallId = 102,
commodityId = 8820,
commodityName = "樱花香皂",
coinType = 2073,
price = 150,
limitNum = 1,
gender = 0,
itemIds = {
620365
}
},
[8821] = {
mallId = 102,
commodityId = 8821,
commodityName = "表情-摸摸头",
coinType = 2073,
price = 50,
limitNum = 1,
gender = 0,
itemIds = {
711016
}
},
[9000] = {
mallId = 104,
commodityId = 9000,
commodityName = "动作-大小姐舞（折扣）",
coinType = 3134,
price = 35,
limitNum = 1,
beginTime = v64,
endTime = v69,
order = 110,
gender = 0,
itemIds = {
720198
}
},
[9001] = {
mallId = 104,
commodityId = 9001,
commodityName = "背饰-显眼包",
coinType = 3134,
price = 150,
limitNum = 1,
beginTime = v64,
order = 42,
gender = 0,
itemIds = {
620115
},
expireDays = {
3
}
},
[9002] = {
mallId = 104,
commodityId = 9002,
commodityName = "白玫荣耀",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = v64,
order = 58,
gender = 0,
itemIds = {
510039
},
expireDays = {
42
}
},
[9003] = {
mallId = 104,
commodityId = 9003,
commodityName = "少年时代上装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v64,
order = 59,
gender = 0,
itemIds = {
510080
},
expireDays = {
42
}
},
[9004] = {
mallId = 104,
commodityId = 9004,
commodityName = "少年时代下装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v64,
order = 60,
gender = 0,
itemIds = {
520052
},
expireDays = {
42
}
},
[9005] = {
mallId = 104,
commodityId = 9005,
commodityName = "少年时代手套",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = v64,
order = 61,
gender = 0,
itemIds = {
530031
}
},
[9006] = {
mallId = 104,
commodityId = 9006,
commodityName = "篮球少年上装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v64,
order = 62,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
510025
}
},
[9007] = {
mallId = 104,
commodityId = 9007,
commodityName = "篮球少年下装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v64,
order = 63,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
520023
}
},
[9008] = {
mallId = 104,
commodityId = 9008,
commodityName = "篮球少年手套",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = v64,
order = 64,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
530004
}
},
[9010] = {
mallId = 104,
commodityId = 9010,
commodityName = "白玫荣耀（试用3天）",
coinType = 3134,
price = 10,
limitNum = 1,
beginTime = v64,
order = 65,
gender = 0,
minVersion = "1.3.12.20",
itemIds = {
510039
}
},
[9011] = {
mallId = 104,
commodityId = 9011,
commodityName = "小红狐（试用42天）",
coinType = 3134,
price = 30,
limitNum = 1,
beginTime = v64,
order = 109,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
400470
}
},
[9012] = {
mallId = 104,
commodityId = 9012,
commodityName = "喵小困（试用42天）",
coinType = 3134,
price = 30,
limitNum = 1,
beginTime = v64,
order = 109,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
400130
}
},
[9013] = {
mallId = 104,
commodityId = 9013,
commodityName = "超新猩（试用42天）",
coinType = 3134,
price = 30,
limitNum = 1,
beginTime = v64,
order = 109,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
400030
}
},
[9014] = {
mallId = 104,
commodityId = 9014,
commodityName = "动作-大小姐舞（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = v50,
order = 41,
gender = 0,
minVersion = "1.3.12.20",
itemIds = {
720198
}
},
[9015] = {
mallId = 104,
commodityId = 9015,
commodityName = "荣誉新生上装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v50,
order = 50,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
510119
}
},
[9016] = {
mallId = 104,
commodityId = 9016,
commodityName = "荣誉新生下装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v50,
order = 51,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
520078
},
expireDays = {
3
}
},
[9017] = {
mallId = 104,
commodityId = 9017,
commodityName = "荣誉新生手套",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = v50,
order = 52,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
530056
}
},
[9018] = {
mallId = 104,
commodityId = 9018,
commodityName = "荣誉新生时装礼包",
coinType = 3134,
price = 10,
limitNum = 1,
beginTime = v50,
order = 53,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
310219
}
},
[9019] = {
mallId = 104,
commodityId = 9019,
commodityName = "青春筑梦上装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v50,
order = 54,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
510170
}
},
[9020] = {
mallId = 104,
commodityId = 9020,
commodityName = "青春筑梦下装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = v50,
order = 55,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
520118
}
},
[9021] = {
mallId = 104,
commodityId = 9021,
commodityName = "青春筑梦手套",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = v50,
order = 56,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
530095
}
},
[9022] = {
mallId = 104,
commodityId = 9022,
commodityName = "青春筑梦时装礼包",
coinType = 3134,
price = 10,
limitNum = 1,
beginTime = v50,
order = 57,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
310220
}
},
[9023] = {
mallId = 104,
commodityId = 9023,
commodityName = "江南风致",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 44,
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
510141
}
},
[9024] = {
mallId = 104,
commodityId = 9024,
commodityName = "江南风致（试用3天）",
coinType = 3134,
price = 10,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 45,
gender = 0,
minVersion = "1.3.12.69",
itemIds = {
510141
}
},
[9025] = {
mallId = 104,
commodityId = 9025,
commodityName = "素雅新装上装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 46,
gender = 0,
minVersion = "1.3.12.69",
itemIds = {
510112
}
},
[9026] = {
mallId = 104,
commodityId = 9026,
commodityName = "素雅新装下装",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 47,
gender = 0,
minVersion = "1.3.12.69",
itemIds = {
520072
}
},
[9027] = {
mallId = 104,
commodityId = 9027,
commodityName = "素雅新装手套",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 48,
gender = 0,
minVersion = "1.3.12.69",
itemIds = {
530052
}
},
[9028] = {
mallId = 104,
commodityId = 9028,
commodityName = "胖胖达",
coinType = 3134,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1718294400
},
order = 49,
gender = 0,
minVersion = "1.3.18.30",
itemIds = {
400140
},
packageIcon = "Review_Isolated"
},
[9029] = {
mallId = 104,
commodityId = 9029,
commodityName = "胧月公主（首周优惠）",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1721923199
},
order = 110,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
510188
}
},
[9030] = {
mallId = 104,
commodityId = 9030,
commodityName = "胧月公主（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1725033599
},
order = 39,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
510188
}
},
[9031] = {
mallId = 104,
commodityId = 9031,
commodityName = "表情-熹贵妃禁言",
coinType = 3134,
price = 30,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1725033599
},
order = 40,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
711015
}
},
[9032] = {
mallId = 104,
commodityId = 9032,
commodityName = "头饰-可爱贴贴",
coinType = 3134,
price = 150,
limitNum = 1,
beginTime = v56,
endTime = {
seconds = 1724947199
},
order = 36,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
630053
}
},
[9033] = {
mallId = 104,
commodityId = 9033,
commodityName = "甜心画师",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = v56,
endTime = {
seconds = 1724947199
},
order = 37,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
510065
},
packageIcon = "Review_Isolated"
},
[9034] = {
mallId = 104,
commodityId = 9034,
commodityName = "悠闲瓜瓜",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = v56,
endTime = {
seconds = 1724947199
},
order = 38,
gender = 0,
minVersion = "1.3.26.92",
itemIds = {
510066
}
},
[9035] = {
mallId = 104,
commodityId = 9035,
commodityName = "方特特会玩",
coinType = 3134,
price = 20,
limitNum = 1,
beginTime = v56,
endTime = {
seconds = 1724947199
},
order = 43,
gender = 0,
itemIds = {
850461
}
},
[9036] = {
mallId = 104,
commodityId = 9036,
commodityName = "来伊份小伊",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1727020800
},
endTime = {
seconds = 1728230399
},
order = 35,
gender = 0,
itemIds = {
510231
},
packageIcon = "Review_Isolated"
},
[9037] = {
mallId = 104,
commodityId = 9037,
commodityName = "动作-变身（折扣价）",
coinType = 3134,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732204799
},
order = 34,
gender = 0,
itemIds = {
720803
}
},
[9038] = {
mallId = 104,
commodityId = 9038,
commodityName = "动作-变身（原价）",
coinType = 3134,
price = 120,
limitNum = 1,
beginTime = {
seconds = 1732204800
},
order = 34,
gender = 0,
itemIds = {
720803
}
},
[9039] = {
mallId = 104,
commodityId = 9039,
commodityName = v13,
coinType = 3134,
price = 1,
limitNum = 10,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1734278399
},
order = 108,
gender = 0,
itemIds = v43
},
[9040] = {
mallId = 104,
commodityId = 9040,
commodityName = "心心糖果",
coinType = 3134,
price = 5,
limitNum = 10,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1734278399
},
order = 108,
gender = 0,
itemIds = {
200015
}
},
[9041] = {
mallId = 104,
commodityId = 9041,
commodityName = "心心宝瓶",
coinType = 3134,
price = 10,
limitNum = 10,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1734278399
},
order = 108,
gender = 0,
itemIds = {
200016
}
},
[9044] = {
mallId = 104,
commodityId = 9044,
commodityName = "星影券*60",
coinType = 3134,
price = 10,
limitNum = 1,
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1734278399
},
order = 12,
gender = 0,
itemIds = {
181991
},
itemNums = {
60
}
},
[9050] = {
mallId = 104,
commodityId = 9050,
commodityName = "星影券*60",
coinType = 3134,
price = 10,
limitNum = 6,
beginTime = {
seconds = 1734278400
},
endTime = {
seconds = 1747324799
},
order = 13,
gender = 0,
itemIds = {
181991
},
itemNums = {
60
}
},
[9051] = {
mallId = 104,
commodityId = 9051,
commodityName = "打榜星星*1",
coinType = 3134,
price = 1,
limitNum = 100,
beginTime = {
seconds = 1734278400
},
endTime = {
seconds = 1747324799
},
order = 13,
gender = 0,
itemIds = {
181992
}
},
[9060] = {
mallId = 104,
commodityId = 9060,
commodityName = "表情：心情真好",
coinType = 3134,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 5,
gender = 0,
itemIds = {
710266
}
},
[9061] = {
mallId = 104,
commodityId = 9061,
commodityName = "表情：好期待",
coinType = 3134,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 6,
gender = 0,
itemIds = {
710265
}
},
[9062] = {
mallId = 104,
commodityId = 9062,
commodityName = "表情：好害羞",
coinType = 3134,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 7,
gender = 0,
itemIds = {
710267
}
},
[9063] = {
mallId = 104,
commodityId = 9063,
commodityName = "表情：满满爱心",
coinType = 3134,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 8,
gender = 0,
minVersion = "********16",
itemIds = {
710268
}
},
[9064] = {
mallId = 104,
commodityId = 9064,
commodityName = "头像框：Hello Kitty的爱",
coinType = 3134,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 9,
gender = 0,
minVersion = "********16",
itemIds = {
840130
}
},
[9065] = {
mallId = 104,
commodityId = 9065,
commodityName = "称号：三丽鸥家族",
coinType = 3134,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 10,
gender = 0,
itemIds = {
850426
}
},
[9066] = {
mallId = 104,
commodityId = 9066,
commodityName = "昵称框：Hello Kitty蝴蝶结",
coinType = 3134,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
order = 11,
gender = 0,
itemIds = {
820088
}
},
[9067] = {
mallId = 104,
commodityId = 9067,
commodityName = "2月任务加速器",
coinType = 3134,
price = 20,
limitType = "MCL_WeeklyLimit",
limitNum = 5,
beginTime = {
seconds = 1738339200
},
endTime = {
seconds = 1740758399
},
order = 12,
gender = 0,
itemIds = {
200630
}
},
[9068] = {
mallId = 104,
commodityId = 9068,
commodityName = "福运鞭炮",
coinType = 3134,
price = 300,
limitNum = 1,
beginTime = {
seconds = 1738166400
},
order = 5,
gender = 0,
itemIds = {
620122
}
},
[9069] = {
mallId = 104,
commodityId = 9069,
commodityName = "平安喜乐时装礼盒（4折）",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 1740931199
},
order = 2,
gender = 0,
itemIds = {
316032
}
},
[9070] = {
mallId = 104,
commodityId = 9070,
commodityName = "平安喜乐时装礼盒（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1740931200
},
endTime = {
seconds = 1741881599
},
order = 2,
gender = 0,
itemIds = {
316032
}
},
[9071] = {
mallId = 104,
commodityId = 9071,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1740931199
},
order = 3,
gender = 0
},
[9072] = {
mallId = 104,
commodityId = 9072,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1740931200
},
endTime = {
seconds = 1741276799
},
order = 3,
gender = 0
},
[9073] = {
mallId = 104,
commodityId = 9073,
commodityName = "蜜语精灵时装礼盒（4折）",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1741276800
},
endTime = {
seconds = 1741535999
},
order = 2,
gender = 0,
itemIds = {
316033
}
},
[9074] = {
mallId = 104,
commodityId = 9074,
commodityName = "蜜语精灵时装礼盒（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1741536000
},
endTime = {
seconds = 1742486399
},
order = 2,
gender = 0,
itemIds = {
316033
}
},
[9075] = {
mallId = 104,
commodityId = 9075,
commodityName = "3月任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 5,
beginTime = {
seconds = 1741276800
},
endTime = {
seconds = 1741535999
},
order = 3,
gender = 0
},
[9076] = {
mallId = 104,
commodityId = 9076,
commodityName = "3月任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 5,
beginTime = {
seconds = 1741536000
},
endTime = {
seconds = 1741881599
},
order = 3,
gender = 0
},
[9077] = {
mallId = 104,
commodityId = 9077,
commodityName = "吟游之声时装礼盒（4折）",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742140799
},
order = 1,
gender = 0,
itemIds = {
316036
}
},
[9078] = {
mallId = 104,
commodityId = 9078,
commodityName = "吟游之声时装礼盒（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1743091199
},
order = 1,
gender = 0,
itemIds = {
316036
}
},
[9079] = {
mallId = 104,
commodityId = 9079,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742140799
},
order = 3,
gender = 0
},
[9080] = {
mallId = 104,
commodityId = 9080,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1742486399
},
order = 3,
gender = 0
},
[9081] = {
mallId = 104,
commodityId = 9081,
commodityName = "樱雨之华",
coinType = 3134,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1742745599
},
order = 1,
gender = 0,
itemIds = {
510330
}
},
[9082] = {
mallId = 104,
commodityId = 9082,
commodityName = "樱雨之华（原价）",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1743782399
},
order = 1,
gender = 0,
itemIds = {
510330
}
},
[9083] = {
mallId = 104,
commodityId = 9083,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1742745599
},
order = 3,
gender = 0
},
[9084] = {
mallId = 104,
commodityId = 9084,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1743091199
},
order = 3,
gender = 0
},
[9085] = {
mallId = 104,
commodityId = 9085,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1743955199
},
order = 3,
gender = 0
},
[9086] = {
mallId = 104,
commodityId = 9086,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1743955200
},
endTime = {
seconds = 1744300799
},
order = 3,
gender = 0
},
[9087] = {
mallId = 104,
commodityId = 9087,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1744559999
},
order = 3,
gender = 0
},
[9088] = {
mallId = 104,
commodityId = 9088,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1744560000
},
endTime = {
seconds = 1744905599
},
order = 3,
gender = 0
},
[9089] = {
mallId = 104,
commodityId = 9089,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745164799
},
order = 3,
gender = 0
},
[9090] = {
mallId = 104,
commodityId = 9090,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1745164800
},
endTime = {
seconds = 1745510399
},
order = 3,
gender = 0
},
[9091] = {
mallId = 104,
commodityId = 9091,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1745769599
},
order = 3,
gender = 0
},
[9092] = {
mallId = 104,
commodityId = 9092,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1745769600
},
endTime = {
seconds = 1746115199
},
order = 3,
gender = 0
},
[9700] = {
mallId = 104,
commodityId = 9700,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1746374399
},
order = 3,
gender = 0
},
[9701] = {
mallId = 104,
commodityId = 9701,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1746374400
},
endTime = {
seconds = 1746719999
},
order = 3,
gender = 0
},
[9702] = {
mallId = 104,
commodityId = 9702,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1746720000
},
endTime = {
seconds = 1746979199
},
order = 3,
gender = 0
},
[9703] = {
mallId = 104,
commodityId = 9703,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1746979200
},
endTime = {
seconds = 1747324799
},
order = 3,
gender = 0
},
[9704] = {
mallId = 104,
commodityId = 9704,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = v62,
endTime = {
seconds = 1747583999
},
order = 3,
gender = 0
},
[9705] = {
mallId = 104,
commodityId = 9705,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1747584000
},
endTime = {
seconds = 1747929599
},
order = 3,
gender = 0,
AvailableTips = 1
},
[9706] = {
mallId = 104,
commodityId = 9706,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1748188799
},
order = 3,
gender = 0,
AvailableTips = 1
},
[9707] = {
mallId = 104,
commodityId = 9707,
commodityName = "任务加速器（原价）",
coinType = 3134,
price = 20,
limitNum = 3,
beginTime = {
seconds = 1748188800
},
endTime = {
seconds = 1748534399
},
order = 3,
gender = 0
},
[9708] = {
mallId = 104,
commodityId = 9708,
commodityName = "任务加速器（5折）",
coinType = 3134,
price = 10,
limitNum = 3,
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1748793599
},
order = 3,
gender = 0
},
[9709] = {
mallId = 104,
commodityId = 9709,
commodityName = "甜心画师",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750003199
},
order = 1,
gender = 0,
itemIds = {
510065
}
},
[9710] = {
mallId = 104,
commodityId = 9710,
commodityName = "悠闲瓜瓜",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750003199
},
order = 2,
gender = 0,
itemIds = {
510066
},
AvailableTips = 1
},
[9711] = {
mallId = 104,
commodityId = 9711,
commodityName = "LULU猪围裙",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750003199
},
order = 3,
gender = 0,
itemIds = {
510230
},
AvailableTips = 1
},
[9712] = {
mallId = 104,
commodityId = 9712,
commodityName = "吾皇马甲",
coinType = 3134,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750003199
},
order = 4,
gender = 0,
itemIds = {
510252
}
},
[8313] = {
mallId = 105,
commodityId = 8313,
commodityName = "花海守护者 莉莉安",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
402100
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8314] = {
mallId = 105,
commodityId = 8314,
commodityName = "守护骑士 杰斯",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
402090
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8315] = {
mallId = 105,
commodityId = 8315,
commodityName = "永恒之翼",
coinType = 211,
price = 120,
limitNum = 1,
gender = 0,
itemIds = {
620298
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8316] = {
mallId = 105,
commodityId = 8316,
commodityName = "永恒之冠",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
630169
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8317] = {
mallId = 105,
commodityId = 8317,
commodityName = "梦幻铃铛",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
630166
}
},
[8318] = {
mallId = 105,
commodityId = 8318,
commodityName = "犬系少年 阿柴",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
402160
}
},
[8319] = {
mallId = 105,
commodityId = 8319,
commodityName = "猫系少女 喵喵",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
402170
}
},
[8320] = {
mallId = 105,
commodityId = 8320,
commodityName = "幸福花束",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620309
},
AvailableTips = 1,
isGrand = true,
pictureUrl = "img_30513.png"
},
[8321] = {
mallId = 105,
commodityId = 8321,
commodityName = "时尚魔头镜",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
610139
},
AvailableTips = 1
},
[8322] = {
mallId = 105,
commodityId = 8322,
commodityName = "星语晶恋",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
630174
}
},
[8323] = {
mallId = 105,
commodityId = 8323,
commodityName = "纯白誓约",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
721013
}
},
[8324] = {
mallId = 105,
commodityId = 8324,
commodityName = "绮莉莉",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
402040
}
},
[8325] = {
mallId = 105,
commodityId = 8325,
commodityName = "动态头像框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
840106
}
},
[8326] = {
mallId = 105,
commodityId = 8326,
commodityName = "动态昵称框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
820075
}
},
[8327] = {
mallId = 105,
commodityId = 8327,
commodityName = "星愿币",
coinType = 211,
price = 1,
limitNum = 9999,
gender = 0,
itemIds = {
2
}
},
[8328] = {
mallId = 107,
commodityId = 8328,
commodityName = "森小野",
coinType = 203,
price = 60,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 1,
itemIds = {
401590
}
},
[8329] = {
mallId = 107,
commodityId = 8329,
commodityName = "折纸风车",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 2,
itemIds = {
620200
}
},
[8330] = {
mallId = 107,
commodityId = 8330,
commodityName = "雪豹眼镜",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 3,
itemIds = {
610062
},
AvailableTips = 1
},
[8331] = {
mallId = 107,
commodityId = 8331,
commodityName = "拼贴风尚上装",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 4,
itemIds = {
510106
},
AvailableTips = 1
},
[8332] = {
mallId = 107,
commodityId = 8332,
commodityName = "拼贴风尚下装",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 5,
itemIds = {
520068
},
AvailableTips = 1
},
[8333] = {
mallId = 107,
commodityId = 8333,
commodityName = "拼贴风尚手套",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 6,
itemIds = {
530048
},
canGift = true,
addIntimacy = 500,
giftCoinType = 213,
giftPrice = 2,
AvailableTips = 1
},
[8334] = {
mallId = 107,
commodityId = 8334,
commodityName = "日落海岛上装",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 7,
itemIds = {
510107
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
AvailableTips = 1
},
[8335] = {
mallId = 107,
commodityId = 8335,
commodityName = "日落海岛下装",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 8,
itemIds = {
520069
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
AvailableTips = 1
},
[8336] = {
mallId = 107,
commodityId = 8336,
commodityName = "日落海岛手套",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 9,
itemIds = {
530049
}
},
[8337] = {
mallId = 107,
commodityId = 8337,
commodityName = "热带风情上装",
coinType = 203,
limitNum = 1,
beginTime = v51,
endTime = {
seconds = 1721404799
},
order = 10,
itemIds = {
510108
}
},
[8338] = {
mallId = 108,
commodityId = 8338,
commodityName = "灭战神 阿多斯",
coinType = 213,
price = 5,
limitNum = 1,
gender = 0,
itemIds = {
402480
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
402460,
402470
}
}
}
}
}
},
sortId = 2,
sortName = "战神颂歌"
},
[8339] = {
mallId = 108,
commodityId = 8339,
commodityName = "焰战神  波尔托斯",
coinType = 213,
price = 3,
limitNum = 1,
gender = 0,
itemIds = {
402460
},
sortId = 2,
sortName = "战神颂歌"
},
[8340] = {
mallId = 108,
commodityId = 8340,
commodityName = "耀战神 达达尼亚",
coinType = 213,
price = 3,
limitNum = 1,
gender = 0,
itemIds = {
402470
},
sortId = 2,
sortName = "战神颂歌"
},
[8341] = {
mallId = 108,
commodityId = 8341,
commodityName = "圣剑",
coinType = 213,
price = 2,
limitNum = 1,
gender = 0,
itemIds = {
640009
},
sortId = 2,
sortName = "战神颂歌"
},
[8342] = {
mallId = 108,
commodityId = 8342,
commodityName = "斗战神翎",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
630230
},
sortId = 2,
sortName = "战神颂歌"
},
[8343] = {
mallId = 108,
commodityId = 8343,
commodityName = "光之面甲",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
610173
},
sortId = 2,
sortName = "战神颂歌"
},
[8344] = {
mallId = 121,
commodityId = 8344,
commodityName = "森小野",
coinType = 2031,
price = 60,
limitNum = 1,
itemIds = {
401590
}
},
[8345] = {
mallId = 121,
commodityId = 8345,
commodityName = "折纸风车",
coinType = 2031,
price = 40,
limitNum = 1,
itemIds = {
620200
}
},
[8346] = {
mallId = 121,
commodityId = 8346,
commodityName = "雪豹眼镜",
coinType = 2031,
price = 40,
limitNum = 1,
itemIds = {
610062
}
},
[8347] = {
mallId = 121,
commodityId = 8347,
commodityName = "拼贴风尚上装",
coinType = 2031,
limitNum = 1,
itemIds = {
510106
}
},
[8348] = {
mallId = 121,
commodityId = 8348,
commodityName = "拼贴风尚下装",
coinType = 2031,
limitNum = 1,
itemIds = {
520068
},
AvailableTips = 1
},
[8349] = {
mallId = 121,
commodityId = 8349,
commodityName = "拼贴风尚手套",
coinType = 2031,
limitNum = 1,
itemIds = {
530048
},
AvailableTips = 1
},
[8350] = {
mallId = 122,
commodityId = 8350,
commodityName = "清爽运动员套装",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
310207
},
AvailableTips = 1
},
[8351] = {
mallId = 122,
commodityId = 8351,
commodityName = "鸭梨宝背随机礼盒",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
320051
},
AvailableTips = 1
},
[8352] = {
mallId = 122,
commodityId = 8352,
commodityName = "星愿币宝箱",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
320042
},
AvailableTips = 1
},
[8353] = {
mallId = 122,
commodityId = 8353,
commodityName = "2选1宝箱",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
330043
}
},
[8354] = {
mallId = 122,
commodityId = 8354,
commodityName = "灭战神 阿多斯",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
402480
}
},
[8355] = {
mallId = 122,
commodityId = 8355,
commodityName = "折纸风车",
coinType = 8,
price = 10,
limitNum = 1,
gender = 0,
itemIds = {
620200
}
},
[8356] = {
mallId = 140,
commodityId = 8356,
commodityName = "小夭",
coinType = 216,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
401500
}
},
[8357] = {
mallId = 140,
commodityId = 8357,
commodityName = "沧玹",
coinType = 216,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
401480
}
},
[8358] = {
mallId = 140,
commodityId = 8358,
commodityName = "涂山璟",
coinType = 216,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
401490
}
},
[8359] = {
mallId = 140,
commodityId = 8359,
commodityName = "赤水丰隆",
coinType = 216,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
401470
}
},
[8360] = {
mallId = 140,
commodityId = 8360,
commodityName = "相柳",
coinType = 216,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
401460
}
},
[8361] = {
mallId = 140,
commodityId = 8361,
commodityName = "小天 团扇",
coinType = 216,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620175
}
},
[8362] = {
mallId = 140,
commodityId = 8362,
commodityName = "跄玹 若木花簪",
coinType = 216,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620173
}
},
[8363] = {
mallId = 140,
commodityId = 8363,
commodityName = "涂山璟 识神小狐",
coinType = 216,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620172
}
},
[8364] = {
mallId = 140,
commodityId = 8364,
commodityName = "丰隆 火球",
coinType = 216,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620174
}
},
[8365] = {
mallId = 140,
commodityId = 8365,
commodityName = "相柳-毛球",
coinType = 216,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
630099
}
},
[8366] = {
mallId = 140,
commodityId = 8366,
commodityName = "小幺表情",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
711035
}
},
[8367] = {
mallId = 140,
commodityId = 8367,
commodityName = "沧玹表情",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
711039
}
},
[8368] = {
mallId = 140,
commodityId = 8368,
commodityName = "涂山璟表情",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
711037
}
},
[8369] = {
mallId = 140,
commodityId = 8369,
commodityName = "赤水丰隆表情",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
711038
}
},
[8370] = {
mallId = 140,
commodityId = 8370,
commodityName = "相柳表情",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
711036
}
},
[8371] = {
mallId = 140,
commodityId = 8371,
commodityName = "小幺语音",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
725434
}
},
[8372] = {
mallId = 140,
commodityId = 8372,
commodityName = "沧玹语音",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
725432
}
},
[8373] = {
mallId = 140,
commodityId = 8373,
commodityName = "涂山璟语音",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
725437
}
},
[8374] = {
mallId = 140,
commodityId = 8374,
commodityName = "赤水丰隆语音",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
725438
}
},
[8375] = {
mallId = 140,
commodityId = 8375,
commodityName = "相柳语音",
coinType = 216,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
725441
}
},
[8377] = {
mallId = 144,
commodityId = 8377,
commodityName = "幸运宝藏",
coinType = 217,
price = 300,
limitNum = 9999,
itemIds = {
320064
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640031,
620402,
610165,
630222,
4,
4
}
}
}
}
}
}
},
[8378] = {
mallId = 144,
commodityId = 8378,
commodityName = "集满宝藏",
coinType = 217,
price = 300,
limitNum = 9999,
itemIds = {
310025
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640031,
620402,
610165,
630222,
1,
4
}
}
}
}
}
}
},
[8379] = {
mallId = 141,
commodityId = 8379,
commodityName = "时装手稿",
coinType = 12,
price = 10,
limitNum = 99,
gender = 0,
itemIds = {
2069
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
2069
}
}
}
}
}
},
itemMaxOwnNums = {
100
}
},
[8380] = {
mallId = 141,
commodityId = 8380,
commodityName = "寻梦冒险家 米萝",
coinType = 2069,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
402570
},
buyCondition = {
condition = {
{
conditionType = 205,
value = 1,
subConditionList = {
{
type = 3,
value = {
402570
}
}
}
}
}
},
itemMaxOwnNums = {
1
}
},
[8381] = {
mallId = 141,
commodityId = 8381,
commodityName = "祈梦星",
coinType = 1,
price = 10,
limitNum = 9999,
gender = 0,
itemIds = {
12
},
itemNums = {
10
},
buyCondition = {
condition = {
{
conditionType = 205,
value = 1,
subConditionList = {
{
type = 3,
value = {
402570
}
}
}
}
}
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
AvailableTips = 1
},
[8385] = {
mallId = 107,
commodityId = 8385,
commodityName = "木伊伊",
coinType = 219,
price = 45,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 5,
itemIds = {
402230
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
AvailableTips = 1
},
[8386] = {
mallId = 107,
commodityId = 8386,
commodityName = "蕉绿绿",
coinType = 219,
price = 30,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 6,
itemIds = {
400910
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 100
},
[8387] = {
mallId = 107,
commodityId = 8387,
commodityName = "香嘟嘟",
coinType = 219,
price = 30,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 7,
itemIds = {
400920
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8388] = {
mallId = 107,
commodityId = 8388,
commodityName = "小丸子头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 10,
itemIds = {
860046
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8389] = {
mallId = 107,
commodityId = 8389,
commodityName = "花轮头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 11,
itemIds = {
860047
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8390] = {
mallId = 107,
commodityId = 8390,
commodityName = "鸭鸭小甜豆头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 12,
itemIds = {
860048
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
AvailableTips = 1
},
[8391] = {
mallId = 107,
commodityId = 8391,
commodityName = "柠檬小甜豆头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 13,
itemIds = {
860049
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
AvailableTips = 1
},
[8392] = {
mallId = 143,
commodityId = 8392,
commodityName = "凰后 丹翎",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
403110
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8393] = {
mallId = 143,
commodityId = 8393,
commodityName = "凤王 赤羽",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
403100
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8394] = {
mallId = 143,
commodityId = 8394,
commodityName = "焰羽瑶琴",
coinType = 211,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
620415
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8395] = {
mallId = 143,
commodityId = 8395,
commodityName = "火凤羽冠",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
630281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8396] = {
mallId = 143,
commodityId = 8396,
commodityName = "凤羽之舞",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
610204
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8397] = {
mallId = 143,
commodityId = 8397,
commodityName = "彩莲提灯",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
640018
}
},
[8398] = {
mallId = 143,
commodityId = 8398,
commodityName = "倾绝之舞 莎希莉",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
403070
}
},
[8399] = {
mallId = 143,
commodityId = 8399,
commodityName = "星佑之音 巴兰",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
403080
}
},
[8400] = {
mallId = 143,
commodityId = 8400,
commodityName = "祥瑞喜鹊",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
630284
}
},
[8401] = {
mallId = 143,
commodityId = 8401,
commodityName = "卷不离身",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620418
},
AvailableTips = 1,
isGrand = true,
pictureUrl = "niujiasankou.png"
},
[8402] = {
mallId = 143,
commodityId = 8402,
commodityName = "火力全开",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
610201
},
AvailableTips = 1
},
[8403] = {
mallId = 143,
commodityId = 8403,
commodityName = "举高高",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
721006
},
AvailableTips = 1
},
[8404] = {
mallId = 143,
commodityId = 8404,
commodityName = "缘芊芊",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
403130
},
AvailableTips = 1
},
[8405] = {
mallId = 143,
commodityId = 8405,
commodityName = "动态头像框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
840157
}
},
[8406] = {
mallId = 143,
commodityId = 8406,
commodityName = "动态昵称框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
820107
}
},
[8407] = {
mallId = 143,
commodityId = 8407,
commodityName = "静态称号",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
850462
},
AvailableTips = 1
},
[8408] = {
mallId = 143,
commodityId = 8408,
commodityName = "星愿币",
coinType = 211,
price = 1,
limitNum = 9999,
gender = 0,
itemIds = {
2
},
AvailableTips = 1
},
[8425] = {
mallId = 107,
commodityId = 8425,
commodityName = "扇火魔心",
coinType = 219,
price = 110,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 1,
itemIds = {
313001
},
AvailableTips = 1
},
[8426] = {
mallId = 107,
commodityId = 8426,
commodityName = "牛魔王",
coinType = 219,
price = 45,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 2,
itemIds = {
403140
}
},
[8427] = {
mallId = 107,
commodityId = 8427,
commodityName = "铁扇公主",
coinType = 219,
price = 45,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 3,
itemIds = {
403150
}
},
[8428] = {
mallId = 107,
commodityId = 8428,
commodityName = "红孩儿",
coinType = 219,
price = 45,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 4,
itemIds = {
402450
}
},
[8429] = {
mallId = 107,
commodityId = 8429,
commodityName = "布朗熊头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 8,
itemIds = {
860056
}
},
[8430] = {
mallId = 107,
commodityId = 8430,
commodityName = "可妮兔头像",
coinType = 219,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1724688000
},
endTime = {
seconds = 1726761599
},
order = 9,
itemIds = {
860057
}
},
[8431] = {
mallId = 146,
commodityId = 8431,
commodityName = "chiikawa-吉伊",
coinType = 221,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
402870
}
},
[8432] = {
mallId = 146,
commodityId = 8432,
commodityName = "chiikawa-小八",
coinType = 221,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
402880
}
},
[8433] = {
mallId = 146,
commodityId = 8433,
commodityName = "chiikawa-乌萨奇",
coinType = 221,
price = 160,
limitNum = 1,
gender = 0,
itemIds = {
402890
}
},
[8434] = {
mallId = 146,
commodityId = 8434,
commodityName = "树叶触角",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
630414
}
},
[8435] = {
mallId = 146,
commodityId = 8435,
commodityName = "幸运小蛙",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
630417
}
},
[8436] = {
mallId = 146,
commodityId = 8436,
commodityName = "除草手册",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
620628
}
},
[8437] = {
mallId = 146,
commodityId = 8437,
commodityName = "吉伊卡哇装",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
410730
}
},
[8438] = {
mallId = 146,
commodityId = 8438,
commodityName = "哈奇喵装",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
410740
}
},
[8439] = {
mallId = 146,
commodityId = 8439,
commodityName = "乌萨奇装",
coinType = 221,
price = 80,
limitNum = 1,
itemIds = {
410750
}
},
[8440] = {
mallId = 146,
commodityId = 8440,
commodityName = "小夭娇俏",
coinType = 221,
price = 40,
limitNum = 1,
itemIds = {
711035
}
},
[8441] = {
mallId = 146,
commodityId = 8441,
commodityName = "玱玹嗑瓜子",
coinType = 221,
price = 40,
limitNum = 1,
itemIds = {
711039
}
},
[8442] = {
mallId = 146,
commodityId = 8442,
commodityName = "涂山璟深情",
coinType = 221,
price = 40,
limitNum = 1,
itemIds = {
711037
}
},
[8449] = {
mallId = 147,
commodityId = 8449,
commodityName = "嫦娥",
coinType = 203,
price = 320,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 1,
gender = 0,
itemIds = {
403460
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8450] = {
mallId = 147,
commodityId = 8450,
commodityName = "兔倚秋夕",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 2,
gender = 0,
itemIds = {
620481
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8451] = {
mallId = 147,
commodityId = 8451,
commodityName = "月色瑶华",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 3,
gender = 0,
itemIds = {
630332
}
},
[8452] = {
mallId = 147,
commodityId = 8452,
commodityName = "晶玉双蝶",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 4,
gender = 0,
itemIds = {
630273
}
},
[8453] = {
mallId = 147,
commodityId = 8453,
commodityName = "皓月使者 丹桂",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 5,
gender = 0,
itemIds = {
310259
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
AvailableTips = 1
},
[8454] = {
mallId = 147,
commodityId = 8454,
commodityName = "月宫灵兔 锦儿",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 6,
gender = 0,
itemIds = {
403580
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
commodityThemeInfo = {
themeId = 1,
isThemeBuyLimit = true
}
},
[8455] = {
mallId = 147,
commodityId = 8455,
commodityName = "玉叶琼枝",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 7,
gender = 0,
itemIds = {
610232
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
commodityThemeInfo = {
themeId = 1,
isThemeBuyLimit = true
}
},
[8456] = {
mallId = 147,
commodityId = 8456,
commodityName = "时光花语",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 8,
gender = 0,
itemIds = {
620475
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 100,
commodityThemeInfo = {
themeId = 1,
isThemeBuyLimit = true
}
},
[8457] = {
mallId = 147,
commodityId = 8457,
commodityName = "金桂玉冠",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 9,
gender = 0,
itemIds = {
630329
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
AvailableTips = 1
},
[8458] = {
mallId = 147,
commodityId = 8458,
commodityName = "猎小鹰",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
order = 10,
gender = 0,
itemIds = {
403550
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
AvailableTips = 1
},
[8459] = {
mallId = 147,
commodityId = 8459,
commodityName = "皎月吹纱",
coinType = 203,
price = 40,
limitNum = 1,
order = 11,
gender = 0,
itemIds = {
840175
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8460] = {
mallId = 147,
commodityId = 8460,
commodityName = "星愿币",
coinType = 203,
price = 1,
limitNum = 9999,
order = 12,
gender = 0,
itemIds = {
2
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8461] = {
mallId = 162,
commodityId = 8461,
commodityName = "冰雪精灵",
coinType = 203,
price = 320,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 1,
gender = 0,
itemIds = {
404320
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8462] = {
mallId = 162,
commodityId = 8462,
commodityName = "迷雾之纱",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 2,
gender = 0,
itemIds = {
610284
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[8463] = {
mallId = 162,
commodityId = 8463,
commodityName = "海洋精灵",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 3,
gender = 0,
itemIds = {
610284
}
},
[8464] = {
mallId = 162,
commodityId = 8464,
commodityName = "霜之韵律",
coinType = 203,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 4,
gender = 0,
itemIds = {
640084
}
},
[8465] = {
mallId = 162,
commodityId = 8465,
commodityName = "大雪怪 伊拉",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 5,
gender = 0,
itemIds = {
404520
}
},
[8466] = {
mallId = 162,
commodityId = 8466,
commodityName = "冰霜者 海沃",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 6,
gender = 0,
itemIds = {
404530
}
},
[8467] = {
mallId = 162,
commodityId = 8467,
commodityName = "电焊面具",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 7,
gender = 0,
itemIds = {
610207
}
},
[8468] = {
mallId = 162,
commodityId = 8468,
commodityName = "冰雪兔兔",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 8,
gender = 0,
itemIds = {
630423
}
},
[8469] = {
mallId = 162,
commodityId = 8469,
commodityName = "法官锤子",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 9,
gender = 0,
itemIds = {
620434
}
},
[8470] = {
mallId = 162,
commodityId = 8470,
commodityName = "可可豆",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 10,
gender = 0,
itemIds = {
402770
}
},
[8471] = {
mallId = 162,
commodityId = 8471,
commodityName = "冰霜之华",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 11,
gender = 0,
itemIds = {
840213
}
},
[8472] = {
mallId = 162,
commodityId = 8472,
commodityName = "星愿币",
coinType = 203,
price = 1,
limitNum = 9999,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
order = 12,
gender = 0,
itemIds = {
2
}
},
[8560] = {
mallId = 153,
commodityId = 8560,
commodityName = "新春贺岁",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200121
}
},
[8561] = {
mallId = 153,
commodityId = 8561,
commodityName = "花好月圆",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200122
}
},
[8562] = {
mallId = 153,
commodityId = 8562,
commodityName = "五月端阳",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200123
}
},
[8563] = {
mallId = 153,
commodityId = 8563,
commodityName = "鹊桥相会",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200124
}
},
[8564] = {
mallId = 153,
commodityId = 8564,
commodityName = "元宵佳节",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200125
}
},
[8565] = {
mallId = 153,
commodityId = 8565,
commodityName = "水墨江山",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200126
}
},
[8566] = {
mallId = 153,
commodityId = 8566,
commodityName = "花鸟写意",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200127
}
},
[8567] = {
mallId = 153,
commodityId = 8567,
commodityName = "汉唐风华",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200128
}
},
[8568] = {
mallId = 153,
commodityId = 8568,
commodityName = "生旦净丑",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200129
}
},
[8569] = {
mallId = 153,
commodityId = 8569,
commodityName = "九宵云汉",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200130
}
},
[8570] = {
mallId = 153,
commodityId = 8570,
commodityName = "瑞彩祥云",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200131
}
},
[8571] = {
mallId = 153,
commodityId = 8571,
commodityName = "飞龙在天",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200132
}
},
[8572] = {
mallId = 153,
commodityId = 8572,
commodityName = "如虎添翼",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200133
}
},
[8573] = {
mallId = 153,
commodityId = 8573,
commodityName = "百鸟朝凤",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200134
}
},
[8574] = {
mallId = 153,
commodityId = 8574,
commodityName = "桃源追忆",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200135
}
},
[8575] = {
mallId = 153,
commodityId = 8575,
commodityName = "千岁鹤归",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200136
}
},
[8576] = {
mallId = 153,
commodityId = 8576,
commodityName = "出水芙蓉",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200137
}
},
[8577] = {
mallId = 153,
commodityId = 8577,
commodityName = "锦衣霓裳",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200138
}
},
[8578] = {
mallId = 153,
commodityId = 8578,
commodityName = "古蜀珍玩",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200139
}
},
[8579] = {
mallId = 153,
commodityId = 8579,
commodityName = "青铜国器",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200140
}
},
[8580] = {
mallId = 153,
commodityId = 8580,
commodityName = "金彰华彩",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200141
}
},
[8581] = {
mallId = 153,
commodityId = 8581,
commodityName = "石之美者",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200142
}
},
[8582] = {
mallId = 153,
commodityId = 8582,
commodityName = "写意工笔",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200143
}
},
[8583] = {
mallId = 153,
commodityId = 8583,
commodityName = "匠心点翠",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200144
}
},
[8584] = {
mallId = 153,
commodityId = 8584,
commodityName = "碧波金影",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200145
}
},
[8585] = {
mallId = 153,
commodityId = 8585,
commodityName = "清明上河",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200146
}
},
[8586] = {
mallId = 153,
commodityId = 8586,
commodityName = "月宫玉兔",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200147
}
},
[8587] = {
mallId = 153,
commodityId = 8587,
commodityName = "晓梦迷蝶",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200148
}
},
[8588] = {
mallId = 153,
commodityId = 8588,
commodityName = "千里江山",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200149
}
},
[8589] = {
mallId = 153,
commodityId = 8589,
commodityName = "富春山居",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200150
}
},
[8590] = {
mallId = 153,
commodityId = 8590,
commodityName = "暗香疏影",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200151
}
},
[8591] = {
mallId = 153,
commodityId = 8591,
commodityName = "空谷幽兰",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200152
}
},
[8592] = {
mallId = 153,
commodityId = 8592,
commodityName = "竹报平安",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200153
}
},
[8593] = {
mallId = 153,
commodityId = 8593,
commodityName = "秋菊傲骨‌",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200154
}
},
[8594] = {
mallId = 153,
commodityId = 8594,
commodityName = "松柏之志",
coinType = 2072,
price = 3,
limitNum = 99,
itemIds = {
200155
}
},
[8595] = {
mallId = 23,
commodityId = 8595,
commodityName = "星钻兑换折纸爱心",
coinType = 1,
price = 10,
limitNum = 70,
itemIds = {
317101
},
itemMaxOwnNums = {
70
}
},
[8701] = {
mallId = 156,
commodityId = 8701,
commodityName = "企鹅小甜豆头像",
coinType = 3407,
price = 30,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 2,
gender = 0,
itemIds = {
860114
}
},
[8702] = {
mallId = 156,
commodityId = 8702,
commodityName = "绵羊小甜豆头像",
coinType = 3407,
price = 30,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 3,
gender = 0,
itemIds = {
860115
}
},
[8703] = {
mallId = 156,
commodityId = 8703,
commodityName = "（返场）小甜豆聊天框",
coinType = 3407,
price = 20,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 4,
gender = 0,
itemIds = {
870008
}
},
[8704] = {
mallId = 156,
commodityId = 8704,
commodityName = "星宝印章*100",
coinType = 3407,
price = 5,
limitNum = 2,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 5,
gender = 0,
itemIds = {
4
},
itemNums = {
100
}
},
[8705] = {
mallId = 156,
commodityId = 8705,
commodityName = v13,
coinType = 3407,
price = 5,
limitNum = 3,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 6,
gender = 0,
itemIds = v43,
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
AvailableTips = 1
},
[8706] = {
mallId = 156,
commodityId = 8706,
commodityName = "心心糖果",
coinType = 3407,
limitNum = 3,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 7,
gender = 0,
itemIds = {
200015
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
AvailableTips = 1
},
[8707] = {
mallId = 156,
commodityId = 8707,
commodityName = "心心宝瓶",
coinType = 3407,
price = 7,
limitNum = 10,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 8,
gender = 0,
itemIds = {
200016
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 100
},
[8708] = {
mallId = 156,
commodityId = 8708,
commodityName = "信心蜜罐",
coinType = 3407,
price = 8,
limitNum = 15,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 9,
gender = 0,
itemIds = {
200017
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8709] = {
mallId = 156,
commodityId = 8709,
commodityName = "萌龙送瑞聊天框",
coinType = 3407,
price = 10,
limitNum = 15,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 10,
gender = 0,
itemIds = {
870003
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8710] = {
mallId = 156,
commodityId = 8710,
commodityName = "玉米棒棒自选礼包",
coinType = 3407,
price = 60,
limitNum = 2,
beginTime = v65,
endTime = {
seconds = 1732982399
},
order = 11,
gender = 0,
itemIds = {
330055
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80
},
[8801] = {
mallId = 144,
commodityId = 8801,
commodityName = "幸运宝藏",
coinType = 222,
price = 300,
limitNum = 9999,
itemIds = {
320079
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640075,
620588,
630411,
620152,
4,
4
}
}
}
}
}
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
AvailableTips = 1
},
[8802] = {
mallId = 144,
commodityId = 8802,
commodityName = "集满宝藏",
coinType = 222,
price = 300,
limitNum = 9999,
itemIds = {
310040
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640075,
620588,
630411,
620152,
1,
4
}
}
}
}
}
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
AvailableTips = 1
},
[8803] = {
mallId = 159,
commodityId = 8803,
commodityName = "永昼男爵 索林",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
404110
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8804] = {
mallId = 159,
commodityId = 8804,
commodityName = "暮色皇女 伊美尔",
coinType = 211,
price = 320,
limitNum = 1,
gender = 0,
itemIds = {
404120
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8805] = {
mallId = 159,
commodityId = 8805,
commodityName = "【橙配】酒杯-手持",
coinType = 211,
price = 100,
limitNum = 1,
gender = 0,
itemIds = {
640068
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8806] = {
mallId = 159,
commodityId = 8806,
commodityName = "双羽焰舞",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
620570
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8807] = {
mallId = 159,
commodityId = 8807,
commodityName = "心梦光环",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
630392
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[8808] = {
mallId = 159,
commodityId = 8808,
commodityName = "赤焰之舞",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
610264
}
},
[8809] = {
mallId = 159,
commodityId = 8809,
commodityName = "小南瓜 卡芭莎",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
404040
}
},
[8810] = {
mallId = 159,
commodityId = 8810,
commodityName = "小幽灵 凡托姆",
coinType = 211,
price = 80,
limitNum = 1,
gender = 0,
itemIds = {
404130
}
},
[8811] = {
mallId = 159,
commodityId = 8811,
commodityName = "皇家权杖",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
640071
}
},
[8812] = {
mallId = 159,
commodityId = 8812,
commodityName = "星愿号角",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
620550
}
},
[8813] = {
mallId = 159,
commodityId = 8813,
commodityName = "大眼萌萌",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
610267
},
AvailableTips = 1
},
[8814] = {
mallId = 159,
commodityId = 8814,
commodityName = "发型不能乱",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
720781
},
AvailableTips = 1
},
[8815] = {
mallId = 159,
commodityId = 8815,
commodityName = "莓果果",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
404150
},
AvailableTips = 1
},
[8816] = {
mallId = 159,
commodityId = 8816,
commodityName = "动态头像框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
840194
}
},
[8817] = {
mallId = 159,
commodityId = 8817,
commodityName = "动态昵称框",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
820132
}
},
[8818] = {
mallId = 159,
commodityId = 8818,
commodityName = "静态称号",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
850508
}
},
[8819] = {
mallId = 159,
commodityId = 8819,
commodityName = "星愿币",
coinType = 211,
price = 1,
limitNum = 9999,
gender = 0,
itemIds = {
2
}
},
[8596] = {
mallId = 23,
commodityId = 8596,
commodityName = "星钻兑换大苹果",
coinType = 1,
price = 10,
limitNum = 130,
itemIds = {
317102
},
itemMaxOwnNums = {
130
}
},
[9101] = {
mallId = 164,
commodityId = 9101,
commodityName = "璀璨星翼",
coinType = 225,
price = 18,
limitNum = 1,
itemIds = {
730013
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
730011
}
}
}
}
}
},
sortId = 1,
sortName = "新品载具"
},
[9102] = {
mallId = 164,
commodityId = 9102,
commodityName = "星澜灵角",
coinType = 225,
price = 9,
limitNum = 1,
itemIds = {
730011
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
730010
}
}
}
}
}
},
sortId = 1,
sortName = "新品载具"
},
[9103] = {
mallId = 164,
commodityId = 9103,
commodityName = "星辉幼角",
coinType = 225,
limitNum = 1,
itemIds = {
730010
},
sortId = 1,
sortName = "新品载具"
},
[9104] = {
mallId = 164,
commodityId = 9104,
commodityName = "载具升阶石",
coinType = 224,
price = 100,
limitNum = 999,
itemIds = {
225
},
sortId = 2,
sortName = "升阶道具"
},
[9105] = {
mallId = 164,
commodityId = 9105,
commodityName = "动态称号",
coinType = 224,
price = 150,
limitNum = 1,
itemIds = {
850570
},
sortId = 4,
sortName = "个性装饰"
},
[9106] = {
mallId = 164,
commodityId = 9106,
commodityName = "动态头像框",
coinType = 224,
price = 80,
limitNum = 1,
itemIds = {
840221
},
sortId = 4,
sortName = "个性装饰"
},
[9107] = {
mallId = 164,
commodityId = 9107,
commodityName = "动态昵称框",
coinType = 224,
price = 80,
limitNum = 1,
itemIds = {
820144
},
sortId = 4,
sortName = "个性装饰"
},
[9108] = {
mallId = 164,
commodityId = 9108,
commodityName = "蝴蝶翅膀",
coinType = 224,
price = 500,
limitNum = 1,
itemIds = {
620368
},
sortId = 3,
sortName = "精选奖品"
},
[9109] = {
mallId = 164,
commodityId = 9109,
commodityName = "大白鲨",
coinType = 224,
price = 180,
limitNum = 1,
itemIds = {
640105
},
sortId = 3,
sortName = "精选奖品"
},
[9110] = {
mallId = 164,
commodityId = 9110,
commodityName = "独角",
coinType = 224,
price = 180,
limitNum = 1,
itemIds = {
630484
},
sortId = 3,
sortName = "精选奖品"
},
[9111] = {
mallId = 164,
commodityId = 9111,
commodityName = "丘比特",
coinType = 224,
price = 150,
limitNum = 1,
itemIds = {
404630
},
sortId = 3,
sortName = "精选奖品"
},
[9112] = {
mallId = 164,
commodityId = 9112,
commodityName = "圣诞小鹿男",
coinType = 224,
price = 50,
limitNum = 1,
itemIds = {
404780
},
sortId = 3,
sortName = "精选奖品"
},
[9113] = {
mallId = 164,
commodityId = 9113,
commodityName = "圣诞小鹿女",
coinType = 224,
price = 50,
limitNum = 1,
itemIds = {
404790
},
AvailableTips = 1,
sortId = 3,
sortName = "精选奖品"
},
[9114] = {
mallId = 164,
commodityId = 9114,
commodityName = "倒扣冰淇淋",
coinType = 224,
price = 30,
limitNum = 1,
itemIds = {
630426
},
sortId = 3,
sortName = "精选奖品"
},
[9115] = {
mallId = 164,
commodityId = 9115,
commodityName = "丘比特之弓",
coinType = 224,
price = 30,
limitNum = 1,
itemIds = {
640102
},
sortId = 3,
sortName = "精选奖品"
},
[9133] = {
mallId = 164,
commodityId = 9133,
commodityName = "头饰-鸟窝",
coinType = 224,
price = 30,
limitNum = 1,
itemIds = {
630445
},
sortId = 3,
sortName = "精选奖品"
},
[8901] = {
mallId = 167,
commodityId = 8901,
commodityName = "星宝农场月卡1日体验",
coinType = 3510,
price = 10,
limitType = "MCL_WeeklyLimit",
limitNum = 1,
order = 1,
gender = 0,
itemIds = {
200104
}
},
[8902] = {
mallId = 167,
commodityId = 8902,
commodityName = "星宝农场月卡7日体验",
coinType = 3510,
price = 70,
limitType = "MCL_MonthlyLimit",
limitNum = 1,
order = 2,
gender = 0,
itemIds = {
200202
}
},
[8903] = {
mallId = 167,
commodityId = 8903,
commodityName = "树精宝宝",
coinType = 3510,
price = 180,
limitNum = 1,
order = 7,
gender = 0,
itemIds = {
218148
}
},
[8904] = {
mallId = 167,
commodityId = 8904,
commodityName = "绿叶时钟",
coinType = 3510,
price = 150,
limitNum = 1,
order = 8,
gender = 0,
itemIds = {
218149
}
},
[8905] = {
mallId = 167,
commodityId = 8905,
commodityName = "小乌云",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
order = 10,
gender = 0,
itemIds = {
219202
},
AvailableTips = 1
},
[8906] = {
mallId = 167,
commodityId = 8906,
commodityName = "小肥啾",
coinType = 3510,
price = 100,
discountPrice = 50,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
order = 11,
gender = 0,
itemIds = {
219402
}
},
[8907] = {
mallId = 167,
commodityId = 8907,
commodityName = "神奇海螺壁炉",
coinType = 3510,
price = 120,
discountPrice = 60,
limitNum = 1,
order = 13,
gender = 0,
itemIds = {
218818
}
},
[8908] = {
mallId = 167,
commodityId = 8908,
commodityName = "爱心小熊沙发",
coinType = 3510,
price = 120,
limitNum = 1,
order = 14,
gender = 0,
itemIds = {
218801
}
},
[8909] = {
mallId = 167,
commodityId = 8909,
commodityName = "银风山谷",
coinType = 3510,
price = 30,
limitType = "MCL_WeeklyLimit",
limitNum = 1,
order = 15,
gender = 0,
itemIds = {
218140
},
expireDays = {
3
}
},
[8910] = {
mallId = 167,
commodityId = 8910,
commodityName = "冬雪庄园",
coinType = 3510,
price = 30,
limitType = "MCL_WeeklyLimit",
limitNum = 1,
order = 16,
gender = 0,
itemIds = {
218141
},
expireDays = {
3
}
},
[8911] = {
mallId = 167,
commodityId = 8911,
commodityName = "富贵屏风",
coinType = 3510,
price = 120,
limitNum = 1,
beginTime = {
seconds = 1740002400
},
order = 12,
gender = 0,
itemIds = {
218823
}
},
[8912] = {
mallId = 167,
commodityId = 8912,
commodityName = "小煤球",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
order = 9,
gender = 0,
itemIds = {
219200
}
},
[8913] = {
mallId = 167,
commodityId = 8913,
commodityName = "萝尖尖果行",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
order = 6,
gender = 0,
showRedPoint = 1,
itemIds = {
218144
}
},
[8914] = {
mallId = 167,
commodityId = 8914,
commodityName = "绿绒绒小铺",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
order = 5,
gender = 0,
showRedPoint = 1,
itemIds = {
218145
}
},
[8915] = {
mallId = 167,
commodityId = 8915,
commodityName = "叶泡泡鱼店",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
order = 4,
gender = 0,
showRedPoint = 1,
itemIds = {
218146
},
AvailableTips = 1
},
[8916] = {
mallId = 167,
commodityId = 8916,
commodityName = "风悠悠树屋",
coinType = 3510,
price = 420,
limitNum = 1,
beginTime = v62,
order = 3,
gender = 0,
minVersion = "*********",
showRedPoint = 1,
itemIds = {
218143
},
AvailableTips = 1
},
[8917] = {
mallId = 167,
commodityId = 8917,
commodityName = "牦铃货铺",
coinType = 3510,
price = 180,
limitNum = 1,
beginTime = {
seconds = 1750953600
},
order = 3,
gender = 0,
minVersion = "*********",
showRedPoint = 1,
itemIds = {
218209
},
AvailableTips = 1
},
[9116] = {
mallId = 108,
commodityId = 9116,
commodityName = "永恒天使 艾薇",
coinType = 213,
price = 8,
limitNum = 1,
gender = 0,
itemIds = {
404560
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
404550,
404540
}
}
}
}
}
},
sortId = 1,
sortName = "天启圣谕"
},
[9117] = {
mallId = 108,
commodityId = 9117,
commodityName = "昼天使 卢兹",
coinType = 213,
price = 5,
limitNum = 1,
gender = 0,
itemIds = {
404550
},
sortId = 1,
sortName = "天启圣谕"
},
[9118] = {
mallId = 108,
commodityId = 9118,
commodityName = "夜天使 莎莉娅",
coinType = 213,
price = 5,
limitNum = 1,
gender = 0,
itemIds = {
404540
},
sortId = 1,
sortName = "天启圣谕"
},
[9119] = {
mallId = 108,
commodityId = 9119,
commodityName = "光暗之翼",
coinType = 213,
price = 5,
limitNum = 1,
gender = 0,
itemIds = {
620672
},
sortId = 1,
sortName = "天启圣谕"
},
[9120] = {
mallId = 108,
commodityId = 9120,
commodityName = "圣光权杖",
coinType = 213,
price = 2,
limitNum = 1,
gender = 0,
itemIds = {
640108
},
sortId = 1,
sortName = "天启圣谕"
},
[9121] = {
mallId = 108,
commodityId = 9121,
commodityName = "天使之辉",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
630452
},
sortId = 1,
sortName = "天启圣谕"
},
[9122] = {
mallId = 108,
commodityId = 9122,
commodityName = "双生之羽",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
610297
},
sortId = 1,
sortName = "天启圣谕"
},
[9123] = {
mallId = 168,
commodityId = 9123,
commodityName = "企鹅小甜豆头像",
coinType = 3407,
price = 30,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 2,
gender = 0,
itemIds = {
860114
}
},
[9124] = {
mallId = 168,
commodityId = 9124,
commodityName = "绵羊小甜豆头像",
coinType = 3407,
price = 30,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 3,
gender = 0,
itemIds = {
860115
}
},
[9125] = {
mallId = 168,
commodityId = 9125,
commodityName = "（返场）小甜豆聊天框",
coinType = 3407,
price = 20,
limitNum = 1,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 4,
gender = 0,
itemIds = {
870008
}
},
[9126] = {
mallId = 168,
commodityId = 9126,
commodityName = "星宝印章*100",
coinType = 3407,
price = 5,
limitNum = 2,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 5,
gender = 0,
itemIds = {
4
},
itemNums = {
100
}
},
[9127] = {
mallId = 168,
commodityId = 9127,
commodityName = v13,
coinType = 3407,
price = 5,
limitNum = 3,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 6,
gender = 0,
itemIds = v43,
AvailableTips = 1
},
[9128] = {
mallId = 168,
commodityId = 9128,
commodityName = "心心糖果",
coinType = 3407,
limitNum = 3,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 7,
gender = 0,
itemIds = {
200015
},
AvailableTips = 1
},
[9129] = {
mallId = 168,
commodityId = 9129,
commodityName = "心心宝瓶",
coinType = 3407,
price = 7,
limitNum = 10,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 8,
gender = 0,
itemIds = {
200016
},
AvailableTips = 1
},
[9130] = {
mallId = 168,
commodityId = 9130,
commodityName = "信心蜜罐",
coinType = 3407,
price = 8,
limitNum = 15,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 9,
gender = 0,
itemIds = {
200017
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
AvailableTips = 1
},
[9131] = {
mallId = 168,
commodityId = 9131,
commodityName = "萌龙送瑞聊天框",
coinType = 3407,
price = 10,
limitNum = 15,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 10,
gender = 0,
itemIds = {
870003
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
AvailableTips = 1
},
[9132] = {
mallId = 168,
commodityId = 9132,
commodityName = "玉米棒棒自选礼包",
coinType = 3407,
price = 60,
limitNum = 2,
beginTime = v65,
endTime = {
seconds = 1736351999
},
order = 11,
gender = 0,
minVersion = "********",
itemIds = {
330055
}
},
[9300] = {
mallId = 169,
commodityId = 9300,
commodityName = "小鹿篮",
coinType = 1,
price = 120,
limitNum = 1,
gender = 0,
minVersion = "********",
itemIds = {
620546
}
},
[9301] = {
mallId = 169,
commodityId = 9301,
commodityName = "小蜗行囊",
coinType = 1,
price = 120,
limitNum = 1,
gender = 0,
minVersion = "********",
itemIds = {
620537
}
},
[9304] = {
mallId = 172,
commodityId = 9304,
commodityName = "峡谷英雄伽罗",
coinType = 3608,
price = 1,
limitNum = 1,
minVersion = "********",
itemIds = {
410140,
301135
},
itemNums = {
1,
1
}
},
[9305] = {
mallId = 172,
commodityId = 9305,
commodityName = "偶像歌手 王昭君",
coinType = 3608,
price = 1,
limitNum = 1,
itemIds = {
403640
}
},
[9306] = {
mallId = 172,
commodityId = 9306,
commodityName = "记忆之芯 公孙离",
coinType = 3608,
price = 1,
limitNum = 1,
itemIds = {
403650
}
},
[9172] = {
mallId = 108,
commodityId = 9172,
commodityName = "深渊之眼 尤利西斯",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
404170
},
sortId = 1,
sortName = "天启圣谕"
},
[9173] = {
mallId = 108,
commodityId = 9173,
commodityName = "甜心烘焙师 芙芙",
coinType = 213,
price = 1,
limitNum = 1,
gender = 0,
itemIds = {
404420
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
sortId = 1,
sortName = "天启圣谕"
},
[9307] = {
mallId = 173,
commodityId = 9307,
commodityName = "限时星愿币",
coinType = 3800,
price = 1,
limitNum = 12,
beginTime = {
seconds = 1736697600
},
endTime = {
seconds = 1746115199
},
itemIds = {
214
},
beginShowTime = {
seconds = 1736697600
},
AvailableTips = 1
},
[9308] = {
mallId = 173,
commodityId = 9308,
commodityName = "限时星愿币",
coinType = 3800,
price = 1,
limitNum = 12,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
itemIds = {
214
},
beginShowTime = {
seconds = 1736697600
},
AvailableTips = 1
},
[9309] = {
mallId = 177,
commodityId = 9309,
commodityName = "限时星愿币",
coinType = 3801,
price = 1,
limitNum = 12,
beginTime = {
seconds = 1741622400
},
endTime = {
seconds = 1755791999
},
itemIds = {
214
},
beginShowTime = {
seconds = 1741622400
}
},
[9310] = {
mallId = 177,
commodityId = 9310,
commodityName = "限时星愿币",
coinType = 3801,
price = 1,
limitNum = 12,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
itemIds = {
214
},
beginShowTime = {
seconds = 1741622400
}
},
[9174] = {
mallId = 23,
commodityId = 9174,
commodityName = "星钻兑换紫葡萄",
coinType = 1,
price = 10,
limitNum = 150,
itemIds = {
317119
},
itemMaxOwnNums = {
150
}
},
[8830] = {
mallId = 144,
commodityId = 8830,
commodityName = "心语宝匣",
coinType = 226,
price = 300,
limitNum = 9999,
itemIds = {
320122
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640123,
620770,
610322,
620775,
4,
4
}
}
}
}
}
}
},
[8831] = {
mallId = 144,
commodityId = 8831,
commodityName = "心语宝匣",
coinType = 226,
price = 300,
limitNum = 9999,
itemIds = {
310305
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640123,
620770,
610322,
620775,
1,
4
}
}
}
}
}
}
},
[9175] = {
mallId = 105,
commodityId = 9175,
commodityName = "庆祝胜利",
coinType = 211,
price = 40,
limitNum = 1,
gender = 0,
itemIds = {
720902
}
},
[9177] = {
mallId = 145,
commodityId = 9177,
commodityName = "冰雪圆舞曲 甄姬",
coinType = 218,
price = 520,
discountPrice = 300,
limitNum = 1,
itemIds = {
410330
},
sortId = 1,
sortName = v79
},
[9178] = {
mallId = 145,
commodityId = 9178,
commodityName = "霓裳风华 杨玉环",
coinType = 218,
price = 520,
discountPrice = 300,
limitNum = 1,
itemIds = {
410320
},
sortId = 1,
sortName = v79
},
[9179] = {
mallId = 145,
commodityId = 9179,
commodityName = "节奏热浪 阿轲",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410060
},
sortId = 3,
sortName = v80
},
[9180] = {
mallId = 145,
commodityId = 9180,
commodityName = "蓝屏警告 典韦",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410080
},
sortId = 3,
sortName = v80
},
[9181] = {
mallId = 145,
commodityId = 9181,
commodityName = "剑圣  宫本武藏",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404620
},
sortId = 3,
sortName = v80
},
[9182] = {
mallId = 145,
commodityId = 9182,
commodityName = "东方曜",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404210
},
sortId = 3,
sortName = v80
},
[9183] = {
mallId = 145,
commodityId = 9183,
commodityName = "全息碎影 孙悟空",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403020
},
sortId = 3,
sortName = v80
},
[9184] = {
mallId = 145,
commodityId = 9184,
commodityName = "超时空战士 狄仁杰",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404140
},
sortId = 3,
sortName = v80
},
[9185] = {
mallId = 145,
commodityId = 9185,
commodityName = "异界灵契 孙尚香",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403000
},
sortId = 3,
sortName = v80
},
[9186] = {
mallId = 145,
commodityId = 9186,
commodityName = "挚爱之约 孙策",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402120
},
sortId = 3,
sortName = v80
},
[9187] = {
mallId = 145,
commodityId = 9187,
commodityName = "龙胆 赵云",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402990
},
sortId = 3,
sortName = v80
},
[9188] = {
mallId = 145,
commodityId = 9188,
commodityName = "追逃游戏 安琪拉",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403010
},
sortId = 3,
sortName = v80
},
[9189] = {
mallId = 145,
commodityId = 9189,
commodityName = "音你心动 小乔",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402130
},
sortId = 3,
sortName = v80
},
[9190] = {
mallId = 145,
commodityId = 9190,
commodityName = "影龙天霄 兰陵王",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402140
},
sortId = 3,
sortName = v80
},
[9191] = {
mallId = 145,
commodityId = 9191,
commodityName = "永冻蔷薇",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630522
},
sortId = 1,
sortName = v79
},
[9192] = {
mallId = 145,
commodityId = 9192,
commodityName = "长乐未央",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620784
},
sortId = 1,
sortName = v79
},
[9193] = {
mallId = 145,
commodityId = 9193,
commodityName = "闪耀应援",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640124
},
sortId = 4,
sortName = v81
},
[9194] = {
mallId = 145,
commodityId = 9194,
commodityName = "电竞一号",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620731
},
sortId = 4,
sortName = v81
},
[9195] = {
mallId = 145,
commodityId = 9195,
commodityName = "鸣雷",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640096
},
sortId = 4,
sortName = v81
},
[9196] = {
mallId = 145,
commodityId = 9196,
commodityName = "绛天战刃",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620278
},
sortId = 4,
sortName = v81
},
[9197] = {
mallId = 145,
commodityId = 9197,
commodityName = "全息灵庙",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620513
},
sortId = 4,
sortName = v81
},
[9198] = {
mallId = 145,
commodityId = 9198,
commodityName = "脉冲饰带",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620563
},
sortId = 4,
sortName = v81
},
[9199] = {
mallId = 145,
commodityId = 9199,
commodityName = "异界旅伴",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620454
},
sortId = 4,
sortName = v81
},
[9200] = {
mallId = 145,
commodityId = 9200,
commodityName = "挚爱之锚",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620279
},
sortId = 4,
sortName = v81
},
[9201] = {
mallId = 145,
commodityId = 9201,
commodityName = "心动热麦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620495
},
sortId = 4,
sortName = v81
},
[9202] = {
mallId = 145,
commodityId = 9202,
commodityName = "琳琅千机",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620494
},
sortId = 4,
sortName = v81
},
[9203] = {
mallId = 145,
commodityId = 9203,
commodityName = "龙胆亮银枪",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620427
},
sortId = 4,
sortName = v81
},
[9204] = {
mallId = 145,
commodityId = 9204,
commodityName = "月光精灵",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630295
},
sortId = 4,
sortName = v81
},
[9205] = {
mallId = 145,
commodityId = 9205,
commodityName = "心动和弦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620280
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
AvailableTips = 1,
sortId = 4,
sortName = v81
},
[9206] = {
mallId = 145,
commodityId = 9206,
commodityName = "影龙之锋",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620281
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
sortId = 4,
sortName = v81
},
[9207] = {
mallId = 145,
commodityId = 9207,
commodityName = "冰雪邀约",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
720972
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
sortId = 1,
sortName = v79
},
[9208] = {
mallId = 145,
commodityId = 9208,
commodityName = "蓬莱飞仙",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
720973
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
sortId = 1,
sortName = v79
},
[9209] = {
mallId = 145,
commodityId = 9209,
commodityName = "滚雪球啰",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711376
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
AvailableTips = 1,
sortId = 1,
sortName = v79
},
[9210] = {
mallId = 145,
commodityId = 9210,
commodityName = "犹抱琵琶",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711374
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
AvailableTips = 1,
sortId = 1,
sortName = v79
},
[9211] = {
mallId = 145,
commodityId = 9211,
commodityName = "乘风破浪 夏侯惇",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404450
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
sortId = 2,
sortName = "返场时装"
},
[9212] = {
mallId = 145,
commodityId = 9212,
commodityName = "缤纷绘卷 张良",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404460
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
sortId = 2,
sortName = "返场时装"
},
[9501] = {
mallId = 162,
commodityId = 9501,
commodityName = "玩偶修复师 多莉",
coinType = 203,
price = 320,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 1,
gender = 0,
itemIds = {
410350
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[9502] = {
mallId = 162,
commodityId = 9502,
commodityName = "粉红吱吱",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 2,
gender = 0,
itemIds = {
620778
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40
},
[9503] = {
mallId = 162,
commodityId = 9503,
commodityName = "趴趴猪",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 3,
gender = 0,
itemIds = {
630514
}
},
[9504] = {
mallId = 162,
commodityId = 9504,
commodityName = "编织睛彩",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 4,
gender = 0,
itemIds = {
610325
}
},
[9505] = {
mallId = 162,
commodityId = 9505,
commodityName = "甜心女仆 莉迪亚",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 5,
gender = 0,
itemIds = {
410300
}
},
[9506] = {
mallId = 162,
commodityId = 9506,
commodityName = "萌犬管家 艾尔弗斯",
coinType = 203,
price = 80,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 6,
gender = 0,
itemIds = {
410290
}
},
[9507] = {
mallId = 162,
commodityId = 9507,
commodityName = "缤纷糖果罐",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 7,
gender = 0,
itemIds = {
620781
}
},
[9508] = {
mallId = 162,
commodityId = 9508,
commodityName = "爱心十字绷",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 8,
gender = 0,
itemIds = {
610328
}
},
[9509] = {
mallId = 162,
commodityId = 9509,
commodityName = "午后茶歇",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 9,
gender = 0,
itemIds = {
630519
}
},
[9510] = {
mallId = 162,
commodityId = 9510,
commodityName = "乐桃桃",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 10,
gender = 0,
itemIds = {
410310
}
},
[9511] = {
mallId = 162,
commodityId = 9511,
commodityName = "多莉头像",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 11,
gender = 0,
itemIds = {
860192
}
},
[9512] = {
mallId = 162,
commodityId = 9512,
commodityName = "疗愈天使",
coinType = 203,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 12,
gender = 0,
itemIds = {
840271
}
},
[9513] = {
mallId = 162,
commodityId = 9513,
commodityName = "星愿币",
coinType = 203,
price = 1,
limitNum = 9999,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
order = 13,
gender = 0,
itemIds = {
2
}
},
[9601] = {
mallId = 176,
commodityId = 9601,
commodityName = "糯糯青团头饰",
coinType = 3622,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
630549
}
},
[9602] = {
mallId = 176,
commodityId = 9602,
commodityName = "烟柳纷纷头像框",
coinType = 3622,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
329932
}
},
[9603] = {
mallId = 176,
commodityId = 9603,
commodityName = "阵营卡",
coinType = 3622,
price = 20,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
200101
}
},
[9604] = {
mallId = 176,
commodityId = 9604,
commodityName = "英雄体验卡自选",
coinType = 3622,
price = 20,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
331024
}
},
[9605] = {
mallId = 176,
commodityId = 9605,
commodityName = "大王排位升星券",
coinType = 3622,
price = 20,
limitNum = 2,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
203006
},
AvailableTips = 1
},
[9606] = {
mallId = 176,
commodityId = 9606,
commodityName = "峡谷币",
coinType = 3622,
price = 20,
limitNum = 5,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
3541
},
itemNums = {
10
},
AvailableTips = 1
},
[9607] = {
mallId = 176,
commodityId = 9607,
commodityName = "狼人币",
coinType = 3622,
price = 20,
limitNum = 5,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
gender = 0,
itemIds = {
13
},
itemNums = {
10
}
},
[9608] = {
mallId = 176,
commodityId = 9608,
commodityName = "笑不出来面饰",
coinType = 3623,
price = 70,
limitNum = 1,
gender = 0,
itemIds = {
830098
}
},
[9609] = {
mallId = 176,
commodityId = 9609,
commodityName = "磷虾",
coinType = 3623,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
219000
},
itemNums = {
2
}
},
[9610] = {
mallId = 176,
commodityId = 9610,
commodityName = "阵营卡",
coinType = 3623,
price = 20,
limitNum = 1,
gender = 0,
itemIds = {
200101
}
},
[9611] = {
mallId = 176,
commodityId = 9611,
commodityName = "峡谷币",
coinType = 3623,
price = 10,
limitNum = 4,
gender = 0,
itemIds = {
3541
},
itemNums = {
10
}
},
[9612] = {
mallId = 176,
commodityId = 9612,
commodityName = "狼人币",
coinType = 3623,
price = 10,
limitNum = 4,
gender = 0,
itemIds = {
13
},
itemNums = {
10
}
},
[9614] = {
mallId = 152,
commodityId = 9614,
commodityName = "沧海之曜 大乔",
coinType = 218,
price = 520,
discountPrice = 300,
limitNum = 1,
itemIds = {
410940
},
sortId = 1,
sortName = v79
},
[9615] = {
mallId = 152,
commodityId = 9615,
commodityName = "浮梦罗烟 海月",
coinType = 218,
price = 520,
discountPrice = 300,
limitNum = 1,
itemIds = {
410930
},
sortId = 1,
sortName = v79
},
[9616] = {
mallId = 152,
commodityId = 9616,
commodityName = "冰雪圆舞曲 甄姬",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410330
},
sortId = 3,
sortName = v80
},
[9617] = {
mallId = 152,
commodityId = 9617,
commodityName = "霓裳风华 杨玉环",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410320
},
sortId = 3,
sortName = v80
},
[9618] = {
mallId = 152,
commodityId = 9618,
commodityName = "节奏热浪 阿轲",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410060
},
sortId = 3,
sortName = v80
},
[9619] = {
mallId = 152,
commodityId = 9619,
commodityName = "蓝屏警告 典韦",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410080
},
sortId = 3,
sortName = v80
},
[9620] = {
mallId = 152,
commodityId = 9620,
commodityName = "剑圣  宫本武藏",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404620
},
sortId = 3,
sortName = v80
},
[9621] = {
mallId = 152,
commodityId = 9621,
commodityName = "东方曜",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404210
},
sortId = 3,
sortName = v80
},
[9622] = {
mallId = 152,
commodityId = 9622,
commodityName = "全息碎影 孙悟空",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403020
},
sortId = 3,
sortName = v80
},
[9623] = {
mallId = 152,
commodityId = 9623,
commodityName = "超时空战士 狄仁杰",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404140
},
sortId = 3,
sortName = v80
},
[9624] = {
mallId = 152,
commodityId = 9624,
commodityName = "异界灵契 孙尚香",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403000
},
sortId = 3,
sortName = v80
},
[9625] = {
mallId = 152,
commodityId = 9625,
commodityName = "挚爱之约 孙策",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402120
},
sortId = 3,
sortName = v80
},
[9626] = {
mallId = 152,
commodityId = 9626,
commodityName = "龙胆 赵云",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402990
},
sortId = 3,
sortName = v80
},
[9627] = {
mallId = 152,
commodityId = 9627,
commodityName = "追逃游戏 安琪拉",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403010
},
sortId = 3,
sortName = v80
},
[9628] = {
mallId = 152,
commodityId = 9628,
commodityName = "音你心动 小乔",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402130
},
sortId = 3,
sortName = v80
},
[9629] = {
mallId = 152,
commodityId = 9629,
commodityName = "影龙天霄 兰陵王",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402140
},
sortId = 3,
sortName = v80
},
[9630] = {
mallId = 152,
commodityId = 9630,
commodityName = "望海长明",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620848
},
sortId = 1,
sortName = v79
},
[9631] = {
mallId = 152,
commodityId = 9631,
commodityName = "玲珑宝器",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630569
},
sortId = 1,
sortName = v79
},
[9632] = {
mallId = 152,
commodityId = 9632,
commodityName = "永冻蔷薇",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630522
},
sortId = 4,
sortName = v81
},
[9633] = {
mallId = 152,
commodityId = 9633,
commodityName = "长乐未央",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620784
},
sortId = 4,
sortName = v81
},
[9634] = {
mallId = 152,
commodityId = 9634,
commodityName = "闪耀应援",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640124
},
sortId = 4,
sortName = v81
},
[9635] = {
mallId = 152,
commodityId = 9635,
commodityName = "电竞一号",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620731
},
sortId = 4,
sortName = v81
},
[9636] = {
mallId = 152,
commodityId = 9636,
commodityName = "鸣雷",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640096
},
sortId = 4,
sortName = v81
},
[9637] = {
mallId = 152,
commodityId = 9637,
commodityName = "绛天战刃",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620278
},
sortId = 4,
sortName = v81
},
[9638] = {
mallId = 152,
commodityId = 9638,
commodityName = "全息灵庙",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620513
},
sortId = 4,
sortName = v81
},
[9639] = {
mallId = 152,
commodityId = 9639,
commodityName = "脉冲饰带",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620563
},
sortId = 4,
sortName = v81
},
[9640] = {
mallId = 152,
commodityId = 9640,
commodityName = "异界旅伴",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620454
},
sortId = 4,
sortName = v81
},
[9641] = {
mallId = 152,
commodityId = 9641,
commodityName = "挚爱之锚",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620279
},
sortId = 4,
sortName = v81
},
[9642] = {
mallId = 152,
commodityId = 9642,
commodityName = "心动热麦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620495
},
sortId = 4,
sortName = v81
},
[9643] = {
mallId = 152,
commodityId = 9643,
commodityName = "琳琅千机",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620494
},
sortId = 4,
sortName = v81
},
[9644] = {
mallId = 152,
commodityId = 9644,
commodityName = "龙胆亮银枪",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620427
},
sortId = 4,
sortName = v81
},
[9645] = {
mallId = 152,
commodityId = 9645,
commodityName = "月光精灵",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630295
},
sortId = 4,
sortName = v81
},
[9646] = {
mallId = 152,
commodityId = 9646,
commodityName = "心动和弦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620280
},
expireDays = {
5
},
sortId = 4,
sortName = v81
},
[9647] = {
mallId = 152,
commodityId = 9647,
commodityName = "影龙之锋",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620281
},
sortId = 4,
sortName = v81
},
[9648] = {
mallId = 152,
commodityId = 9648,
commodityName = "鱼跃鸢飞",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
720968
},
sortId = 1,
sortName = v79
},
[9649] = {
mallId = 152,
commodityId = 9649,
commodityName = "罗烟海市",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
722028
},
sortId = 1,
sortName = v79
},
[9650] = {
mallId = 152,
commodityId = 9650,
commodityName = "安心摸鱼",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711373
},
sortId = 1,
sortName = v79
},
[9651] = {
mallId = 152,
commodityId = 9651,
commodityName = "雅韵悠长",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711490
},
sortId = 1,
sortName = v79
},
[9652] = {
mallId = 152,
commodityId = 9652,
commodityName = "乘风破浪 夏侯惇",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404450
},
AvailableTips = 1,
sortId = 2,
sortName = "返场时装"
},
[9653] = {
mallId = 152,
commodityId = 9653,
commodityName = "缤纷绘卷 张良",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404460
},
AvailableTips = 1,
sortId = 2,
sortName = "返场时装"
},
[9253] = {
mallId = 192,
commodityId = 9253,
commodityName = "奶油云朵乐园5日体验",
coinType = 317130,
price = 30,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
itemIds = {
218169
},
sortId = 1
},
[9254] = {
mallId = 192,
commodityId = 9254,
commodityName = "磷虾*2",
coinType = 317130,
price = 30,
limitNum = 5,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1747065599
},
itemIds = {
219000
},
sortId = 2
},
[9255] = {
mallId = 192,
commodityId = 9255,
commodityName = "召唤铃*5",
coinType = 317130,
price = 30,
limitNum = 2,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1747065599
},
itemIds = {
200620
},
sortId = 2
},
[9256] = {
mallId = 192,
commodityId = 9256,
commodityName = "喵喵币*25",
coinType = 317130,
price = 30,
limitNum = 4,
beginTime = {
seconds = 1746072000
},
endTime = {
seconds = 1746979199
},
itemIds = {
3544
},
itemNums = {
25
},
sortId = 2
},
[9260] = {
mallId = 191,
commodityId = 9260,
commodityName = "幻彩宝匣",
coinType = 3556,
price = 300,
limitNum = 9999,
itemIds = {
320117
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
410840,
411190,
630606,
610392,
620900,
640163,
4,
6
}
}
}
}
}
}
},
[9261] = {
mallId = 191,
commodityId = 9261,
commodityName = "幻彩宝匣",
coinType = 3556,
price = 300,
limitNum = 9999,
itemIds = {
310327
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
410840,
411190,
630606,
610392,
620900,
640163,
1,
6
}
}
}
}
}
}
},
[9140] = {
mallId = 193,
commodityId = 9140,
commodityName = "节奏热浪 阿轲",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410060
},
sortId = 3,
sortName = v79
},
[9141] = {
mallId = 193,
commodityId = 9141,
commodityName = "蓝屏警告 典韦",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
410080
},
sortId = 3,
sortName = v79
},
[9142] = {
mallId = 193,
commodityId = 9142,
commodityName = "剑圣  宫本武藏",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404620
},
sortId = 3,
sortName = v80
},
[9143] = {
mallId = 193,
commodityId = 9143,
commodityName = "东方曜",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404210
},
sortId = 3,
sortName = v80
},
[9144] = {
mallId = 193,
commodityId = 9144,
commodityName = "全息碎影 孙悟空",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403020
},
sortId = 3,
sortName = v80
},
[9145] = {
mallId = 193,
commodityId = 9145,
commodityName = "超时空战士 狄仁杰",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404140
},
sortId = 3,
sortName = v80
},
[9146] = {
mallId = 193,
commodityId = 9146,
commodityName = "异界灵契 孙尚香",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403000
},
sortId = 3,
sortName = v80
},
[9147] = {
mallId = 193,
commodityId = 9147,
commodityName = "挚爱之约 孙策",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402120
},
sortId = 3,
sortName = v80
},
[9148] = {
mallId = 193,
commodityId = 9148,
commodityName = "龙胆 赵云",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402990
},
sortId = 3,
sortName = v80
},
[9149] = {
mallId = 193,
commodityId = 9149,
commodityName = "追逃游戏 安琪拉",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
403010
},
sortId = 3,
sortName = v80
},
[9150] = {
mallId = 193,
commodityId = 9150,
commodityName = "音你心动 小乔",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402130
},
sortId = 3,
sortName = v80
},
[9151] = {
mallId = 193,
commodityId = 9151,
commodityName = "影龙天霄 兰陵王",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
402140
},
sortId = 3,
sortName = v80
},
[9152] = {
mallId = 193,
commodityId = 9152,
commodityName = "闪耀应援",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640124
},
sortId = 4,
sortName = v79
},
[9153] = {
mallId = 193,
commodityId = 9153,
commodityName = "电竞一号",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620731
},
sortId = 4,
sortName = v79
},
[9154] = {
mallId = 193,
commodityId = 9154,
commodityName = "鸣雷",
coinType = 218,
price = 90,
limitNum = 1,
itemIds = {
640096
},
sortId = 4,
sortName = v81
},
[9155] = {
mallId = 193,
commodityId = 9155,
commodityName = "绛天战刃",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620278
},
sortId = 4,
sortName = v81
},
[9156] = {
mallId = 193,
commodityId = 9156,
commodityName = "全息灵庙",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620513
},
sortId = 4,
sortName = v81
},
[9157] = {
mallId = 193,
commodityId = 9157,
commodityName = "脉冲饰带",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620563
},
sortId = 4,
sortName = v81
},
[9158] = {
mallId = 193,
commodityId = 9158,
commodityName = "异界旅伴",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620454
},
sortId = 4,
sortName = v81
},
[9159] = {
mallId = 193,
commodityId = 9159,
commodityName = "挚爱之锚",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620279
},
sortId = 4,
sortName = v81
},
[9160] = {
mallId = 193,
commodityId = 9160,
commodityName = "心动热麦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620495
},
sortId = 4,
sortName = v81
},
[9161] = {
mallId = 193,
commodityId = 9161,
commodityName = "琳琅千机",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620494
},
sortId = 4,
sortName = v81
},
[9162] = {
mallId = 193,
commodityId = 9162,
commodityName = "龙胆亮银枪",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620427
},
sortId = 4,
sortName = v81
},
[9163] = {
mallId = 193,
commodityId = 9163,
commodityName = "月光精灵",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
630295
},
sortId = 4,
sortName = v81
},
[9164] = {
mallId = 193,
commodityId = 9164,
commodityName = "心动和弦",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620280
},
sortId = 4,
sortName = v81
},
[9165] = {
mallId = 193,
commodityId = 9165,
commodityName = "影龙之锋",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
620281
},
expireDays = {
3
},
sortId = 4,
sortName = v81
},
[9166] = {
mallId = 193,
commodityId = 9166,
commodityName = "鱼跃鸢飞",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
720968
},
sortId = 1,
sortName = v79
},
[9167] = {
mallId = 193,
commodityId = 9167,
commodityName = "罗烟海市",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
722028
},
sortId = 1,
sortName = v79
},
[9168] = {
mallId = 193,
commodityId = 9168,
commodityName = "安心摸鱼",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711373
},
sortId = 1,
sortName = v79
},
[9169] = {
mallId = 193,
commodityId = 9169,
commodityName = "雅韵悠长",
coinType = 218,
price = 60,
limitNum = 1,
itemIds = {
711490
},
sortId = 1,
sortName = v79
},
[9170] = {
mallId = 193,
commodityId = 9170,
commodityName = "乘风破浪 夏侯惇",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404450
},
sortId = 2,
sortName = "返场时装"
},
[9171] = {
mallId = 193,
commodityId = 9171,
commodityName = "缤纷绘卷 张良",
coinType = 218,
price = 520,
limitNum = 1,
itemIds = {
404460
},
sortId = 2,
sortName = "返场时装"
},
[9410] = {
mallId = 194,
commodityId = 9410,
commodityName = "星愿币",
coinType = 3560,
price = 60,
limitNum = 9999,
gender = 0,
itemIds = {
2
}
},
[9411] = {
mallId = 194,
commodityId = 9411,
commodityName = "测试衣服3天",
coinType = 3560,
price = 60,
limitNum = 1,
itemIds = {
510374
}
},
[9412] = {
mallId = 194,
commodityId = 9412,
commodityName = "测试衣服永久",
coinType = 3560,
price = 60,
limitNum = 1,
itemIds = {
510374
}
},
[9514] = {
mallId = 196,
commodityId = 9514,
commodityName = "自由之蝶 凡妮莎",
coinType = 3950,
price = 320,
limitNum = 1,
order = 1,
gender = 0,
itemIds = {
410660
},
canGift = true,
addIntimacy = 600,
giftCoinType = 3950,
giftPrice = 320,
AvailableTips = 1
},
[9515] = {
mallId = 196,
commodityId = 9515,
commodityName = "晶珀之梦",
coinType = 3950,
price = 80,
limitNum = 1,
order = 2,
gender = 0,
itemIds = {
620838
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80
},
[9516] = {
mallId = 196,
commodityId = 9516,
commodityName = "晶蝶幻面",
coinType = 3950,
price = 80,
limitNum = 1,
order = 3,
gender = 0,
itemIds = {
610353
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80
},
[9517] = {
mallId = 196,
commodityId = 9517,
commodityName = "蝶梦尘铃",
coinType = 3950,
price = 80,
limitNum = 1,
order = 4,
gender = 0,
itemIds = {
630538
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80
},
[9518] = {
mallId = 196,
commodityId = 9518,
commodityName = "星闪流萤 埃莉诺",
coinType = 3950,
price = 80,
limitNum = 1,
order = 5,
gender = 0,
itemIds = {
410640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
AvailableTips = 1
},
[9519] = {
mallId = 196,
commodityId = 9519,
commodityName = "怪奇菌学家 奇奇奥",
coinType = 3950,
price = 80,
limitNum = 1,
order = 6,
gender = 0,
itemIds = {
410650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
AvailableTips = 1
},
[9520] = {
mallId = 196,
commodityId = 9520,
commodityName = "奇幻蘑菇手持",
coinType = 3950,
price = 40,
limitNum = 1,
order = 7,
gender = 0,
itemIds = {
640144
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40
},
[9521] = {
mallId = 196,
commodityId = 9521,
commodityName = "星愿苹果头饰",
coinType = 3950,
price = 40,
limitNum = 1,
order = 8,
gender = 0,
itemIds = {
630526
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40
},
[9522] = {
mallId = 196,
commodityId = 9522,
commodityName = "松树之境面饰",
coinType = 3950,
price = 40,
limitNum = 1,
order = 9,
gender = 0,
itemIds = {
610403
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40
},
[9523] = {
mallId = 196,
commodityId = 9523,
commodityName = "琪露露",
coinType = 3950,
price = 40,
limitNum = 1,
order = 10,
gender = 0,
itemIds = {
404690
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40
},
[9524] = {
mallId = 196,
commodityId = 9524,
commodityName = "动态称号",
coinType = 3950,
price = 40,
limitNum = 1,
order = 11,
gender = 0,
itemIds = {
850659
}
},
[9525] = {
mallId = 196,
commodityId = 9525,
commodityName = "动态昵称框",
coinType = 3950,
price = 40,
limitNum = 1,
order = 12,
gender = 0,
itemIds = {
820194
}
},
[9526] = {
mallId = 196,
commodityId = 9526,
commodityName = "动态头像框",
coinType = 3950,
price = 40,
limitNum = 1,
order = 13,
gender = 0,
itemIds = {
840310
}
},
[9527] = {
mallId = 196,
commodityId = 9527,
commodityName = "凡妮莎头像",
coinType = 3950,
price = 40,
limitNum = 1,
order = 14,
gender = 0,
itemIds = {
860211
}
},
[9528] = {
mallId = 196,
commodityId = 9528,
commodityName = "星愿币",
coinType = 3950,
price = 1,
limitNum = 9999,
order = 15,
gender = 0,
itemIds = {
2
}
},
[9529] = {
mallId = 144,
commodityId = 9529,
commodityName = "音语宝匣",
coinType = 227,
price = 300,
limitNum = 9999,
itemIds = {
320135
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640158,
620925,
630629,
610376,
4,
4
}
}
}
}
}
}
},
[9530] = {
mallId = 144,
commodityId = 9530,
commodityName = "音语宝匣",
coinType = 227,
price = 300,
limitNum = 9999,
itemIds = {
310346
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
640158,
620925,
630629,
610376,
1,
4
}
}
}
}
}
}
},
[9531] = {
mallId = 194,
commodityId = 9531,
commodityName = "天眼萌仔",
coinType = 3560,
price = 500,
limitNum = 1,
itemIds = {
410700
}
},
[9311] = {
mallId = 197,
commodityId = 9311,
commodityName = "限时星愿币",
coinType = 3801,
price = 1,
limitNum = 12,
beginTime = {
seconds = 1751558400
},
endTime = {
seconds = 1755791999
},
itemIds = {
214
},
beginShowTime = {
seconds = 1751558400
}
},
[9555] = {
mallId = 198,
commodityId = 9555,
commodityName = "荔枝妞妞",
coinType = 3639,
price = 10,
limitNum = 1,
itemIds = {
620398
}
},
[9556] = {
mallId = 198,
commodityId = 9556,
commodityName = "背享净界",
coinType = 3639,
price = 20,
limitNum = 1,
itemIds = {
620705
}
},
[9557] = {
mallId = 198,
commodityId = 9557,
commodityName = "心动和弦",
coinType = 3639,
price = 20,
limitNum = 1,
itemIds = {
620280
}
},
[9558] = {
mallId = 198,
commodityId = 9558,
commodityName = "净净管家",
coinType = 3639,
price = 30,
limitNum = 1,
itemIds = {
410220
}
},
[9559] = {
mallId = 200,
commodityId = 9559,
commodityName = "御剑术",
coinType = 3640,
price = 60,
discountPrice = 30,
limitNum = 1,
order = 1,
gender = 0,
itemIds = {
300218
}
},
[9560] = {
mallId = 200,
commodityId = 9560,
commodityName = "斩立决",
coinType = 3640,
price = 60,
discountPrice = 30,
limitNum = 1,
order = 2,
gender = 0,
itemIds = {
300219
}
},
[9561] = {
mallId = 200,
commodityId = 9561,
commodityName = "万剑归宗",
coinType = 3640,
price = 240,
discountPrice = 60,
limitNum = 1,
order = 3,
gender = 0,
itemIds = {
300212
}
},
[9562] = {
mallId = 200,
commodityId = 9562,
commodityName = "伞影婆娑",
coinType = 3640,
price = 240,
discountPrice = 90,
limitNum = 1,
order = 4,
gender = 0,
itemIds = {
300210
}
},
[9563] = {
mallId = 200,
commodityId = 9563,
commodityName = "剑走星驰",
coinType = 3640,
price = 240,
discountPrice = 120,
limitNum = 1,
order = 5,
gender = 0,
itemIds = {
300207
}
},
[9564] = {
mallId = 200,
commodityId = 9564,
commodityName = "暗无天日",
coinType = 3640,
price = 240,
limitNum = 1,
order = 6,
gender = 0,
itemIds = {
303513
}
},
[9565] = {
mallId = 200,
commodityId = 9565,
commodityName = "白色修罗 铠",
coinType = 3640,
price = 180,
limitNum = 1,
order = 7,
gender = 0,
showRedPoint = 1,
itemIds = {
302031
}
},
[9566] = {
mallId = 200,
commodityId = 9566,
commodityName = "白色修罗",
coinType = 3640,
price = 180,
limitNum = 1,
order = 8,
gender = 0,
showRedPoint = 1,
itemIds = {
407026
},
AvailableTips = 1
},
[9570] = {
mallId = 204,
commodityId = 9570,
commodityName = "绿装-月夜狼宝",
coinType = 3643,
price = 60,
limitNum = 1,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 1,
gender = 0,
showRedPoint = 1,
itemIds = {
510342
},
beginShowTime = {
seconds = 1748966400
}
},
[9571] = {
mallId = 204,
commodityId = 9571,
commodityName = "卡包5档*1",
coinType = 3643,
price = 40,
limitNum = 1,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 2,
gender = 0,
showRedPoint = 1,
itemIds = {
290025
},
beginShowTime = {
seconds = 1748966400
}
},
[9572] = {
mallId = 204,
commodityId = 9572,
commodityName = "互动道具-送钻戒*1",
coinType = 3643,
price = 30,
limitNum = 1,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 3,
gender = 0,
itemIds = {
240408
},
beginShowTime = {
seconds = 1748966400
}
},
[9573] = {
mallId = 204,
commodityId = 9573,
commodityName = "互动道具-祝福*1",
coinType = 3643,
price = 10,
limitNum = 1,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 4,
gender = 0,
itemIds = {
240419
},
beginShowTime = {
seconds = 1748966400
}
},
[9574] = {
mallId = 204,
commodityId = 9574,
commodityName = "互动道具-送花*1",
coinType = 3643,
price = 10,
limitNum = 1,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 5,
gender = 0,
itemIds = {
240404
},
beginShowTime = {
seconds = 1748966400
}
},
[9575] = {
mallId = 204,
commodityId = 9575,
commodityName = "互动道具-疯狂点赞*1",
coinType = 3643,
price = 2,
limitNum = 5,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 6,
gender = 0,
showRedPoint = 1,
itemIds = {
240416
},
beginShowTime = {
seconds = 1748966400
}
},
[9576] = {
mallId = 204,
commodityId = 9576,
commodityName = "互动道具-爱心*1",
coinType = 3643,
price = 2,
limitNum = 5,
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750607999
},
order = 7,
gender = 0,
showRedPoint = 1,
itemIds = {
240403
},
beginShowTime = {
seconds = 1748966400
}
},
[9580] = {
mallId = 201,
commodityId = 9580,
commodityName = "紫装 小爱",
coinType = 3641,
price = 800,
limitNum = 1,
beginTime = {
seconds = 1746374400
},
endTime = v71,
order = 1,
gender = 0,
itemIds = {
400700
},
beginShowTime = {
seconds = 1746374400
}
},
[9581] = {
mallId = 201,
commodityId = 9581,
commodityName = "蓝装 喵小萌",
coinType = 3641,
price = 450,
limitNum = 1,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 2,
gender = 0,
itemIds = {
410370
},
beginShowTime = {
seconds = 1745942400
}
},
[9582] = {
mallId = 201,
commodityId = 9582,
commodityName = "动作 超级跳喵喵舞",
coinType = 3641,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 3,
gender = 0,
itemIds = {
720194
},
beginShowTime = {
seconds = 1745942400
}
},
[9583] = {
mallId = 201,
commodityId = 9583,
commodityName = "头像 喵小萌",
coinType = 3641,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 4,
gender = 0,
itemIds = {
860209
},
beginShowTime = {
seconds = 1745942400
}
},
[9584] = {
mallId = 201,
commodityId = 9584,
commodityName = "小新星星眼表情",
coinType = 3641,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1746374400
},
endTime = v71,
order = 5,
gender = 0,
itemIds = {
710054
},
beginShowTime = {
seconds = 1746374400
}
},
[9585] = {
mallId = 201,
commodityId = 9585,
commodityName = "蓝色小新表情—打起精神",
coinType = 3641,
price = 50,
limitNum = 1,
beginTime = {
seconds = 1746374400
},
endTime = v71,
order = 6,
gender = 0,
itemIds = {
710053
},
beginShowTime = {
seconds = 1746374400
}
},
[9586] = {
mallId = 201,
commodityId = 9586,
commodityName = "蓝色小新动作—呼拉舞",
coinType = 3641,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1746374400
},
endTime = v71,
order = 7,
gender = 0,
itemIds = {
720052
},
beginShowTime = {
seconds = 1746374400
}
},
[9587] = {
mallId = 201,
commodityId = 9587,
commodityName = "蓝色小新动作—带劲摇摆",
coinType = 3641,
price = 100,
limitNum = 1,
beginTime = {
seconds = 1746374400
},
endTime = v71,
order = 8,
gender = 0,
itemIds = {
720051
},
beginShowTime = {
seconds = 1746374400
}
},
[9588] = {
mallId = 201,
commodityId = 9588,
commodityName = "福利碎片*5",
coinType = 3641,
price = 50,
limitNum = 20,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 9,
gender = 0,
itemIds = {
3134
},
itemNums = {
5
},
beginShowTime = {
seconds = 1745942400
}
},
[9589] = {
mallId = 201,
commodityId = 9589,
commodityName = "通用排位升星券*1",
coinType = 3641,
price = 50,
limitNum = 10,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 10,
gender = 0,
itemIds = {
200020
},
beginShowTime = {
seconds = 1745942400
}
},
[9590] = {
mallId = 201,
commodityId = 9590,
commodityName = "娱乐排位升星券*1",
coinType = 3641,
price = 50,
limitNum = 10,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 11,
gender = 0,
itemIds = {
203001
},
beginShowTime = {
seconds = 1745942400
}
},
[9591] = {
mallId = 201,
commodityId = 9591,
commodityName = "任务加速器",
coinType = 3641,
price = 50,
limitNum = 10,
beginTime = {
seconds = 1745942400
},
endTime = v71,
order = 12,
gender = 0,
beginShowTime = {
seconds = 1745942400
}
}
}

local mt = {
mallId = 18,
commodityName = "玫小星荧光棒",
coinType = 2007,
price = 6,
limitType = "MCL_LifeLongLimit",
itemIds = {
200635
},
itemNums = {
1
},
disableNtf = false,
canGift = false,
isGrand = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data