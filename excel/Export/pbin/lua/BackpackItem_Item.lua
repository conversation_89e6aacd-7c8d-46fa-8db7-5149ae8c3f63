--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表.xlsx: 道具

local v0 = "ItemType_AutoUse"

local v1 = 999999

local v2 = 9999

local v3 = 9999999

local v4 = 99999

local v5 = 2

local v6 = "CDN:T_Common_Item_System_Stationery_01"

local v7 = 1

local v8 = "活动"

local v9 = {
{
itemId = 6,
itemNum = 10
}
}

local v10 = {
{
itemId = 2072,
itemNum = 1
}
}

local data = {
[181881] = {
id = 181881,
effect = true,
stackedNum = 1,
maxNum = 999,
quality = 1,
name = "脚力增强鞋",
desc = "使用后，可向前跑出3-6个格子",
icon = "Icon_Cup_08",
picture = "Icon_Cup_08",
bagId = 1,
bHideInBag = true
},
[181882] = {
id = 181882,
effect = true,
stackedNum = 1,
maxNum = 999,
quality = 1,
name = "涡轮动力滑板",
desc = "使用后，可向前冲刺8个格子",
icon = "CDN:T_Common_Item_Connan_Skateboard",
picture = "CDN:T_Common_Item_Connan_Skateboard",
bagId = 1,
bHideInBag = true
},
[181883] = {
id = 181883,
effect = true,
stackedNum = 1,
maxNum = 999,
quality = 1,
name = "麻醉手表",
desc = "使用后，可麻醉敌人两回合不能行动",
icon = "CDN:T_Common_Item_Connan_Watch",
picture = "CDN:T_Common_Item_Connan_Watch",
bagId = 1,
bHideInBag = true
},
[181991] = {
id = 181991,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "星影券",
desc = "用于在【隐形守护者】玩法中解锁更多剧情章节",
icon = "CDN:T_Common_Item_System_MovieTicket",
picture = "CDN:T_Common_Item_System_MovieTicket",
getWay = "福利商店",
jumpId = {
20
},
bagId = 1,
bHideInBag = true
},
[181992] = {
id = 181992,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "打榜星星",
desc = "用于在【隐形守护者】玩法中兑换鲜花与鸡蛋",
icon = "CDN:T_Common_Item_System_MovieStar",
picture = "CDN:T_Common_Item_System_MovieStar",
getWay = "福利商店",
jumpId = {
20
},
bagId = 1,
bHideInBag = true
},
[200001] = {
id = 200001,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "活动兑换代币",
desc = "从XX活动获得代币，可用于在活动商店中兑换奖励。",
icon = "T_Item_Acorn",
getWay = "商城",
jumpId = {
15
},
bagId = 1
},
[200002] = {
id = 200002,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 5,
name = "亲密道具",
desc = "可对好友使用，增加亲密度。",
icon = "T_Common_Item_Intimacy_01",
picture = "T_Common_Item_Intimacy_01",
getWay = "商城",
jumpId = {
15
},
bagId = 1,
useType = "IUTO_Send",
useParam = {
5
}
},
[200003] = {
id = 200003,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 4,
name = "XX皮肤7天体验卡",
desc = "使用后可体验7天XXX皮肤。",
icon = "T_Item_Nameplate_Hedgehog",
getWay = "商城",
jumpId = {
15
},
bagId = 1
},
[200004] = {
id = 200004,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 5,
name = "7天双倍金币卡",
desc = "使用后7天内参与比赛可获得双倍金币。",
icon = "T_Item_Hamburger",
getWay = "商城",
jumpId = {
15
},
bagId = 1
},
[200005] = {
id = 200005,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "载具喷涂罐",
desc = "用来获得染色载具",
icon = "T_Common_Item_System_BagBig_048",
picture = "T_Common_Item_System_BagBig_048",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
isUsePurchaseDyeing = true
},
[200006] = {
id = 200006,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "时装染色膏",
desc = "用来获得染色时装。",
icon = "CDN:T_Common_Item_Dye_01",
picture = "CDN:T_Common_Item_DyeBig_01",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
isUsePurchaseDyeing = true
},
[200007] = {
id = 200007,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "高级时装染色剂",
desc = "用来获得高等级染色时装。",
icon = "T_Item_Acorn",
getWay = "商城",
jumpId = {
15
},
bagId = 1
},
[200008] = {
id = 200008,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "饰品调色盘",
desc = "用来获得染色饰品。",
icon = "CDN:T_Common_Item_Dye_02",
picture = "CDN:T_Common_Item_DyeBig_02",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
isUsePurchaseDyeing = true
},
[200009] = {
id = 200009,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "高级饰品染色剂",
desc = "用来获得高等级染色饰品。",
icon = "T_Item_Acorn",
getWay = "商城",
jumpId = {
15
},
bagId = 1
},
[200010] = {
id = 200010,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "昵称修改卡",
desc = "用来修改昵称。",
icon = "T_Common_Item_System_Bag_013",
picture = "T_Common_Item_System_BagBig_013",
getWay = "商城",
jumpId = {
71
},
bagId = 1
},
[200011] = {
id = 200011,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "幸运钥匙",
desc = "获得幸运钥匙时，自动抽取奖池内圈奖励！",
icon = "T_DrawReward_Img_Key"
},
[200013] = {
id = 200013,
effect = true,
stackedNum = 99999999,
maxNum = 99999999,
name = "接力闯关生命数",
desc = "每日剩余生命",
icon = "T_UGC_Finish_Icon_Blood",
getWay = v8
},
[200014] = {
id = 200014,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 4,
name = "甜蜜初心",
desc = "可对好友使用，增加1点亲密度。",
icon = "T_Common_Item_Intimacy_01",
picture = "T_Common_Item_IntimacyBig_01",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
useType = "IUTO_Send",
useParam = {
1
}
},
[200015] = {
id = 200015,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 4,
name = "心心糖果",
desc = "可对好友使用，增加5点亲密度。",
icon = "T_Common_Item_Intimacy_02",
picture = "T_Common_Item_IntimacyBig_02",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
useType = "IUTO_Send",
useParam = {
5
}
},
[200016] = {
id = 200016,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 4,
name = "心心宝瓶",
desc = "可对好友使用，增加10点亲密度。",
icon = "T_Common_Item_Intimacy_03",
picture = "T_Common_Item_IntimacyBig_03",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
useType = "IUTO_Send",
useParam = {
10
}
},
[200017] = {
id = 200017,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 4,
name = "心心蜜罐",
desc = "可对好友使用，增加20点亲密度。",
icon = "T_Common_Item_Intimacy_04",
picture = "T_Common_Item_IntimacyBig_04",
getWay = "商城",
jumpId = {
71
},
bagId = 1,
useType = "IUTO_Send",
useParam = {
20
}
},
[200018] = {
id = 200018,
effect = true,
type = "ItemType_QualifyingProtectedCard",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
expiredReplaceItem = {
{
itemId = 4,
itemNum = 1
}
},
quality = 2,
name = "通用排位保护券",
desc = "到达王者段位前，在任意排位赛结算时自动使用，可抵消一次积分扣除。数量随赛季重置清0。",
icon = "T_Common_Item_System_Bag_026",
picture = "T_Common_Item_System_Bag_026",
getWay = v8,
bagId = 1,
qualifyingCardConf = {
priority = 1,
qualifyingCardInfo = {
{
matchType = 0,
rangeLeft = 0,
rangeRight = 2520
}
}
}
},
[200019] = {
id = 200019,
effect = true,
type = "ItemType_QualifyingProtectedCard",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
expiredReplaceItem = {
{
itemId = 4,
itemNum = 1
}
},
quality = 2,
name = "通用排位组队保护券",
desc = "到达王者段位前，在任意组队排位赛结算时自动使用，可抵消一次积分扣除。数量随赛季重置清0。",
icon = "T_Common_Item_System_Bag_031",
picture = "T_Common_Item_System_Bag_031",
getWay = v8,
bagId = 1,
qualifyingCardConf = {
priority = 2,
teamMemberRequire = {
2,
3,
4
},
qualifyingCardInfo = {
{
matchType = 0,
rangeLeft = 0,
rangeRight = 2520
}
}
}
},
[200020] = {
id = 200020,
effect = true,
type = "ItemType_QualifyingAdditionalCard",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
expiredReplaceItem = {
{
itemId = 4,
itemNum = 1
}
},
quality = 2,
name = "通用排位升星券",
desc = "到达王者段位前，在任意排位赛中积分增加时可额外获得当前升一颗星所需积分。数量随赛季重置清0。",
icon = "T_Common_Item_System_Bag_028",
picture = "T_Common_Item_System_Bag_028",
getWay = v8,
bagId = 1,
qualifyingCardConf = {
priority = 1,
qualifyingCardInfo = {
{
matchType = 0,
rangeLeft = 0,
rangeRight = 2520
}
}
}
},
[200021] = {
id = 200021,
effect = true,
type = "ItemType_QualifyingAdditionalCard",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
expiredReplaceItem = {
{
itemId = 4,
itemNum = 1
}
},
quality = 2,
name = "通用排位组队升星券",
desc = "到达王者段位前，在任意组队排位赛中积分增加时可额外获得当前升一颗星所需积分。数量随赛季重置清0。",
icon = "T_Common_Item_System_Bag_032",
picture = "T_Common_Item_System_Bag_032",
getWay = "奖杯征程;活动",
bagId = 1,
qualifyingCardConf = {
priority = 2,
teamMemberRequire = {
2,
3,
4
},
qualifyingCardInfo = {
{
matchType = 0,
rangeLeft = 0,
rangeRight = 2520
}
}
}
},
[200022] = {
id = 200022,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "印章加成卡",
desc = "使用后在一局游戏结算时额外获得50%的星宝印章。",
icon = "T_Common_Item_System_Bag_034",
picture = "T_Common_Item_System_Bag_034",
getWay = v8,
bagId = 1,
useParam = {
50
}
},
[200023] = {
id = 200023,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "经验加成卡",
desc = "使用后在一局游戏结算时额外获得30%的经验值。",
icon = "T_Common_Item_System_Bag_024",
picture = "T_Common_Item_System_Bag_024",
getWay = v8,
bagId = 1,
useParam = {
30
}
},
[200024] = {
id = 200024,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 5,
name = "【废弃】烟花道具",
desc = "在烟花秀进行期间，可使用烟花道具发射烟花。",
icon = "T_Common_Icon_Coin_25",
getWay = "商城",
jumpId = {
71
},
bagId = 1
},
[200025] = {
id = 200025,
effect = true,
type = "ItemType_QQCash",
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "1Q币",
icon = "T_RedBag_Icon_QB",
getWay = v8
},
[200026] = {
id = 200026,
effect = true,
type = "ItemType_WXCash",
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "现金1元",
icon = "T_RedBag_Icon_Coin",
getWay = v8
},
[200027] = {
id = 200027,
effect = true,
type = "ItemType_QQCash",
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "6Q币",
icon = "T_RedBag_Icon_QB",
getWay = v8
},
[200028] = {
id = 200028,
effect = true,
type = "ItemType_WXCash",
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "现金6元",
icon = "T_RedBag_Icon_Coin",
getWay = v8
},
[200030] = {
id = 200030,
effect = true,
type = "ItemType_GongyiXHH",
stackedNum = 9999,
maxNum = 99999,
quality = 2,
name = "小红花",
desc = "腾讯公益小红花，可前往腾讯公益查看",
icon = "T_PublicWelfare_Img_Flower",
getWay = v8
},
[200031] = {
id = 200031,
effect = true,
type = "ItemType_GongyiMedal",
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "航天助力官勋章",
desc = "腾讯公益“元梦航天助力官”勋章，可前往腾讯公益查看",
icon = "T_PublicWelfare_Icon_03",
getWay = v8
},
[200032] = {
id = 200032,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "星运红包",
desc = "元梦拜年礼，新年星好运！可用于参与亿起领红包新年活动，188元现金红包等你来拿！",
icon = "T_Common_Item_System_Bag_024",
picture = "T_Common_Item_System_Bag_024",
getWay = v8,
bagId = 1,
bHideInBag = true
},
[200033] = {
id = 200033,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 1,
name = "现金红包",
desc = "188元现金红包",
icon = "T_Common_Icon_Coin_65",
picture = "T_Common_Icon_Coin_65",
getWay = v8,
bHideInBag = true
},
[200034] = {
id = 200034,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "腾讯视频",
desc = "腾讯视频会员VIP双周卡",
icon = "CDN:T_Common_Item_System_Bag_063",
picture = "CDN:T_Common_Item_System_Bag_063",
getWay = v8,
bHideInBag = true
},
[200035] = {
id = 200035,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "QQ音乐",
desc = "QQ音乐豪华绿钻双周卡",
icon = "CDN:T_Common_Item_System_Bag_061",
picture = "CDN:T_Common_Item_System_Bag_061",
getWay = v8,
bHideInBag = true
},
[200036] = {
id = 200036,
effect = true,
stackedNum = 999999,
maxNum = 999999,
name = "海底捞",
desc = "海底捞菜品券",
icon = "CDN:T_Common_Item_System_Bag_056",
picture = "CDN:T_Common_Item_System_Bag_056",
getWay = v8,
bHideInBag = true
},
[200037] = {
id = 200037,
effect = true,
stackedNum = 999999,
maxNum = 999999,
name = "绝味",
desc = "绝味鸭脖50元礼包",
icon = "CDN:T_Common_Item_System_Bag_057",
picture = "CDN:T_Common_Item_System_Bag_057",
getWay = v8,
bHideInBag = true
},
[200038] = {
id = 200038,
effect = true,
stackedNum = 999999,
maxNum = 999999,
name = "瑞幸",
desc = "瑞幸15元新春券",
icon = "CDN:T_Common_Item_System_Bag_062",
picture = "CDN:T_Common_Item_System_Bag_062",
getWay = v8,
bHideInBag = true
},
[200039] = {
id = 200039,
effect = true,
stackedNum = 999999,
maxNum = 999999,
name = "来伊份",
desc = "来伊份10元无门槛券",
icon = "CDN:T_Common_Item_System_Bag_058",
picture = "CDN:T_Common_Item_System_Bag_058",
getWay = v8,
bHideInBag = true
},
[200040] = {
id = 200040,
effect = true,
type = v0,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "腾讯视频",
desc = "腾讯视频会员VIP月卡",
icon = "CDN:T_Common_Item_System_Bag_066",
picture = "CDN:T_Common_Item_System_Bag_066",
getWay = v8,
useType = "IUTO_AMS",
useParam = {
1
},
bHideInBag = true
},
[200041] = {
id = 200041,
effect = true,
type = v0,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v9,
quality = 2,
name = "一元幸启",
desc = "一元幸启",
icon = "T_Common_Item_System_Bag_014",
picture = "T_Common_Item_System_BagBig_014",
bagId = 1
},
[200069] = {
id = 200069,
effect = true,
stackedNum = 999999,
maxNum = 999999,
name = "巅峰盛典纪念票",
desc = "点滴热爱，成就巅峰！",
icon = "T_Common_Icon_Coin_88",
picture = "T_Common_Icon_Coin_88",
getWay = v8
},
[200070] = {
id = 200070,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "时装借用卡",
desc = "在周六和周日期间，可以通过组队向亲密关系达到4级以上的好友借用时装(有效期1天)",
icon = "T_Common_Icon_Coin_91",
picture = "T_Common_Icon_Coin_91",
getWay = "默契商店兑换"
},
[200071] = {
id = 200071,
effect = true,
type = v0,
stackedNum = 99999999,
maxNum = 99999999,
quality = 1,
name = "奖杯",
desc = "获得后，可累计至【奖杯征程】活动的总进度值中，达成不同的进度阶段即可领取相应的奖励。（每周获得奖杯的累计进度值有上限）",
icon = "T_Common_Icon_Coin_101",
picture = "T_Common_Icon_Coin_101",
getWay = "奖杯征程任务;参与部分奖杯玩法等",
jumpId = {
348,
235
},
bagId = 1,
useType = "IUTO_Cups",
bHideInBag = true,
noShowTipsNum = true
},
[200072] = {
id = 200072,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(1)",
desc = "获得后，可以永久增加第五赛季时尚分1分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
5,
1
},
bHideInBag = true
},
[200073] = {
id = 200073,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(10)",
desc = "获得后，可以永久增加第五赛季时尚分10分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
5,
10
},
bHideInBag = true
},
[200074] = {
id = 200074,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(100)",
desc = "获得后，可以永久增加第五赛季时尚分100分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
5,
100
},
bHideInBag = true
},
[200075] = {
id = 200075,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(1)",
desc = "获得后，可以永久增加第六赛季时尚分1分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
6,
1
},
bHideInBag = true
},
[200076] = {
id = 200076,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(10)",
desc = "获得后，可以永久增加第六赛季时尚分10分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
6,
10
},
bHideInBag = true
},
[200077] = {
id = 200077,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(100)",
desc = "获得后，可以永久增加第六赛季时尚分100分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
6,
100
},
bHideInBag = true
},
[200078] = {
id = 200078,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(1)",
desc = "获得后，可以永久增加第七赛季时尚分1分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
7,
1
},
bHideInBag = true
},
[200079] = {
id = 200079,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(10)",
desc = "获得后，可以永久增加第七赛季时尚分10分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
7,
10
},
bHideInBag = true
},
[200080] = {
id = 200080,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(100)",
desc = "获得后，可以永久增加第七赛季时尚分100分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
7,
100
},
bHideInBag = true
},
[200081] = {
id = 200081,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(1)",
desc = "获得后，可以永久增加第四赛季时尚分1分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
4,
1
},
bHideInBag = true
},
[200082] = {
id = 200082,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(10)",
desc = "获得后，可以永久增加第四赛季时尚分10分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
4,
10
},
bHideInBag = true
},
[200083] = {
id = 200083,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
exceedReplaceItem = v9,
name = "时尚分(100)",
desc = "获得后，可以永久增加第四赛季时尚分100分",
icon = "CDN:T_Common_Item_System_Bag_064",
picture = "CDN:T_Common_Item_System_Bag_064",
bagId = 1,
useType = "IUTO_FashionValue",
useParam = {
4,
100
},
bHideInBag = true
},
[200101] = {
id = 200101,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "阵营卡",
desc = "在【谁是狼人】中消耗阵营卡，可自选阵营进入游戏。",
icon = "T_E3_Prepare_Icon_FactionCard_001",
picture = "T_E3_Prepare_Icon_FactionCard_001",
getWay = "兑换商店;狼人通行证;购买",
jumpId = {
313,
331,
10500
}
},
[200102] = {
id = 200102,
effect = true,
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "身份卡",
desc = "在【谁是狼人】中消耗身份卡，可自选身份进入游戏。",
icon = "T_E3_Prepare_Icon_IdentityCard_001",
picture = "T_E3_Prepare_Icon_IdentityCard_001",
getWay = "兑换商店;狼人通行证;购买",
jumpId = {
313,
331,
10501
}
},
[200103] = {
id = 200103,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "星宝农场月卡",
desc = "使用后，星宝农场月卡有效时间增加30天。注意：需要星宝农场等级达到3级后才会生效哦~",
icon = "T_Farmyard_Icon_Coin_Card",
picture = "T_Farmyard_Icon_Coin_Card",
getWay = "商城",
useType = "IUTO_FarmMonthlyPass",
useParam = {
30
}
},
[200104] = {
id = 200104,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "星宝农场月卡1日体验",
desc = "使用后，星宝农场月卡有效时间增加1天。注意：需要星宝农场等级达到3级后才会生效哦~",
icon = "T_Farmyard_Icon_Coin_Card01",
picture = "T_Farmyard_Icon_Coin_Card01",
getWay = v8,
useType = "IUTO_FarmMonthlyPass",
useParam = {
1
}
},
[200105] = {
id = 200105,
quality = 4,
name = "酷洛米馈赠宝箱",
desc = "在“寻梦之旅”活动中，打开后可能收获时装手稿或嘉年华寄语。",
icon = "T_FlyingChess_Icon_TreasureChest",
getWay = v8,
bHideInBag = true
},
[200106] = {
id = 200106,
effect = true,
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "现金1元",
icon = "T_RedBag_Icon_Coin",
picture = "T_RedBag_Icon_Coin",
getWay = v8
},
[200107] = {
id = 200107,
effect = true,
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "现金2元",
icon = "T_RedBag_Icon_Coin",
picture = "T_RedBag_Icon_Coin",
getWay = v8
},
[200108] = {
id = 200108,
effect = true,
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "红包",
desc = "现金5元",
icon = "T_RedBag_Icon_Coin",
picture = "T_RedBag_Icon_Coin",
getWay = v8
},
[200109] = {
id = 200109,
effect = true,
stackedNum = 9999,
maxNum = 99999,
quality = 1,
name = "星运红包",
desc = "随机获得以下奖励之一：现金5元（限量）、现金2元（限量）、现金1元（限量）、限时星愿币*2、限时星愿币*6",
icon = "T_Common_Icon_Coin_53",
picture = "T_Common_Icon_Coin_53",
getWay = v8
},
[200110] = {
id = 200110,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
1
},
bHideInBag = true
},
[200111] = {
id = 200111,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
2
},
bHideInBag = true
},
[200112] = {
id = 200112,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
3
},
bHideInBag = true
},
[200113] = {
id = 200113,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
4
},
bHideInBag = true
},
[200114] = {
id = 200114,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
5
},
bHideInBag = true
},
[200115] = {
id = 200115,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
0
},
bHideInBag = true
},
[200116] = {
id = 200116,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
0
},
bHideInBag = true
},
[200117] = {
id = 200117,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
0
},
bHideInBag = true
},
[200118] = {
id = 200118,
effect = true,
type = v0,
stackedNum = 9999999,
maxNum = 9999999,
name = "灵感花笺",
desc = "获得后随机获得一个灵感花笺",
picture = v6,
useType = "IUTO_PrayerCard",
useParam = {
0
},
bHideInBag = true
},
[200120] = {
id = 200120,
effect = true,
type = "ItemType_FarmChest",
maxNum = 999999999,
quality = 2,
name = "农场宝箱",
desc = "打开农场宝箱，可获得根据农场小屋等级对应数量的农场币",
icon = "CDN:T_Common_Item_Farm_Coin",
picture = "CDN:T_Common_Item_System_FarmChest_01",
getWay = "每日奖杯挑战",
useType = "IUTO_FarmChest",
useParam = {
218000,
500
},
bHideInBag = true
},
[200121] = {
id = 200121,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "新春贺岁",
desc = "春回大地，万物新生",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200122] = {
id = 200122,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "花好月圆",
desc = "桂花飘香，寄情千里，共赏明月",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200123] = {
id = 200123,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "五月端阳",
desc = "糯糯清香香四溢，脉脉温情情更浓 ",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200124] = {
id = 200124,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "七夕弄巧",
desc = "一年一度的相逢，一生一世的眷恋",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200125] = {
id = 200125,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "元宵佳节",
desc = "灯花如昼映长街，笑语盈盈满人间",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200126] = {
id = 200126,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "水墨江山",
desc = "挥毫山水讲求豪迈写意，贵在似与不似之间",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200127] = {
id = 200127,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "花鸟写意",
desc = "以墨为骨，以色为体，尽显是天地之道",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200128] = {
id = 200128,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "汉唐风华",
desc = "中国有礼仪之大，故称夏；有服章之美，谓之华",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200129] = {
id = 200129,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "生旦净丑",
desc = "人生如戏，戏如人生",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200130] = {
id = 200130,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "星汉灿烂",
desc = "寂静深空，星月交辉，银河谱写万古长卷",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200131] = {
id = 200131,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "瑞彩祥云",
desc = "只要你愿意抬头看，天空处处都是祥云",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200132] = {
id = 200132,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "飞龙在天",
desc = "永恒不朽的图腾，生生不息的灵魂",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200133] = {
id = 200133,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "如虎添翼",
desc = "静则不露锋芒，动则气吞万里",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200134] = {
id = 200134,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "百鸟朝凤",
desc = "只有经历烈火的痛苦煎熬，才能涅槃重生",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200135] = {
id = 200135,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "桃源追忆",
desc = "不知来路，忘了归途，水漫桃花，已在桃源中",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200136] = {
id = 200136,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "千岁鹤归",
desc = "山中的流云，便是仙鹤的家乡",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200137] = {
id = 200137,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "荷花亭亭",
desc = "花团锦簇固然华美，含苞待放也别有风味",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200138] = {
id = 200138,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "华夏衣冠",
desc = "一褶一皱间，饱藏千年风华",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200139] = {
id = 200139,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "古蜀文明",
desc = "当古蜀先民抬头仰望时，他们在仰望什么？",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200140] = {
id = 200140,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "青铜国器",
desc = "古之大国重器，历经千年不朽",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200141] = {
id = 200141,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "金彰华彩",
desc = "古今同赏金器之美，岁月映照千年风华",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
},
[200142] = {
id = 200142,
effect = true,
stackedNum = 9999,
maxNum = 99999,
name = "石之美者",
desc = "只有经过百般雕琢，才称得上是真正的美玉",
picture = v6,
getWay = v8,
bHideInBag = true,
enableDecompose = true,
decomposeItem = v10,
decomposeRetainNum = 1
}
}

local mt = {
effect = false,
type = "ItemType_Common",
quality = 3,
icon = v6,
bHideInBag = false,
isUsePurchaseDyeing = false,
noShowTipsNum = false,
enableDecompose = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data