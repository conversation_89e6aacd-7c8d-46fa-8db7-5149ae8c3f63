--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-头饰

local v0 = 6

local v1 = 200008

local v2 = 10000

local v3 = 5

local v4 = 10

local v5 = {
seconds = 1676390400
}

local v6 = {
seconds = 1706198400
}

local v7 = {
seconds = 4074854400
}

local v8 = {
2,
12
}

local v9 = 1

local v10 = "赛季祈愿"

local v11 = "1.2.67.1"

local v12 = "1.2.100.1"

local v13 = "1.3.6.1"

local v14 = 80

local data = {
[60001] = {
commodityId = 60001,
commodityName = "小熊爪",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
shopSort = 1,
itemIds = {
630001
},
suitId = 70001
},
[60002] = {
commodityId = 60002,
commodityName = "四叶草",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630002
},
suitId = 70002
},
[60003] = {
commodityId = 60003,
commodityName = "灵感",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630003
},
suitId = 70003
},
[60004] = {
commodityId = 60004,
commodityName = "哄不好头饰",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v8,
shopSort = 2,
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
630004
},
bOpenSuit = true,
suitId = 70004
},
[60005] = {
commodityId = 60005,
commodityName = "疑惑头饰",
beginTime = v5,
shopTag = v8,
shopSort = 3,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
630005
},
bOpenSuit = true,
suitId = 70005
},
[60006] = {
commodityId = 60006,
commodityName = "星梦之冠",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v8,
shopSort = 2,
jumpId = 22,
jumpText = "云端星梦",
itemIds = {
630006
},
bOpenSuit = true,
suitId = 70006
},
[60007] = {
commodityId = 60007,
commodityName = "星梦之冠",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
630007
}
},
[60008] = {
commodityId = 60008,
commodityName = "星梦之冠",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
630008
}
},
[60009] = {
commodityId = 60009,
commodityName = "仙桃浮梦冠",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v8,
shopSort = 1,
jumpId = 11,
jumpText = v10,
itemIds = {
630009
},
bOpenSuit = true,
suitId = 70007
},
[60010] = {
commodityId = 60010,
commodityName = "仙桃浮梦冠",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630010
}
},
[60011] = {
commodityId = 60011,
commodityName = "仙桃浮梦冠",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630011
}
},
[60012] = {
commodityId = 60012,
commodityName = "炫彩竹蜻蜓",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v8,
shopSort = 1,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
630012
},
bOpenSuit = true,
suitId = 70008
},
[60013] = {
commodityId = 60013,
commodityName = "炫彩竹蜻蜓",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704024000
},
itemIds = {
630013
}
},
[60014] = {
commodityId = 60014,
commodityName = "炫彩竹蜻蜓",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704024000
},
itemIds = {
630014
}
},
[60015] = {
commodityId = 60015,
commodityName = "星梦光环",
beginTime = v5,
shopTag = v8,
jumpId = 34,
jumpText = "星宝会员卡",
itemIds = {
630015
},
bOpenSuit = true,
suitId = 70009
},
[60016] = {
commodityId = 60016,
commodityName = "灵魂出窍",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v8,
shopSort = 4,
jumpId = 39,
jumpText = "冲段挑战",
itemIds = {
630016
},
bOpenSuit = true,
suitId = 70010
},
[60017] = {
commodityId = 60017,
commodityName = "灵魂出窍",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630017
}
},
[60018] = {
commodityId = 60018,
commodityName = "灵魂出窍",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630018
}
},
[60019] = {
commodityId = 60019,
commodityName = "桃子头饰",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v8,
shopSort = 3,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
630019
},
bOpenSuit = true,
suitId = 70011
},
[60020] = {
commodityId = 60020,
commodityName = "桃子头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630020
}
},
[60021] = {
commodityId = 60021,
commodityName = "桃子头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
630021
}
},
[60022] = {
commodityId = 60022,
commodityName = "迪斯科头饰",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630022
},
suitId = 70012
},
[60023] = {
commodityId = 60023,
commodityName = "迪斯科头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630023
}
},
[60024] = {
commodityId = 60024,
commodityName = "迪斯科头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630024
}
},
[60025] = {
commodityId = 60025,
commodityName = "橄榄头饰",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v8,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
630025
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 70013
},
[60026] = {
commodityId = 60026,
commodityName = "橄榄头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
630026
}
},
[60027] = {
commodityId = 60027,
commodityName = "橄榄头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
630027
}
},
[60028] = {
commodityId = 60028,
commodityName = "蝴蝶结头饰",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630028
},
suitId = 70014
},
[60029] = {
commodityId = 60029,
commodityName = "蝴蝶结头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630029
}
},
[60030] = {
commodityId = 60030,
commodityName = "蝴蝶结头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630030
}
},
[60031] = {
commodityId = 60031,
commodityName = "大帅哥",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630031
},
suitId = 70015
},
[60032] = {
commodityId = 60032,
commodityName = "小公举",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
shopSort = 4,
itemIds = {
630032
},
suitId = 70016
},
[60033] = {
commodityId = 60033,
commodityName = "来贴贴",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
shopSort = 5,
itemIds = {
630033
},
suitId = 70017
},
[60034] = {
commodityId = 60034,
commodityName = "水逆退散",
beginTime = v5,
shopTag = v8,
shopSort = 6,
jumpId = 123,
jumpText = "扫码一起玩",
itemIds = {
630034
},
bOpenSuit = true,
suitId = 70018
},
[60035] = {
commodityId = 60035,
commodityName = "小狼头饰",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v8,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
630035
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 70019
},
[60036] = {
commodityId = 60036,
commodityName = "小狼头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630036
}
},
[60037] = {
commodityId = 60037,
commodityName = "小狼头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630037
}
},
[60038] = {
commodityId = 60038,
commodityName = "莲花头饰",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630038
},
suitId = 70020
},
[60039] = {
commodityId = 60039,
commodityName = "莲花头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630039
}
},
[60040] = {
commodityId = 60040,
commodityName = "莲花头饰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630040
}
},
[60041] = {
commodityId = 60041,
commodityName = "小红花",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630041
},
suitId = 70021
},
[60042] = {
commodityId = 60042,
commodityName = "塑封的心",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630042
},
suitId = 70022
},
[60043] = {
commodityId = 60043,
commodityName = "荷包蛋",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630043
},
suitId = 70023
},
[60044] = {
commodityId = 60044,
commodityName = "悬浮星球",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v8,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
630044
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 70024
},
[60045] = {
commodityId = 60045,
commodityName = "悬浮星球",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
630045
}
},
[60046] = {
commodityId = 60046,
commodityName = "悬浮星球",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
630046
}
},
[60047] = {
commodityId = 60047,
commodityName = "星点子",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630047
},
suitId = 70025
},
[60048] = {
commodityId = 60048,
commodityName = "造梦飞星",
beginTime = v5,
shopTag = v8,
jumpId = 74,
jumpText = "造梦之旅",
itemIds = {
630048
},
bOpenSuit = true,
suitId = 70026
},
[60049] = {
commodityId = 60049,
commodityName = "牛气冲天",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630049
},
suitId = 70027
},
[60050] = {
commodityId = 60050,
commodityName = "元梦星冠",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630050
},
suitId = 70028
},
[60051] = {
commodityId = 60051,
commodityName = "荣耀之冠",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630051
},
suitId = 70029
},
[60052] = {
commodityId = 60052,
commodityName = "璀璨银冠",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630052
},
suitId = 70030
},
[60053] = {
commodityId = 60053,
commodityName = "可爱贴贴",
coinType = 6,
price = 10000,
beginTime = v5,
endTime = {
seconds = 1676476800
},
shopTag = v8,
itemIds = {
630053
},
suitId = 70031
},
[60054] = {
commodityId = 60054,
commodityName = "TNT头饰",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706112000
},
shopTag = v8,
jumpId = 47,
jumpText = "初星代言人 ",
itemIds = {
630054
},
bOpenSuit = true,
suitId = 70032
},
[61000] = {
commodityId = 61000,
commodityName = "元宵宝宝头饰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630055
},
suitId = 70033
},
[61001] = {
commodityId = 61001,
commodityName = "睡觉中头饰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630056
},
suitId = 70034
},
[61002] = {
commodityId = 61002,
commodityName = "粉色回忆",
beginTime = {
seconds = 1750521600
},
endTime = {
seconds = 1752940799
},
shopTag = v8,
jumpId = 1099,
jumpText = "轻羽仙子",
minVersion = "1.3.88.155",
itemIds = {
630057
},
bOpenSuit = true,
suitId = 70035
},
[61003] = {
commodityId = 61003,
commodityName = "元宝",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630058
},
suitId = 70036
},
[61004] = {
commodityId = 61004,
commodityName = "迪迦签名",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630059
},
suitId = 70037
},
[61005] = {
commodityId = 61005,
commodityName = "赛罗签名",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630060
},
suitId = 70038
},
[61006] = {
commodityId = 61006,
commodityName = "泽塔签名",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630061
},
suitId = 70039
},
[61007] = {
commodityId = 61007,
commodityName = "下次一定灯牌",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630062
},
suitId = 70040
},
[61008] = {
commodityId = 61008,
commodityName = "青花茶盏头饰",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
shopTag = v8,
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
630063
},
bOpenSuit = true,
suitId = 70041
},
[61009] = {
commodityId = 61009,
commodityName = "青花茶盏头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
630064
}
},
[61010] = {
commodityId = 61010,
commodityName = "青花茶盏头饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
630065
}
},
[61011] = {
commodityId = 61011,
commodityName = "锦鲤附体",
beginTime = v6,
endTime = {
seconds = 1710431999
},
shopTag = v8,
shopSort = 1,
jumpId = 175,
jumpText = v10,
minVersion = v11,
itemIds = {
630066
},
bOpenSuit = true,
suitId = 70042
},
[61012] = {
commodityId = 61012,
commodityName = "锦鲤附体",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = v11,
itemIds = {
630067
}
},
[61013] = {
commodityId = 61013,
commodityName = "锦鲤附体",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = v11,
itemIds = {
630068
}
},
[61014] = {
commodityId = 61014,
commodityName = "雪人头饰",
beginTime = v6,
endTime = {
seconds = 1710431999
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "山海通行证",
minVersion = v11,
itemIds = {
630069
},
bOpenSuit = true,
suitId = 70043
},
[61015] = {
commodityId = 61015,
commodityName = "雪人头饰",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = v11,
itemIds = {
630070
}
},
[61016] = {
commodityId = 61016,
commodityName = "雪人头饰",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = v11,
itemIds = {
630071
}
},
[61017] = {
commodityId = 61017,
commodityName = "叹号头饰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630072
},
suitId = 70044
},
[61018] = {
commodityId = 61018,
commodityName = "海浪光环",
beginTime = v6,
endTime = {
seconds = 1710431999
},
shopTag = v8,
shopSort = 1,
jumpId = 175,
jumpText = v10,
minVersion = v11,
itemIds = {
630073
},
bOpenSuit = true,
suitId = 70045
},
[61019] = {
commodityId = 61019,
commodityName = "海浪光环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v11,
itemIds = {
630074
}
},
[61020] = {
commodityId = 61020,
commodityName = "海浪光环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v11,
itemIds = {
630075
}
},
[61021] = {
commodityId = 61021,
commodityName = "小红花头饰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630076
},
suitId = 70046
},
[61022] = {
commodityId = 61022,
commodityName = "龙角",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopTag = v8,
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
minVersion = v12,
itemIds = {
630077
},
bOpenSuit = true,
suitId = 70047
},
[61023] = {
commodityId = 61023,
commodityName = "龙角",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v11,
itemIds = {
630078
}
},
[61024] = {
commodityId = 61024,
commodityName = "龙角",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v11,
itemIds = {
630079
}
},
[61025] = {
commodityId = 61025,
commodityName = "金箍",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630080
},
suitId = 70048
},
[61026] = {
commodityId = 61026,
commodityName = "冰雪皇冠",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
shopTag = v8,
shopSort = 1,
jumpId = 183,
jumpText = "冰雪玫瑰",
minVersion = "1.2.80.1",
itemIds = {
630081
},
bOpenSuit = true,
suitId = 70049
},
[61027] = {
commodityId = 61027,
commodityName = "冰雪皇冠",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v11,
itemIds = {
630082
}
},
[61028] = {
commodityId = 61028,
commodityName = "冰雪皇冠",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v11,
itemIds = {
630083
}
},
[61029] = {
commodityId = 61029,
commodityName = "风间徽章",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v11,
itemIds = {
630084
},
suitId = 70050
},
[61030] = {
commodityId = 61030,
commodityName = "元气吐司",
beginTime = v5,
shopTag = v8,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v11,
itemIds = {
630086
},
bOpenSuit = true,
suitId = 70052
},
[61031] = {
commodityId = 61031,
commodityName = "热情吐司",
beginTime = v5,
shopTag = v8,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v11,
itemIds = {
630087
},
bOpenSuit = true,
suitId = 70053
},
[61032] = {
commodityId = 61032,
commodityName = "幸福饺饺",
beginTime = {
seconds = 1707235200
},
endTime = {
seconds = 1707926399
},
shopTag = v8,
jumpId = 20,
jumpText = "春节集福",
minVersion = v11,
itemIds = {
630088
},
bOpenSuit = true,
suitId = 70054
},
[61101] = {
commodityId = 61101,
commodityName = "使魔",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v8,
shopSort = 1,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
630092
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 70058
},
[61102] = {
commodityId = 61102,
commodityName = "使魔",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630093
}
},
[61103] = {
commodityId = 61103,
commodityName = "使魔",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630094
}
},
[61104] = {
commodityId = 61104,
commodityName = "时空道具",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v8,
shopSort = 1,
jumpId = 189,
jumpText = v10,
itemIds = {
630095
},
bOpenSuit = true,
suitId = 70059
},
[61105] = {
commodityId = 61105,
commodityName = "时空道具",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630096
}
},
[61106] = {
commodityId = 61106,
commodityName = "时空道具",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630097
}
},
[61107] = {
commodityId = 61107,
commodityName = "萌宝出笼",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v8,
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
630098
},
bOpenSuit = true,
suitId = 70060
},
[61108] = {
commodityId = 61108,
commodityName = "相柳 毛球",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v8,
jumpId = 1054,
jumpText = "长相思",
minVersion = "1.3.12.47",
itemIds = {
630099
},
bOpenSuit = true,
suitId = 70061
},
[61109] = {
commodityId = 61109,
commodityName = "招牌菜狗",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
shopTag = v8,
jumpId = 175,
jumpText = "蔬菜精灵祈愿",
itemIds = {
630100
},
bOpenSuit = true,
suitId = 70062
},
[61110] = {
commodityId = 61110,
commodityName = "元气开关",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630101
},
suitId = 70063
},
[61111] = {
commodityId = 61111,
commodityName = "提醒铃铛",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
jumpId = 1082,
jumpText = "遗落珍宝",
itemIds = {
630102
},
suitId = 70064
},
[61112] = {
commodityId = 61112,
commodityName = "海王星",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630103
},
suitId = 70065
},
[61113] = {
commodityId = 61113,
commodityName = "星星交流器",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630104
},
suitId = 70066
},
[61114] = {
commodityId = 61114,
commodityName = "星星交流器",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630105
}
},
[61115] = {
commodityId = 61115,
commodityName = "星星交流器",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630106
}
},
[61116] = {
commodityId = 61116,
commodityName = "星河探测仪",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v8,
shopSort = 1,
jumpId = 189,
jumpText = v10,
itemIds = {
630107
},
bOpenSuit = true,
suitId = 70067
},
[61117] = {
commodityId = 61117,
commodityName = "星河探测仪",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630108
}
},
[61118] = {
commodityId = 61118,
commodityName = "星河探测仪",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630109
}
},
[61119] = {
commodityId = 61119,
commodityName = "小太阳",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
630110
},
bOpenSuit = true,
suitId = 70068
},
[61120] = {
commodityId = 61120,
commodityName = "小太阳",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630111
}
},
[61121] = {
commodityId = 61121,
commodityName = "小太阳",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630112
}
},
[61122] = {
commodityId = 61122,
commodityName = "欢乐马戏",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630113
},
suitId = 70069
},
[61123] = {
commodityId = 61123,
commodityName = "我家营业中",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630114
},
suitId = 70070
},
[61124] = {
commodityId = 61124,
commodityName = "搞怪天才",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630115
},
suitId = 70071
},
[61125] = {
commodityId = 61125,
commodityName = "气鼓鼓河豚",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630116
},
suitId = 70072
},
[61126] = {
commodityId = 61126,
commodityName = "气鼓鼓河豚",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630117
}
},
[61127] = {
commodityId = 61127,
commodityName = "气鼓鼓河豚",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630118
}
},
[61128] = {
commodityId = 61128,
commodityName = "橘宝宝",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630119
},
suitId = 70073
},
[61129] = {
commodityId = 61129,
commodityName = "神速之杯",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1713455999
},
shopTag = v8,
shopSort = 1,
jumpId = 158,
jumpText = "星愿之旅",
itemIds = {
630125
},
bOpenSuit = true,
suitId = 70077
},
[61130] = {
commodityId = 61130,
commodityName = "藕趴趴",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
shopTag = v8,
jumpId = 176,
jumpText = "蔬菜精灵祈愿",
itemIds = {
630121
},
bOpenSuit = true,
suitId = 70075
},
[61131] = {
commodityId = 61131,
commodityName = "星际朋友",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v8,
shopSort = 1,
jumpId = 615,
jumpText = "向日葵小班",
itemIds = {
630122
},
bOpenSuit = true,
suitId = 70076
},
[61132] = {
commodityId = 61132,
commodityName = "星际朋友",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630123
}
},
[61133] = {
commodityId = 61133,
commodityName = "星际朋友",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630124
}
},
[61134] = {
commodityId = 61134,
commodityName = "蛋糕蜜语",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630126
},
suitId = 70078
},
[61135] = {
commodityId = 61135,
commodityName = "草莓塔塔",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v8,
shopSort = 1,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
630127
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 70079
},
[61136] = {
commodityId = 61136,
commodityName = "草莓塔塔",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630128
}
},
[61137] = {
commodityId = 61137,
commodityName = "草莓塔塔",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630129
}
},
[61138] = {
commodityId = 61138,
commodityName = "阿呆的金鱼",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v8,
shopSort = 1,
jumpId = 617,
jumpText = "向日葵小班",
itemIds = {
630130
},
bOpenSuit = true,
suitId = 70080
},
[61139] = {
commodityId = 61139,
commodityName = "满格电量",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630131
},
suitId = 70081
},
[61140] = {
commodityId = 61140,
commodityName = "小粽子",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630132
},
suitId = 70082
},
[61141] = {
commodityId = 61141,
commodityName = "炸弹",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630133
},
suitId = 70083
},
[61142] = {
commodityId = 61142,
commodityName = "彩色音符",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v12,
itemIds = {
630134
},
bOpenSuit = true,
suitId = 70084
},
[61143] = {
commodityId = 61143,
commodityName = "彩色音符",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630135
}
},
[61144] = {
commodityId = 61144,
commodityName = "彩色音符",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630136
}
},
[61145] = {
commodityId = 61145,
commodityName = "头戴雨伞",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630137
},
suitId = 70085
},
[61146] = {
commodityId = 61146,
commodityName = "海洋系列（海豚）",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630138
},
suitId = 70086
},
[61147] = {
commodityId = 61147,
commodityName = "海洋系列（海豚）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630139
}
},
[61148] = {
commodityId = 61148,
commodityName = "海洋系列（海豚）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630140
}
},
[61149] = {
commodityId = 61149,
commodityName = "海洋系列（章鱼）",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630141
},
suitId = 70087
},
[61150] = {
commodityId = 61150,
commodityName = "海洋系列（章鱼）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630142
}
},
[61151] = {
commodityId = 61151,
commodityName = "海洋系列（章鱼）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630143
}
},
[61152] = {
commodityId = 61152,
commodityName = "海洋系列（鲸鱼）",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630144
},
suitId = 70088
},
[61153] = {
commodityId = 61153,
commodityName = "海洋系列（鲸鱼）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630145
}
},
[61154] = {
commodityId = 61154,
commodityName = "海洋系列（鲸鱼）",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630146
}
},
[61155] = {
commodityId = 61155,
commodityName = "萌动音符",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v8,
shopSort = 1,
jumpId = 180,
jumpText = v10,
minVersion = v12,
itemIds = {
630147
},
bOpenSuit = true,
suitId = 70089
},
[61156] = {
commodityId = 61156,
commodityName = "萌动音符",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v12,
itemIds = {
630148
}
},
[61157] = {
commodityId = 61157,
commodityName = "萌动音符",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v12,
itemIds = {
630149
}
},
[61158] = {
commodityId = 61158,
commodityName = "完美连击",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v8,
shopSort = 1,
jumpId = 180,
jumpText = v10,
minVersion = v12,
itemIds = {
630150
},
bOpenSuit = true,
suitId = 70090
},
[61159] = {
commodityId = 61159,
commodityName = "完美连击",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630151
}
},
[61160] = {
commodityId = 61160,
commodityName = "完美连击",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630152
}
},
[61161] = {
commodityId = 61161,
commodityName = "找搭子",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630153
},
suitId = 70091
},
[61162] = {
commodityId = 61162,
commodityName = "为你点赞",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630154
},
suitId = 70092
},
[61163] = {
commodityId = 61163,
commodityName = "星夜太妃糖",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630155
},
suitId = 70093
},
[61164] = {
commodityId = 61164,
commodityName = "波纹甜甜心",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630156
},
suitId = 70094
},
[61165] = {
commodityId = 61165,
commodityName = "超核晶冠",
coinType = 3,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v12,
itemIds = {
630157
},
suitId = 70095
},
[61166] = {
commodityId = 61166,
commodityName = "超核晶冠",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v12,
itemIds = {
630158
}
},
[61167] = {
commodityId = 61167,
commodityName = "超核晶冠",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v12,
itemIds = {
630159
}
},
[61168] = {
commodityId = 61168,
commodityName = "小丸子樱桃",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v8,
shopSort = 1,
jumpId = 187,
jumpText = "小丸子便当屋",
minVersion = v12,
itemIds = {
630160
},
bOpenSuit = true,
suitId = 70096
},
[61169] = {
commodityId = 61169,
commodityName = "鲷鱼烧",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v8,
shopSort = 1,
jumpId = 187,
jumpText = "小丸子便当屋",
minVersion = v12,
itemIds = {
630161
},
bOpenSuit = true,
suitId = 70097
},
[61170] = {
commodityId = 61170,
commodityName = "鲷鱼烧",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630162
}
},
[61171] = {
commodityId = 61171,
commodityName = "鲷鱼烧",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v12,
itemIds = {
630163
}
},
[61172] = {
commodityId = 61172,
commodityName = "（无语子）",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.68.52",
itemIds = {
630164
},
bOpenSuit = true,
suitId = 70098
},
[61173] = {
commodityId = 61173,
commodityName = "智械触角",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630165
},
suitId = 70099
},
[61174] = {
commodityId = 61174,
commodityName = "梦幻铃铛",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v8,
shopSort = 1,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
630166
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 70100
},
[61175] = {
commodityId = 61175,
commodityName = "梦幻铃铛",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630167
}
},
[61176] = {
commodityId = 61176,
commodityName = "梦幻铃铛",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630168
}
},
[61177] = {
commodityId = 61177,
commodityName = "誓约之冠",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v8,
shopSort = 1,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
630169
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 70101
},
[61178] = {
commodityId = 61178,
commodityName = "誓约之冠",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630170
}
},
[61179] = {
commodityId = 61179,
commodityName = "誓约之冠",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630171
}
},
[61180] = {
commodityId = 61180,
commodityName = "大柠檬",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630172
},
suitId = 70102
},
[61181] = {
commodityId = 61181,
commodityName = "破壳鸡",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.68.52",
itemIds = {
630173
},
bOpenSuit = true,
suitId = 70103
},
[61182] = {
commodityId = 61182,
commodityName = "星语晶恋",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v8,
shopSort = 1,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
630174
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 70104
},
[61183] = {
commodityId = 61183,
commodityName = "星语晶恋",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630175
}
},
[61184] = {
commodityId = 61184,
commodityName = "星语晶恋",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
630176
}
},
[61185] = {
commodityId = 61185,
commodityName = "白金飞鸟",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
shopTag = v8,
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
630177
},
bOpenSuit = true,
suitId = 70105
},
[61186] = {
commodityId = 61186,
commodityName = "白金飞鸟",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1716566400
},
itemIds = {
630178
}
},
[61187] = {
commodityId = 61187,
commodityName = "白金飞鸟",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1716566400
},
itemIds = {
630179
}
},
[61188] = {
commodityId = 61188,
commodityName = "天鹅之冠",
beginTime = {
seconds = 1718380800
},
endTime = {
seconds = 1720367999
},
shopTag = v8,
shopSort = 1,
jumpId = 603,
jumpText = "双生曼舞",
minVersion = "1.3.7.31",
itemIds = {
630180
},
bOpenSuit = true,
suitId = 70106
},
[61189] = {
commodityId = 61189,
commodityName = "天鹅之冠",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630181
}
},
[61190] = {
commodityId = 61190,
commodityName = "天鹅之冠",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630182
}
},
[61191] = {
commodityId = 61191,
commodityName = "蟹眯眯",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v13,
itemIds = {
630183
},
bOpenSuit = true,
suitId = 70107
},
[61192] = {
commodityId = 61192,
commodityName = "蟹眯眯",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630184
}
},
[61193] = {
commodityId = 61193,
commodityName = "蟹眯眯",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630185
}
},
[61194] = {
commodityId = 61194,
commodityName = "多肉盆栽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630186
},
suitId = 70108
},
[61195] = {
commodityId = 61195,
commodityName = "多肉盆栽",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630187
}
},
[61196] = {
commodityId = 61196,
commodityName = "多肉盆栽",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630188
}
},
[61197] = {
commodityId = 61197,
commodityName = "小瓢虫",
beginTime = {
seconds = 1728662400
},
endTime = {
seconds = 1730476799
},
shopTag = v8,
jumpId = 622,
jumpText = "晨曦玫瑰",
minVersion = "1.3.18.71",
itemIds = {
630189
},
bOpenSuit = true,
suitId = 70109
},
[61198] = {
commodityId = 61198,
commodityName = "雀翎羽",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
630190
},
bOpenSuit = true,
suitId = 70110
},
[61199] = {
commodityId = 61199,
commodityName = "雀翎羽",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630191
}
},
[61200] = {
commodityId = 61200,
commodityName = "雀翎羽",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630192
}
},
[61201] = {
commodityId = 61201,
commodityName = "头顶云朵",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630193
},
suitId = 70111
},
[61202] = {
commodityId = 61202,
commodityName = "小黄鸭",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630194
},
suitId = 70112
},
[61203] = {
commodityId = 61203,
commodityName = "水晶天鹅",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.7.31",
itemIds = {
630195
},
bOpenSuit = true,
suitId = 70113
},
[61204] = {
commodityId = 61204,
commodityName = "水晶天鹅",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v13,
itemIds = {
630196
}
},
[61205] = {
commodityId = 61205,
commodityName = "水晶天鹅",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v13,
itemIds = {
630197
}
},
[61206] = {
commodityId = 61206,
commodityName = "固态火苗",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
shopTag = v8,
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
630198
},
bOpenSuit = true,
suitId = 70114
},
[61207] = {
commodityId = 61207,
commodityName = "固态火苗",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1719504000
},
minVersion = "1.3.7.75",
itemIds = {
630199
}
},
[61208] = {
commodityId = 61208,
commodityName = "固态火苗",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1719504000
},
minVersion = "1.3.7.75",
itemIds = {
630200
}
},
[61209] = {
commodityId = 61209,
commodityName = "多肉盆栽1",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630201
},
suitId = 70115
},
[61210] = {
commodityId = 61210,
commodityName = "多肉盆栽1",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630202
}
},
[61211] = {
commodityId = 61211,
commodityName = "多肉盆栽1",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630203
}
},
[61212] = {
commodityId = 61212,
commodityName = "水晶之花",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v8,
shopSort = 1,
jumpId = 11,
jumpText = v10,
minVersion = v13,
itemIds = {
630204
},
bOpenSuit = true,
suitId = 70116
},
[61213] = {
commodityId = 61213,
commodityName = "水晶之花",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v13,
itemIds = {
630205
}
},
[61214] = {
commodityId = 61214,
commodityName = "水晶之花",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = v13,
itemIds = {
630206
}
},
[61215] = {
commodityId = 61215,
commodityName = "可爱寿司",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v8,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
minVersion = "1.3.26.93",
itemIds = {
630207
},
bOpenSuit = true,
suitId = 70117
},
[61216] = {
commodityId = 61216,
commodityName = "可爱寿司",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630208
}
},
[61217] = {
commodityId = 61217,
commodityName = "可爱寿司",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630209
}
},
[61218] = {
commodityId = 61218,
commodityName = "食人花",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v8,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
630210
},
bOpenSuit = true,
suitId = 70118
},
[61219] = {
commodityId = 61219,
commodityName = "食人花",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.7.94",
itemIds = {
630211
}
},
[61220] = {
commodityId = 61220,
commodityName = "食人花",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.7.94",
itemIds = {
630212
}
},
[61221] = {
commodityId = 61221,
commodityName = "珊瑚王冠",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v8,
shopSort = 1,
jumpId = 11,
jumpText = v10,
minVersion = v13,
itemIds = {
630213
},
bOpenSuit = true,
suitId = 70119
},
[61222] = {
commodityId = 61222,
commodityName = "珊瑚王冠",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630214
}
},
[61223] = {
commodityId = 61223,
commodityName = "珊瑚王冠",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630215
}
},
[61224] = {
commodityId = 61224,
commodityName = "萌萌脚印A",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630216
},
suitId = 70120
},
[61225] = {
commodityId = 61225,
commodityName = "萌萌脚印B",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630217
},
suitId = 70121
},
[61226] = {
commodityId = 61226,
commodityName = "萌萌脚印C",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630218
},
suitId = 70122
},
[61227] = {
commodityId = 61227,
commodityName = "星世界地图鉴赏家",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = v13,
itemIds = {
630219
},
suitId = 70123
},
[61228] = {
commodityId = 61228,
commodityName = "星世界地图鉴赏家",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630220
}
},
[61229] = {
commodityId = 61229,
commodityName = "星世界地图鉴赏家",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = v13,
itemIds = {
630221
}
},
[61230] = {
commodityId = 61230,
commodityName = "生命之环",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v8,
shopSort = 1,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = "1.3.12.118",
itemIds = {
630222
},
bOpenSuit = true,
suitId = 70124
},
[61231] = {
commodityId = 61231,
commodityName = "生命之环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.118",
itemIds = {
630223
}
},
[61232] = {
commodityId = 61232,
commodityName = "生命之环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.118",
itemIds = {
630224
}
},
[61233] = {
commodityId = 61233,
commodityName = "枫糖松饼",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630225
},
suitId = 70125
},
[61234] = {
commodityId = 61234,
commodityName = "枫糖松饼",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630226
}
},
[61235] = {
commodityId = 61235,
commodityName = "枫糖松饼",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630227
}
},
[61236] = {
commodityId = 61236,
commodityName = "寻梦火箭",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630228
},
suitId = 70126
},
[61237] = {
commodityId = 61237,
commodityName = "lulu猪 趴趴灯牌",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v8,
jumpId = 1094,
jumpText = "夏日花园",
minVersion = "1.3.88.53",
itemIds = {
630229
},
bOpenSuit = true,
suitId = 70127
},
[61238] = {
commodityId = 61238,
commodityName = "斗战神翎",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v8,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
630230
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 70128
},
[61239] = {
commodityId = 61239,
commodityName = "斗战神翎",
coinType = 200008,
price = 10,
beginTime = v5,
minVersion = "1.3.68.33",
itemIds = {
630231
}
},
[61240] = {
commodityId = 61240,
commodityName = "斗战神翎",
coinType = 200008,
price = 10,
beginTime = v5,
minVersion = "1.3.68.33",
itemIds = {
630232
}
},
[61241] = {
commodityId = 61241,
commodityName = "光耀圣冕",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630233
},
suitId = 70129
},
[61242] = {
commodityId = 61242,
commodityName = "光耀圣冕",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630234
}
},
[61243] = {
commodityId = 61243,
commodityName = "光耀圣冕",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630235
}
},
[61244] = {
commodityId = 61244,
commodityName = "星梦一号",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630236
},
suitId = 70130
},
[61245] = {
commodityId = 61245,
commodityName = "分享刨冰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630237
},
suitId = 70131
},
[61246] = {
commodityId = 61246,
commodityName = "分享刨冰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630238
}
},
[61247] = {
commodityId = 61247,
commodityName = "分享刨冰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630239
}
},
[61248] = {
commodityId = 61248,
commodityName = "小熊汉堡",
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630240
},
suitId = 70132
},
[61249] = {
commodityId = 61249,
commodityName = "小熊汉堡",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630241
}
},
[61250] = {
commodityId = 61250,
commodityName = "小熊汉堡",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630242
}
},
[61251] = {
commodityId = 61251,
commodityName = "尊贵娘娘",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630243
},
suitId = 70133
},
[61252] = {
commodityId = 61252,
commodityName = "幻彩魅梦",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v8,
shopSort = 1,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
630244
},
bOpenSuit = true,
suitId = 70134
},
[61253] = {
commodityId = 61253,
commodityName = "绝顶甜蜜",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
itemIds = {
630245
},
suitId = 70135
},
[61254] = {
commodityId = 61254,
commodityName = "E人灯牌",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630246
},
suitId = 70136
},
[61255] = {
commodityId = 61255,
commodityName = "I人灯牌",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630247
},
suitId = 70137
},
[61256] = {
commodityId = 61256,
commodityName = "水晶蝴蝶",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v8,
shopSort = 1,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = "1.3.12.118",
itemIds = {
630248
},
bOpenSuit = true,
suitId = 70138
},
[61257] = {
commodityId = 61257,
commodityName = "水晶蝴蝶",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.12.118",
itemIds = {
630249
}
},
[61258] = {
commodityId = 61258,
commodityName = "水晶蝴蝶",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.12.118",
itemIds = {
630250
}
},
[61259] = {
commodityId = 61259,
commodityName = "老铁666",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630251
},
suitId = 70139
},
[61260] = {
commodityId = 61260,
commodityName = "彩蝶飞",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630252
},
suitId = 70140
},
[61261] = {
commodityId = 61261,
commodityName = "就是矫情",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v8,
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
630253
},
bOpenSuit = true,
suitId = 70141
},
[61262] = {
commodityId = 61262,
commodityName = "就是矫情",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630254
}
},
[61263] = {
commodityId = 61263,
commodityName = "就是矫情",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630255
}
},
[61264] = {
commodityId = 61264,
commodityName = "闪耀心冕",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
shopSort = 1,
minVersion = "1.3.12.1",
itemIds = {
630256
},
suitId = 70142
},
[61265] = {
commodityId = 61265,
commodityName = "指南针",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630257
},
suitId = 70143
},
[61266] = {
commodityId = 61266,
commodityName = "早8农场见",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630258
},
suitId = 70144
},
[61267] = {
commodityId = 61267,
commodityName = "猫咪罐头",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630259
},
suitId = 70145
},
[61268] = {
commodityId = 61268,
commodityName = "狗狗罐头",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630260
},
suitId = 70146
},
[61269] = {
commodityId = 61269,
commodityName = "羽毛球",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630261
},
suitId = 70147
},
[61270] = {
commodityId = 61270,
commodityName = "乒乓球",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630262
},
suitId = 70148
},
[61271] = {
commodityId = 61271,
commodityName = "禁止贴贴灯牌",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630263
},
suitId = 70149
},
[61272] = {
commodityId = 61272,
commodityName = "海滩小船",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
minVersion = "1.3.12.1",
itemIds = {
630264
},
suitId = 70150
},
[61273] = {
commodityId = 61273,
commodityName = "茄子",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
630265
},
bOpenSuit = true,
suitId = 70151
},
[61274] = {
commodityId = 61274,
commodityName = "青菜",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v8,
jumpId = 557,
jumpText = "遗落的珍宝",
minVersion = "1.3.68.52",
itemIds = {
630266
},
bOpenSuit = true,
suitId = 70152
},
[61275] = {
commodityId = 61275,
commodityName = "许愿瓶",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
630267
},
bOpenSuit = true,
suitId = 70153
},
[61276] = {
commodityId = 61276,
commodityName = "许愿瓶",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.12.1",
itemIds = {
630268
}
},
[61277] = {
commodityId = 61277,
commodityName = "许愿瓶",
coinType = 200008,
price = 5,
beginTime = v5,
minVersion = "1.3.12.1",
itemIds = {
630269
}
},
[61278] = {
commodityId = 61278,
commodityName = "梦幻天驹",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v8,
shopSort = 1,
jumpId = 706,
jumpText = v10,
minVersion = "1.3.12.1",
itemIds = {
630270
},
bOpenSuit = true,
suitId = 70154
},
[61279] = {
commodityId = 61279,
commodityName = "梦幻天驹",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.1",
itemIds = {
630271
}
},
[61280] = {
commodityId = 61280,
commodityName = "梦幻天驹",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.1",
itemIds = {
630272
}
},
[61281] = {
commodityId = 61281,
commodityName = "晶玉双蝶",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630273
},
giftCoinType = 1,
suitId = 70155
},
[61282] = {
commodityId = 61282,
commodityName = "晶玉双蝶",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630274
}
},
[61283] = {
commodityId = 61283,
commodityName = "晶玉双蝶",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630275
}
},
[61284] = {
commodityId = 61284,
commodityName = "奶油萌萌卷",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630276
},
suitId = 70156
},
[61285] = {
commodityId = 61285,
commodityName = "一头平民",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630277
},
suitId = 70157
},
[61286] = {
commodityId = 61286,
commodityName = "铁好人",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630278
},
suitId = 70158
},
[61287] = {
commodityId = 61287,
commodityName = "樱桃慕斯",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630279
},
suitId = 70159
},
[61288] = {
commodityId = 61288,
commodityName = "农场币",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729785599
},
shopTag = v8,
shopSort = 1,
jumpId = 366,
jumpText = "甜兔仙踪",
minVersion = "1.3.12.52",
itemIds = {
630280
},
bOpenSuit = true,
suitId = 70160
},
[61289] = {
commodityId = 61289,
commodityName = "火凤羽冠",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v8,
shopSort = 1,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
630281
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 70161
},
[61290] = {
commodityId = 61290,
commodityName = "火凤羽冠",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.90",
itemIds = {
630282
}
},
[61291] = {
commodityId = 61291,
commodityName = "火凤羽冠",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.12.90",
itemIds = {
630283
}
},
[61292] = {
commodityId = 61292,
commodityName = "祥瑞喜鹊",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v8,
shopSort = 1,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
630284
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 70162
},
[61293] = {
commodityId = 61293,
commodityName = "祥瑞喜鹊",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = "1.3.12.90",
itemIds = {
630285
}
},
[61294] = {
commodityId = 61294,
commodityName = "祥瑞喜鹊",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = "1.3.12.90",
itemIds = {
630286
}
},
[61295] = {
commodityId = 61295,
commodityName = "星际电波",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.37",
itemIds = {
630287
},
bOpenSuit = true,
suitId = 70163
},
[61296] = {
commodityId = 61296,
commodityName = "星际电波",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630288
}
},
[61297] = {
commodityId = 61297,
commodityName = "星际电波",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630289
}
},
[61298] = {
commodityId = 61298,
commodityName = "好运来",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.68.52",
itemIds = {
630290
},
bOpenSuit = true,
suitId = 70164
},
[61299] = {
commodityId = 61299,
commodityName = "心意纸飞机",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630291
},
suitId = 70165
},
[61300] = {
commodityId = 61300,
commodityName = "纯情信封",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630292
},
suitId = 70166
},
[61301] = {
commodityId = 61301,
commodityName = "金苹果",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630293
},
suitId = 70167
},
[61302] = {
commodityId = 61302,
commodityName = "阳光精灵",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630294
},
suitId = 70168
},
[61303] = {
commodityId = 61303,
commodityName = "月光精灵",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v8,
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61304] = {
commodityId = 61304,
commodityName = "情定之时",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630296
},
suitId = 70170
},
[61305] = {
commodityId = 61305,
commodityName = "瓜王争霸",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630297
},
suitId = 70171
},
[61306] = {
commodityId = 61306,
commodityName = "永不空军",
beginTime = {
seconds = 1725033600
},
endTime = {
seconds = 1726243199
},
shopTag = v8,
jumpId = 412,
jumpText = "洋葱牛奶祈愿",
minVersion = "1.3.18.1",
itemIds = {
630298
},
bOpenSuit = true,
suitId = 70172
},
[61307] = {
commodityId = 61307,
commodityName = "种菜专家",
beginTime = {
seconds = 1725033600
},
endTime = {
seconds = 1726243199
},
shopTag = v8,
jumpId = 413,
jumpText = "洋葱牛奶祈愿",
minVersion = "1.3.18.1",
itemIds = {
630299
},
bOpenSuit = true,
suitId = 70173
},
[61308] = {
commodityId = 61308,
commodityName = "吃瓜群众",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630300
},
giftCoinType = 1,
suitId = 70174
},
[61309] = {
commodityId = 61309,
commodityName = "吃瓜群众",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630301
}
},
[61310] = {
commodityId = 61310,
commodityName = "吃瓜群众",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630302
}
},
[61311] = {
commodityId = 61311,
commodityName = "元气满满",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630303
},
giftCoinType = 1,
suitId = 70175
},
[61312] = {
commodityId = 61312,
commodityName = "元气满满",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630304
}
},
[61313] = {
commodityId = 61313,
commodityName = "元气满满",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630305
}
},
[61314] = {
commodityId = 61314,
commodityName = "温泉毛巾",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v8,
shopSort = 1,
jumpId = 613,
jumpText = "百变小新",
itemIds = {
630306
},
bOpenSuit = true,
suitId = 70176
},
[61315] = {
commodityId = 61315,
commodityName = "微笑玫瑰",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v8,
shopSort = 1,
jumpId = 619,
jumpText = v10,
minVersion = "1.3.18.1",
itemIds = {
630307
},
bOpenSuit = true,
suitId = 70177
},
[61316] = {
commodityId = 61316,
commodityName = "微笑玫瑰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630308
}
},
[61317] = {
commodityId = 61317,
commodityName = "微笑玫瑰",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630309
}
},
[61318] = {
commodityId = 61318,
commodityName = "精选好礼",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630310
},
suitId = 70178
},
[61319] = {
commodityId = 61319,
commodityName = "珍藏心意",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630311
},
suitId = 70179
},
[61320] = {
commodityId = 61320,
commodityName = "柿柿如意",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630312
},
suitId = 70180
},
[61321] = {
commodityId = 61321,
commodityName = "投了投了",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
630313
},
bOpenSuit = true,
suitId = 70181
},
[61322] = {
commodityId = 61322,
commodityName = "稳了稳了",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630314
},
suitId = 70182
},
[61323] = {
commodityId = 61323,
commodityName = "一起跳舞",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630315
},
suitId = 70183
},
[61324] = {
commodityId = 61324,
commodityName = "求放过",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630316
},
suitId = 70184
},
[61325] = {
commodityId = 61325,
commodityName = "已老实",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630317
},
suitId = 70185
},
[61326] = {
commodityId = 61326,
commodityName = "多汁番茄",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
630318
},
bOpenSuit = true,
suitId = 70186
},
[61327] = {
commodityId = 61327,
commodityName = "多汁番茄",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630319
}
},
[61328] = {
commodityId = 61328,
commodityName = "多汁番茄",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630320
}
},
[61329] = {
commodityId = 61329,
commodityName = "绒绒狼宝",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630321
},
giftCoinType = 1,
suitId = 70187
},
[61330] = {
commodityId = 61330,
commodityName = "绒绒狼宝",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630322
}
},
[61331] = {
commodityId = 61331,
commodityName = "绒绒狼宝",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630323
}
},
[61332] = {
commodityId = 61332,
commodityName = "星辉烁羽",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v8,
shopSort = 1,
jumpId = 619,
jumpText = v10,
minVersion = "1.3.18.1",
itemIds = {
630324
},
bOpenSuit = true,
suitId = 70188
},
[61333] = {
commodityId = 61333,
commodityName = "星辉烁羽",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630325
}
},
[61334] = {
commodityId = 61334,
commodityName = "星辉烁羽",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630326
}
},
[61335] = {
commodityId = 61335,
commodityName = "一口秋浓",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630327
},
suitId = 70189
},
[61336] = {
commodityId = 61336,
commodityName = "美味白菜",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630328
},
suitId = 70190
},
[61337] = {
commodityId = 61337,
commodityName = "金桂玉冠",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v8,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
minVersion = "1.3.18.37",
itemIds = {
630329
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 70191
},
[61338] = {
commodityId = 61338,
commodityName = "金桂玉冠",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630330
}
},
[61339] = {
commodityId = 61339,
commodityName = "金桂玉冠",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630331
}
},
[61340] = {
commodityId = 61340,
commodityName = "月色瑶华",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v8,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
minVersion = "1.3.18.37",
itemIds = {
630332
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 70192
},
[61341] = {
commodityId = 61341,
commodityName = "月色瑶华",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630333
}
},
[61342] = {
commodityId = 61342,
commodityName = "月色瑶华",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630334
}
},
[61343] = {
commodityId = 61343,
commodityName = "绵羊宝宝",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v8,
jumpId = 10600,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
630335
},
giftCoinType = 1,
bOpenSuit = true,
suitId = 70193
},
[61344] = {
commodityId = 61344,
commodityName = "热狗派对",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v8,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
630336
},
giftCoinType = 1,
bOpenSuit = true,
suitId = 70194
},
[61345] = {
commodityId = 61345,
commodityName = "热狗派对",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630337
}
},
[61346] = {
commodityId = 61346,
commodityName = "热狗派对",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630338
}
},
[61347] = {
commodityId = 61347,
commodityName = "农场无人机",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1710345599
},
shopTag = v8,
jumpId = 573,
jumpText = "梦幻告白",
minVersion = "1.3.26.23",
itemIds = {
630339
},
bOpenSuit = true,
suitId = 70195
},
[61348] = {
commodityId = 61348,
commodityName = "牧场小助手",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1710345599
},
shopTag = v8,
jumpId = 573,
jumpText = "梦幻告白",
minVersion = "1.3.26.23",
itemIds = {
630340
},
bOpenSuit = true,
suitId = 70196
},
[61349] = {
commodityId = 61349,
commodityName = "采摘小福星",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630341
},
suitId = 70197
},
[61350] = {
commodityId = 61350,
commodityName = "饲料大胃王",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630342
},
suitId = 70198
},
[61351] = {
commodityId = 61351,
commodityName = "丰收浇灌者",
beginTime = {
seconds = 1727884800
},
endTime = {
seconds = 1729785599
},
shopTag = v8,
jumpId = 377,
jumpText = "绿洲奇遇",
minVersion = "1.3.18.81",
itemIds = {
630343
},
bOpenSuit = true,
suitId = 70199
},
[61352] = {
commodityId = 61352,
commodityName = "钓鱼全能王",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
shopTag = v8,
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.26.96",
itemIds = {
630344
},
bOpenSuit = true,
suitId = 70200
},
[61353] = {
commodityId = 61353,
commodityName = "农场无人机",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630345
}
},
[61354] = {
commodityId = 61354,
commodityName = "农场无人机",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630346
}
},
[61355] = {
commodityId = 61355,
commodityName = "菜园猎手",
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1735919999
},
shopTag = v8,
jumpId = 368,
jumpText = "丰收兔",
minVersion = "1.3.18.54",
itemIds = {
630347
},
bOpenSuit = true,
suitId = 70201
},
[61356] = {
commodityId = 61356,
commodityName = "菜园猎手",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630348
}
},
[61357] = {
commodityId = 61357,
commodityName = "菜园猎手",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630349
}
},
[61358] = {
commodityId = 61358,
commodityName = "吾皇御印",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v8,
jumpId = 1092,
jumpText = "吾皇猫",
minVersion = "1.3.88.53",
itemIds = {
630350
},
giftCoinType = 1,
bOpenSuit = true,
suitId = 70202
},
[61359] = {
commodityId = 61359,
commodityName = "见龙吟",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v8,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
630351
},
bOpenSuit = true,
suitId = 70203
},
[61360] = {
commodityId = 61360,
commodityName = "见龙吟",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630352
}
},
[61361] = {
commodityId = 61361,
commodityName = "见龙吟",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630353
}
},
[61362] = {
commodityId = 61362,
commodityName = "萌菇贝贝",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630354
},
suitId = 70204
},
[61363] = {
commodityId = 61363,
commodityName = "萌菇贝贝",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630355
}
},
[61364] = {
commodityId = 61364,
commodityName = "萌菇贝贝",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630356
}
},
[61366] = {
commodityId = 61366,
commodityName = "绝代天椒",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630358
},
suitId = 70206
},
[61367] = {
commodityId = 61367,
commodityName = "秋毫明探",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630359
},
suitId = 70207
},
[61368] = {
commodityId = 61368,
commodityName = "玉轮弯月",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630360
},
suitId = 70208
},
[61365] = {
commodityId = 61365,
commodityName = "江湖礼节",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630357
},
suitId = 70205
},
[61369] = {
commodityId = 61369,
commodityName = "月光精灵",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v8,
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61370] = {
commodityId = 61370,
commodityName = "星核之光",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v8,
shopSort = 1,
jumpId = 623,
jumpText = v10,
minVersion = "1.3.26.1",
itemIds = {
630361
},
bOpenSuit = true,
suitId = 70209
},
[61371] = {
commodityId = 61371,
commodityName = "星核之光",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630362
}
},
[61372] = {
commodityId = 61372,
commodityName = "星核之光",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630363
}
},
[61373] = {
commodityId = 61373,
commodityName = "绒绒暖帽",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739980799
},
shopTag = v8,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.68.33",
itemIds = {
630364
},
bOpenSuit = true,
suitId = 70210
},
[61374] = {
commodityId = 61374,
commodityName = "绒绒暖帽",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630365
}
},
[61375] = {
commodityId = 61375,
commodityName = "绒绒暖帽",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630366
}
},
[61376] = {
commodityId = 61376,
commodityName = "萌橘天降",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630367
},
suitId = 70211
},
[61377] = {
commodityId = 61377,
commodityName = "幸运时刻",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630368
},
suitId = 70212
},
[61378] = {
commodityId = 61378,
commodityName = "雷霆轻语",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630369
},
suitId = 70213
},
[61379] = {
commodityId = 61379,
commodityName = "流星碎片",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.26.1",
itemIds = {
630370
},
bOpenSuit = true,
suitId = 70214
},
[61380] = {
commodityId = 61380,
commodityName = "流星碎片",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630371
}
},
[61381] = {
commodityId = 61381,
commodityName = "流星碎片",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630372
}
},
[61382] = {
commodityId = 61382,
commodityName = "银河星兽",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v8,
shopSort = 1,
jumpId = 623,
jumpText = v10,
minVersion = "1.3.26.1",
itemIds = {
630373
},
bOpenSuit = true,
suitId = 70215
},
[61383] = {
commodityId = 61383,
commodityName = "银河星兽",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630374
}
},
[61384] = {
commodityId = 61384,
commodityName = "银河星兽",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630375
}
},
[61385] = {
commodityId = 61385,
commodityName = "奇珍",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
shopTag = v8,
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.26.96",
itemIds = {
630376
},
bOpenSuit = true,
suitId = 70216
},
[61386] = {
commodityId = 61386,
commodityName = "奇珍",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630377
}
},
[61387] = {
commodityId = 61387,
commodityName = "奇珍",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630378
}
},
[61388] = {
commodityId = 61388,
commodityName = "金冠稀世",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
shopTag = v8,
shopSort = 1,
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.26.96",
itemIds = {
630379
},
bOpenSuit = true,
suitId = 70217
},
[61389] = {
commodityId = 61389,
commodityName = "金冠稀世",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630380
}
},
[61390] = {
commodityId = 61390,
commodityName = "金冠稀世",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630381
}
},
[61391] = {
commodityId = 61391,
commodityName = "我在元梦等你",
endTime = v7,
shopTag = v8,
itemIds = {
630382
},
suitId = 70218
},
[61392] = {
commodityId = 61392,
commodityName = "烛泪之魂",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630383
},
suitId = 70219
},
[61393] = {
commodityId = 61393,
commodityName = "魔力之源",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630384
},
suitId = 70220
},
[61394] = {
commodityId = 61394,
commodityName = "魔力之源",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630385
}
},
[61395] = {
commodityId = 61395,
commodityName = "魔力之源",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630386
}
},
[61396] = {
commodityId = 61396,
commodityName = "月光精灵",
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1736697599
},
shopTag = v8,
shopSort = 1,
jumpId = 10701,
jumpText = "峡谷幻梦",
minVersion = "1.3.37.37",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61397] = {
commodityId = 61397,
commodityName = "小心猫猫",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630387
},
suitId = 70221
},
[61398] = {
commodityId = 61398,
commodityName = "心碎之声",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028800
},
shopTag = v8,
jumpId = 1078,
jumpText = "百合信使",
minVersion = "1.3.68.101",
itemIds = {
630388
},
bOpenSuit = true,
suitId = 70222
},
[61399] = {
commodityId = 61399,
commodityName = "翼展光明",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630389
},
suitId = 70223
},
[61400] = {
commodityId = 61400,
commodityName = "翼展光明",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630390
}
},
[61401] = {
commodityId = 61401,
commodityName = "翼展光明",
coinType = 200008,
price = 5,
beginTime = v5,
itemIds = {
630391
}
},
[61402] = {
commodityId = 61402,
commodityName = "心梦光环",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v8,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
630392
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 70224
},
[61403] = {
commodityId = 61403,
commodityName = "心梦光环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.26.71",
itemIds = {
630393
}
},
[61404] = {
commodityId = 61404,
commodityName = "心梦光环",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.26.71",
itemIds = {
630394
}
},
[61405] = {
commodityId = 61405,
commodityName = "绝对主角",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630395
},
suitId = 70225
},
[61406] = {
commodityId = 61406,
commodityName = "马卡龙",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v8,
shopSort = 1,
jumpId = 630,
jumpText = v10,
minVersion = "1.3.37.1",
itemIds = {
630396
},
bOpenSuit = true,
suitId = 70226
},
[61407] = {
commodityId = 61407,
commodityName = "马卡龙",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630397
}
},
[61408] = {
commodityId = 61408,
commodityName = "马卡龙",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630398
}
},
[61409] = {
commodityId = 61409,
commodityName = "大蒜头",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.37.1",
itemIds = {
630399
},
bOpenSuit = true,
suitId = 70227
},
[61410] = {
commodityId = 61410,
commodityName = "大蒜头",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630400
}
},
[61411] = {
commodityId = 61411,
commodityName = "大蒜头",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630401
}
},
[61412] = {
commodityId = 61412,
commodityName = "双耳睡帽A",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630402
},
suitId = 70228
},
[61413] = {
commodityId = 61413,
commodityName = "双耳睡帽B",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630403
},
suitId = 70229
},
[61414] = {
commodityId = 61414,
commodityName = "双耳睡帽C",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630404
},
suitId = 70230
},
[61415] = {
commodityId = 61415,
commodityName = "狗骨头",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630405
},
suitId = 70231
},
[61416] = {
commodityId = 61416,
commodityName = "划水",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630406
},
suitId = 70232
},
[61417] = {
commodityId = 61417,
commodityName = "废话不多说字牌",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630407
},
suitId = 70233
},
[61418] = {
commodityId = 61418,
commodityName = "甜点熊猫",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v8,
shopSort = 1,
jumpId = 630,
jumpText = v10,
minVersion = "1.3.37.1",
itemIds = {
630408
},
bOpenSuit = true,
suitId = 70234
},
[61419] = {
commodityId = 61419,
commodityName = "甜点熊猫",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630409
}
},
[61420] = {
commodityId = 61420,
commodityName = "甜点熊猫",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630410
}
},
[61421] = {
commodityId = 61421,
commodityName = "飞鼠宝宝",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v8,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
itemIds = {
630411
},
bOpenSuit = true,
suitId = 70235
},
[61422] = {
commodityId = 61422,
commodityName = "飞鼠宝宝",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630412
}
},
[61423] = {
commodityId = 61423,
commodityName = "飞鼠宝宝",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630413
}
},
[61424] = {
commodityId = 61424,
commodityName = "树叶触角",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630414
},
suitId = 70236
},
[61425] = {
commodityId = 61425,
commodityName = "树叶触角",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630415
}
},
[61426] = {
commodityId = 61426,
commodityName = "树叶触角",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630416
}
},
[61427] = {
commodityId = 61427,
commodityName = "幸运小蛙",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630417
},
suitId = 70237
},
[61428] = {
commodityId = 61428,
commodityName = "幸运小蛙",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630418
}
},
[61429] = {
commodityId = 61429,
commodityName = "幸运小蛙",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630419
}
},
[61430] = {
commodityId = 61430,
commodityName = "给我点点",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630420
},
suitId = 70238
},
[61431] = {
commodityId = 61431,
commodityName = "真香",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v8,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
630421
},
bOpenSuit = true,
suitId = 70239
},
[61432] = {
commodityId = 61432,
commodityName = "好感度系统",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630422
},
suitId = 70240
},
[61433] = {
commodityId = 61433,
commodityName = "冰雪兔兔",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v8,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
630423
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 70241
},
[61434] = {
commodityId = 61434,
commodityName = "冰雪兔兔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630424
}
},
[61435] = {
commodityId = 61435,
commodityName = "冰雪兔兔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630425
}
},
[61436] = {
commodityId = 61436,
commodityName = "甜筒冰冰",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v8,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = "1.3.37.87",
itemIds = {
630426
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 80,
giftCoinType = 224,
giftPrice = 30,
bOpenSuit = true,
suitId = 70242
},
[61437] = {
commodityId = 61437,
commodityName = "甜筒冰冰",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630427
}
},
[61438] = {
commodityId = 61438,
commodityName = "甜筒冰冰",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630428
}
},
[61439] = {
commodityId = 61439,
commodityName = "兔子苹果",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630429
},
suitId = 70243
},
[61440] = {
commodityId = 61440,
commodityName = "名侦探出动",
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742140799
},
shopTag = v8,
jumpId = 580,
jumpText = "满减福利",
minVersion = "1.3.37.37",
itemIds = {
630430
},
bOpenSuit = true,
suitId = 70244
},
[61441] = {
commodityId = 61441,
commodityName = "名侦探出动",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1740758400
},
itemIds = {
630431
}
},
[61442] = {
commodityId = 61442,
commodityName = "名侦探出动",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1740758400
},
itemIds = {
630432
}
},
[61443] = {
commodityId = 61443,
commodityName = "樱花班小黄帽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630433
},
suitId = 70245
},
[61444] = {
commodityId = 61444,
commodityName = "大厨之帽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630434
},
suitId = 70246
},
[61445] = {
commodityId = 61445,
commodityName = "冲天辫辫",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630435
},
suitId = 70247
},
[61446] = {
commodityId = 61446,
commodityName = "金锁吉祥",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v8,
shopSort = 1,
jumpId = 636,
jumpText = v10,
minVersion = "1.3.68.1",
itemIds = {
630436
},
bOpenSuit = true,
suitId = 70248
},
[61447] = {
commodityId = 61447,
commodityName = "金锁吉祥",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630437
}
},
[61448] = {
commodityId = 61448,
commodityName = "金锁吉祥",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630438
}
},
[61449] = {
commodityId = 61449,
commodityName = "八方卦象盘",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630439
},
suitId = 70249
},
[61450] = {
commodityId = 61450,
commodityName = "八方卦象盘",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630440
}
},
[61451] = {
commodityId = 61451,
commodityName = "八方卦象盘",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630441
}
},
[61452] = {
commodityId = 61452,
commodityName = "团圆麦香",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630442
},
suitId = 70250
},
[61453] = {
commodityId = 61453,
commodityName = "主角遥控杆",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630443
},
suitId = 70251
},
[61454] = {
commodityId = 61454,
commodityName = "元梦星期五天团",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630444
},
suitId = 70252
},
[61455] = {
commodityId = 61455,
commodityName = "小鸟之家",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v8,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = "1.3.37.87",
itemIds = {
630445
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 80,
giftCoinType = 224,
giftPrice = 30,
bOpenSuit = true,
suitId = 70253
},
[61456] = {
commodityId = 61456,
commodityName = "小鸟之家",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630446
}
},
[61457] = {
commodityId = 61457,
commodityName = "小鸟之家",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630447
}
},
[61458] = {
commodityId = 61458,
commodityName = "小鹿星",
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1738252799
},
shopTag = v8,
jumpId = 521,
jumpText = "雪球精灵",
minVersion = "1.3.37.1",
itemIds = {
630448
}
},
[61459] = {
commodityId = 61459,
commodityName = "小鹿星",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736438400
},
itemIds = {
630449
}
},
[61460] = {
commodityId = 61460,
commodityName = "小鹿星",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736438400
},
itemIds = {
630450
}
},
[61461] = {
commodityId = 61461,
commodityName = "2025",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630451
},
suitId = 70255
},
[61462] = {
commodityId = 61462,
commodityName = "天使之辉",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v8,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
630452
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 70256
},
[61463] = {
commodityId = 61463,
commodityName = "天使之辉",
coinType = 200008,
price = 10,
beginTime = v5,
minVersion = "1.3.68.33",
itemIds = {
630453
}
},
[61464] = {
commodityId = 61464,
commodityName = "天使之辉",
coinType = 200008,
price = 10,
beginTime = v5,
minVersion = "1.3.68.33",
itemIds = {
630454
}
},
[61465] = {
commodityId = 61465,
commodityName = "烟囱小熊",
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1738252799
},
shopTag = v8,
jumpId = 521,
jumpText = "雪球精灵",
minVersion = "1.3.37.1",
itemIds = {
630455
}
},
[61466] = {
commodityId = 61466,
commodityName = "烟囱小熊",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736438400
},
itemIds = {
630456
}
},
[61467] = {
commodityId = 61467,
commodityName = "烟囱小熊",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736438400
},
itemIds = {
630457
}
},
[61468] = {
commodityId = 61468,
commodityName = "猫盒",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630458
},
suitId = 70258
},
[61469] = {
commodityId = 61469,
commodityName = "香酥菠萝包",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v8,
jumpId = 694,
jumpText = "幻音喵境",
itemIds = {
630459
},
bOpenSuit = true,
suitId = 70259
},
[61470] = {
commodityId = 61470,
commodityName = "香酥菠萝包",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630460
}
},
[61471] = {
commodityId = 61471,
commodityName = "香酥菠萝包",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630461
}
},
[61472] = {
commodityId = 61472,
commodityName = "弹弹网球",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630462
},
suitId = 70260
},
[61473] = {
commodityId = 61473,
commodityName = "爱心手势",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630463
},
suitId = 70261
},
[61474] = {
commodityId = 61474,
commodityName = "社恐小蛛",
beginTime = {
seconds = 1740672000
},
endTime = v7,
shopTag = v8,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
itemIds = {
630464
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 70262
},
[61475] = {
commodityId = 61475,
commodityName = "社恐小蛛",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630465
}
},
[61476] = {
commodityId = 61476,
commodityName = "社恐小蛛",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630466
}
},
[61477] = {
commodityId = 61477,
commodityName = "很想你",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v8,
jumpId = 1083,
jumpText = "墨影流光",
minVersion = "1.3.78.99",
itemIds = {
630467
},
bOpenSuit = true,
suitId = 70263
},
[61478] = {
commodityId = 61478,
commodityName = "很想你",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630468
}
},
[61479] = {
commodityId = 61479,
commodityName = "很想你",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630469
}
},
[61480] = {
commodityId = 61480,
commodityName = "买它！",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630470
},
suitId = 70264
},
[61481] = {
commodityId = 61481,
commodityName = "种草",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630471
},
suitId = 70265
},
[61482] = {
commodityId = 61482,
commodityName = "年年有余",
beginTime = {
seconds = 1738080000
},
endTime = {
seconds = 1741276799
},
shopTag = v8,
jumpId = 553,
jumpText = "年年有鱼",
itemIds = {
630472
},
suitId = 70266
},
[61483] = {
commodityId = 61483,
commodityName = "金光火眼",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1752163199
},
shopTag = v8,
jumpId = 7001,
jumpText = "幸运翻翻乐",
itemIds = {
630473
},
suitId = 70267
},
[61484] = {
commodityId = 61484,
commodityName = "幸福天降",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630474
},
suitId = 70268
},
[61485] = {
commodityId = 61485,
commodityName = "过年好",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630475
},
suitId = 70269
},
[61486] = {
commodityId = 61486,
commodityName = "热烈街篮",
beginTime = {
seconds = 1736870400
},
endTime = {
seconds = 1741881599
},
shopTag = v8,
jumpId = 544,
jumpText = "猫猫搜城记",
itemIds = {
630476
},
bOpenSuit = true,
suitId = 70270
},
[61487] = {
commodityId = 61487,
commodityName = "比翼双栖",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v8,
shopSort = 1,
jumpId = 636,
jumpText = v10,
minVersion = "1.3.68.1",
itemIds = {
630477
},
bOpenSuit = true,
suitId = 70271
},
[61488] = {
commodityId = 61488,
commodityName = "比翼双栖",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630478
}
},
[61489] = {
commodityId = 61489,
commodityName = "比翼双栖",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630479
}
},
[61490] = {
commodityId = 61490,
commodityName = "举个栗子",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630480
},
suitId = 70272
},
[61491] = {
commodityId = 61491,
commodityName = "雪花飘飘",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630481
},
suitId = 70273
},
[61492] = {
commodityId = 61492,
commodityName = "蜜糖暖意",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630482
},
suitId = 70274
},
[61493] = {
commodityId = 61493,
commodityName = "播音喇叭",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630483
},
suitId = 70275
},
[61494] = {
commodityId = 61494,
commodityName = "彩虹独角",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v8,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
itemIds = {
630484
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 160,
giftCoinType = 224,
giftPrice = 180,
bOpenSuit = true,
suitId = 70276
},
[61495] = {
commodityId = 61495,
commodityName = "彩虹独角",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630485
}
},
[61496] = {
commodityId = 61496,
commodityName = "彩虹独角",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630486
}
},
[61500] = {
commodityId = 61500,
commodityName = "月光精灵",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v8,
shopSort = 1,
jumpId = 10710,
jumpText = "峡谷幻梦",
minVersion = "1.3.37.97",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61501] = {
commodityId = 61501,
commodityName = "美乐蒂绵绵冰",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v8,
jumpId = 1073,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
630487
},
bOpenSuit = true,
suitId = 70277
},
[61502] = {
commodityId = 61502,
commodityName = "大耳狗礼帽",
beginTime = {
seconds = 1672588800
},
endTime = {
seconds = 1675267199
},
shopTag = v8,
minVersion = "1.3.37.87",
itemIds = {
630488
},
bOpenSuit = true,
suitId = 70278
},
[61503] = {
commodityId = 61503,
commodityName = "灵狐赐福",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v8,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
630489
},
bOpenSuit = true,
suitId = 70279
},
[61504] = {
commodityId = 61504,
commodityName = "灵狐赐福",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.68.100",
itemIds = {
630490
}
},
[61505] = {
commodityId = 61505,
commodityName = "灵狐赐福",
coinType = 200008,
price = 10,
beginTime = v6,
minVersion = "1.3.68.100",
itemIds = {
630491
}
},
[61506] = {
commodityId = 61506,
commodityName = "花枝头饰",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v8,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
630492
},
bOpenSuit = true,
suitId = 70280
},
[61507] = {
commodityId = 61507,
commodityName = "花枝头饰",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = "1.3.68.100",
itemIds = {
630493
}
},
[61508] = {
commodityId = 61508,
commodityName = "花枝头饰",
coinType = 200008,
price = 5,
beginTime = v6,
minVersion = "1.3.68.100",
itemIds = {
630494
}
},
[61509] = {
commodityId = 61509,
commodityName = "海洋小兔",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v8,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
minVersion = "1.3.68.87",
itemIds = {
630495
},
bOpenSuit = true,
suitId = 70281
},
[61510] = {
commodityId = 61510,
commodityName = "海洋小兔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630496
}
},
[61511] = {
commodityId = 61511,
commodityName = "海洋小兔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630497
}
},
[61512] = {
commodityId = 61512,
commodityName = "热力应援",
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
shopTag = v8,
jumpId = 568,
jumpText = "莱恩特咖啡屋",
itemIds = {
630498
},
bOpenSuit = true,
suitId = 70282
},
[61513] = {
commodityId = 61513,
commodityName = "热力应援",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630499
}
},
[61514] = {
commodityId = 61514,
commodityName = "热力应援",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630500
}
},
[61515] = {
commodityId = 61515,
commodityName = "福娃小虎",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630501
},
suitId = 70283
},
[61516] = {
commodityId = 61516,
commodityName = "福娃小虎",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630502
}
},
[61517] = {
commodityId = 61517,
commodityName = "福娃小虎",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630503
}
},
[61518] = {
commodityId = 61518,
commodityName = "笑口常开",
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
shopTag = v8,
jumpId = 546,
jumpText = "福运琳琅",
itemIds = {
630504
},
suitId = 70284
},
[61519] = {
commodityId = 61519,
commodityName = "笑口常开",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630505
}
},
[61520] = {
commodityId = 61520,
commodityName = "笑口常开",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630506
}
},
[61521] = {
commodityId = 61521,
commodityName = "小笼喵",
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740067199
},
shopTag = v8,
jumpId = 551,
jumpText = "嘶嘶灵宝",
itemIds = {
630507
},
suitId = 70284
},
[61522] = {
commodityId = 61522,
commodityName = "小笼喵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630508
}
},
[61523] = {
commodityId = 61523,
commodityName = "小笼喵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630509
}
},
[61524] = {
commodityId = 61524,
commodityName = "唤醒清晨",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630510
},
suitId = 70286
},
[61525] = {
commodityId = 61525,
commodityName = "忧郁清晨",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630511
},
suitId = 70287
},
[61526] = {
commodityId = 61526,
commodityName = "团圆瓷勺",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630512
},
suitId = 70288
},
[61527] = {
commodityId = 61527,
commodityName = "骨感美鱼",
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
shopTag = v8,
jumpId = 546,
jumpText = "福运琳琅",
itemIds = {
630513
},
suitId = 70289
},
[61528] = {
commodityId = 61528,
commodityName = "趴趴猪",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v8,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
630514
},
bOpenSuit = true,
suitId = 70290
},
[61529] = {
commodityId = 61529,
commodityName = "趴趴猪",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630515
}
},
[61530] = {
commodityId = 61530,
commodityName = "趴趴猪",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630516
}
},
[61531] = {
commodityId = 61531,
commodityName = "靓发神器",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630517
},
suitId = 70291
},
[61532] = {
commodityId = 61532,
commodityName = "好笋",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630518
},
suitId = 70292
},
[61533] = {
commodityId = 61533,
commodityName = "午后茶歇",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v8,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
630519
},
bOpenSuit = true,
suitId = 70293
},
[61534] = {
commodityId = 61534,
commodityName = "午后茶歇",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630520
}
},
[61535] = {
commodityId = 61535,
commodityName = "午后茶歇",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630521
}
},
[61536] = {
commodityId = 61536,
commodityName = "永冻蔷薇",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v8,
shopSort = 1,
jumpId = 10714,
jumpText = "峡谷幻梦",
minVersion = "1.3.68.100",
itemIds = {
630522
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70294
},
[61537] = {
commodityId = 61537,
commodityName = "月光精灵",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v8,
shopSort = 1,
jumpId = 10714,
jumpText = "峡谷幻梦",
minVersion = "1.3.68.100",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61538] = {
commodityId = 61538,
commodityName = "梦魇之翼",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630523
},
suitId = 70295
},
[61539] = {
commodityId = 61539,
commodityName = "梦魇之翼",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630524
}
},
[61540] = {
commodityId = 61540,
commodityName = "梦魇之翼",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630525
}
},
[61541] = {
commodityId = 61541,
commodityName = "星愿苹果",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v8,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
630526
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40,
bOpenSuit = true,
suitId = 70296
},
[61542] = {
commodityId = 61542,
commodityName = "星愿苹果",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630527
}
},
[61543] = {
commodityId = 61543,
commodityName = "星愿苹果",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630528
}
},
[61544] = {
commodityId = 61544,
commodityName = "勤劳小蜂",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
shopTag = v8,
jumpId = 584,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
630529
},
suitId = 70297
},
[61545] = {
commodityId = 61545,
commodityName = "小太阳",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630530
},
suitId = 70298
},
[61546] = {
commodityId = 61546,
commodityName = "蔷薇之毒",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v8,
jumpId = 640,
jumpText = v10,
minVersion = "1.3.78.1",
itemIds = {
630531
},
bOpenSuit = true,
suitId = 70299
},
[61547] = {
commodityId = 61547,
commodityName = "蔷薇之毒",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630532
}
},
[61548] = {
commodityId = 61548,
commodityName = "蔷薇之毒",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630533
}
},
[61549] = {
commodityId = 61549,
commodityName = "幻耀神石",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v8,
jumpId = 640,
jumpText = v10,
minVersion = "1.3.78.1",
itemIds = {
630534
},
bOpenSuit = true,
suitId = 70300
},
[61550] = {
commodityId = 61550,
commodityName = "幻耀神石",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630535
}
},
[61551] = {
commodityId = 61551,
commodityName = "幻耀神石",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630536
}
},
[61552] = {
commodityId = 61552,
commodityName = "趴趴水豚",
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
shopTag = v8,
jumpId = 575,
jumpText = "桃坞问春",
minVersion = "1.3.68.90",
itemIds = {
630537
},
suitId = 70301
},
[61553] = {
commodityId = 61553,
commodityName = "蝶梦尘铃",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v8,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
630538
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
bOpenSuit = true,
suitId = 70302
},
[61554] = {
commodityId = 61554,
commodityName = "蝶梦尘铃",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630539
}
},
[61555] = {
commodityId = 61555,
commodityName = "蝶梦尘铃",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630540
}
},
[61556] = {
commodityId = 61556,
commodityName = "万彩潮流",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630541
},
suitId = 70303
},
[61557] = {
commodityId = 61557,
commodityName = "万彩潮流",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630542
}
},
[61558] = {
commodityId = 61558,
commodityName = "万彩潮流",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630543
}
},
[61559] = {
commodityId = 61559,
commodityName = "蔚蓝行星",
beginTime = {
seconds = 1743091200
},
shopTag = v8,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
630544
},
bOpenSuit = true,
suitId = 70304
},
[61560] = {
commodityId = 61560,
commodityName = "赤土星球",
beginTime = {
seconds = 1743091200
},
shopTag = v8,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
630545
},
bOpenSuit = true,
suitId = 70305
},
[61561] = {
commodityId = 61561,
commodityName = "梦幻星球",
beginTime = {
seconds = 1743091200
},
shopTag = v8,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
630546
},
bOpenSuit = true,
suitId = 70306
},
[61562] = {
commodityId = 61562,
commodityName = "拦路小帽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630547
},
suitId = 70307
},
[61563] = {
commodityId = 61563,
commodityName = "无限加载中",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630548
},
suitId = 70308
},
[61564] = {
commodityId = 61564,
commodityName = "糯糯青团",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630549
},
suitId = 70309
},
[61565] = {
commodityId = 61565,
commodityName = "晨曦小花",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630550
},
suitId = 70310
},
[61566] = {
commodityId = 61566,
commodityName = "晨曦小花",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630551
}
},
[61567] = {
commodityId = 61567,
commodityName = "晨曦小花",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630552
}
},
[61568] = {
commodityId = 61568,
commodityName = "树莓软糖",
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
shopTag = v8,
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
630553
},
suitId = 70311
},
[61569] = {
commodityId = 61569,
commodityName = "树莓软糖",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630554
}
},
[61570] = {
commodityId = 61570,
commodityName = "树莓软糖",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630555
}
},
[61571] = {
commodityId = 61571,
commodityName = "更衣香蕉鸭",
endTime = v7,
shopTag = v8,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
630556
},
suitId = 70312
},
[61572] = {
commodityId = 61572,
commodityName = "更衣香蕉鸭",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630557
}
},
[61573] = {
commodityId = 61573,
commodityName = "更衣香蕉鸭",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630558
}
},
[61574] = {
commodityId = 61574,
commodityName = "斑斓蘑菇",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630559
},
suitId = 70313
},
[61575] = {
commodityId = 61575,
commodityName = "斑斓蘑菇",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630560
}
},
[61576] = {
commodityId = 61576,
commodityName = "斑斓蘑菇",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630561
}
},
[61577] = {
commodityId = 61577,
commodityName = "樱花雪",
beginTime = {
seconds = 1743177600
},
endTime = {
seconds = 1745510399
},
shopTag = v8,
jumpId = 592,
jumpText = "狐爷爷",
minVersion = "1.3.78.58",
itemIds = {
630562
},
suitId = 70314
},
[61578] = {
commodityId = 61578,
commodityName = "樱花雪",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630563
}
},
[61579] = {
commodityId = 61579,
commodityName = "樱花雪",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630564
}
},
[61580] = {
commodityId = 61580,
commodityName = "鸡王之冠",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v8,
jumpId = 10598,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
630565
},
bOpenSuit = true,
suitId = 70315
},
[61581] = {
commodityId = 61581,
commodityName = "软酪精灵",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630566
},
suitId = 70316
},
[61582] = {
commodityId = 61582,
commodityName = "软酪精灵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630567
}
},
[61583] = {
commodityId = 61583,
commodityName = "软酪精灵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630568
}
},
[61584] = {
commodityId = 61584,
commodityName = "玲珑宝器",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v8,
shopSort = 1,
jumpId = 10718,
jumpText = "峡谷幻梦",
minVersion = "1.3.78.80",
itemIds = {
630569
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70317
},
[61585] = {
commodityId = 61585,
commodityName = "咔咔大盗",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v8,
jumpId = 594,
jumpText = "蜜糖彩虹之梦",
minVersion = "1.3.78.96",
itemIds = {
630570
},
suitId = 70318
},
[61586] = {
commodityId = 61586,
commodityName = "咔咔大盗",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630571
}
},
[61587] = {
commodityId = 61587,
commodityName = "咔咔大盗",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630572
}
},
[61588] = {
commodityId = 61588,
commodityName = "牛马发箍",
beginTime = {
seconds = 1743782400
},
endTime = {
seconds = 1746374400
},
shopTag = v8,
jumpId = 598,
jumpText = "翡光仙灵",
minVersion = "1.3.78.72",
itemIds = {
630573
},
bOpenSuit = true,
suitId = 70319
},
[61589] = {
commodityId = 61589,
commodityName = "牛马发箍",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630574
}
},
[61590] = {
commodityId = 61590,
commodityName = "牛马发箍",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630575
}
},
[61591] = {
commodityId = 61591,
commodityName = "晴空朵朵",
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749052800
},
shopTag = v8,
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.68.98",
itemIds = {
630576
},
suitId = 70320
},
[61592] = {
commodityId = 61592,
commodityName = "晴空朵朵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630577
}
},
[61593] = {
commodityId = 61593,
commodityName = "晴空朵朵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630578
}
},
[61594] = {
commodityId = 61594,
commodityName = "甜心小兔",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630579
},
suitId = 70321
},
[61595] = {
commodityId = 61595,
commodityName = "星冕之核",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630580
},
suitId = 70322
},
[61596] = {
commodityId = 61596,
commodityName = "星冕之核",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630581
}
},
[61597] = {
commodityId = 61597,
commodityName = "星冕之核",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630582
}
},
[61598] = {
commodityId = 61598,
commodityName = "土豆芽芽乐",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630583
},
suitId = 70323
},
[61599] = {
commodityId = 61599,
commodityName = "土豆芽芽乐",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630584
}
},
[61600] = {
commodityId = 61600,
commodityName = "土豆芽芽乐",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630585
}
},
[61601] = {
commodityId = 61601,
commodityName = "毛绒恶魔",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630586
},
suitId = 70324
},
[61602] = {
commodityId = 61602,
commodityName = "毛绒恶魔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630587
}
},
[61603] = {
commodityId = 61603,
commodityName = "毛绒恶魔",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630588
}
},
[61604] = {
commodityId = 61604,
commodityName = "甜心刨冰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630589
},
suitId = 70325
},
[61605] = {
commodityId = 61605,
commodityName = "开花小绿瓜",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630590
},
suitId = 70326
},
[61606] = {
commodityId = 61606,
commodityName = "音浪符号",
beginTime = {
seconds = 1746720000
},
endTime = {
seconds = 1748793599
},
shopTag = v8,
jumpId = 672,
jumpText = "幸运大翻牌",
minVersion = "1.3.88.1",
itemIds = {
630591
},
suitId = 70327
},
[61607] = {
commodityId = 61607,
commodityName = "逢考必胜",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630592
},
suitId = 70328
},
[61608] = {
commodityId = 61608,
commodityName = "永冻蔷薇",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v8,
shopSort = 1,
jumpId = 10718,
jumpText = "峡谷幻梦",
minVersion = "1.3.78.80",
itemIds = {
630522
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70294
},
[61609] = {
commodityId = 61609,
commodityName = "月光精灵",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v8,
shopSort = 1,
jumpId = 10718,
jumpText = "峡谷幻梦",
minVersion = "1.3.78.80",
itemIds = {
630295
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 70169
},
[61610] = {
commodityId = 61610,
commodityName = "晚安云朵帽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630593
},
suitId = 70329
},
[61611] = {
commodityId = 61611,
commodityName = "礼盒灰灰",
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1749743999
},
shopTag = v8,
jumpId = 659,
jumpText = "满杯蜜桃猫",
itemIds = {
630594
},
suitId = 70330
},
[61612] = {
commodityId = 61612,
commodityName = "甜蜜奶茶",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630595
},
suitId = 70331
},
[61613] = {
commodityId = 61613,
commodityName = "海风草帽",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630596
},
suitId = 70332
},
[61614] = {
commodityId = 61614,
commodityName = "海风草帽",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630597
}
},
[61615] = {
commodityId = 61615,
commodityName = "海风草帽",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630598
}
},
[61616] = {
commodityId = 61616,
commodityName = "星月王冠",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630599
},
suitId = 70333
},
[61617] = {
commodityId = 61617,
commodityName = "狼君金冠",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630600
},
suitId = 70334
},
[61618] = {
commodityId = 61618,
commodityName = "永恒银冠",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630601
},
suitId = 70335
},
[61619] = {
commodityId = 61619,
commodityName = "嗷呜金币",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630602
},
suitId = 70336
},
[61620] = {
commodityId = 61620,
commodityName = "爆爆弹",
beginTime = {
seconds = 1747411200
},
endTime = {
seconds = 1750003199
},
shopTag = v8,
jumpId = 683,
jumpText = "狼人太空战",
minVersion = "1.3.88.50",
itemIds = {
630603
},
suitId = 70337
},
[61621] = {
commodityId = 61621,
commodityName = "赛博天眼",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630604
},
suitId = 70338
},
[61622] = {
commodityId = 61622,
commodityName = "元梦喵喵节",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630605
},
suitId = 70339
},
[61623] = {
commodityId = 61623,
commodityName = "颜料球精灵",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v8,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
630606
},
bOpenSuit = true,
suitId = 70340
},
[61624] = {
commodityId = 61624,
commodityName = "颜料球精灵",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630607
}
},
[61625] = {
commodityId = 61625,
commodityName = "颜料球精灵",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630608
}
},
[61626] = {
commodityId = 61626,
commodityName = "郁郁花香",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630609
},
suitId = 70341
},
[61627] = {
commodityId = 61627,
commodityName = "郁郁花香",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630610
}
},
[61628] = {
commodityId = 61628,
commodityName = "郁郁花香",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630611
}
},
[61629] = {
commodityId = 61629,
commodityName = "樱樱清晨",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630612
},
suitId = 70342
},
[61630] = {
commodityId = 61630,
commodityName = "樱樱清晨",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630613
}
},
[61631] = {
commodityId = 61631,
commodityName = "樱樱清晨",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630614
}
},
[61632] = {
commodityId = 61632,
commodityName = "怪异发型",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630615
},
suitId = 70343
},
[61633] = {
commodityId = 61633,
commodityName = "怪异发型",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630616
}
},
[61634] = {
commodityId = 61634,
commodityName = "怪异发型",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630617
}
},
[61635] = {
commodityId = 61635,
commodityName = "野蛮风格头饰",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630618
},
suitId = 70344
},
[61636] = {
commodityId = 61636,
commodityName = "野蛮风格头饰",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630619
}
},
[61637] = {
commodityId = 61637,
commodityName = "野蛮风格头饰",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630620
}
},
[61638] = {
commodityId = 61638,
commodityName = "“粽”心如意",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630621
},
suitId = 70345
},
[61639] = {
commodityId = 61639,
commodityName = "星宝蛋糕",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630622
},
suitId = 70346
},
[61640] = {
commodityId = 61640,
commodityName = "大明星",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630623
},
suitId = 70347
},
[61641] = {
commodityId = 61641,
commodityName = "黄金松果",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630624
},
suitId = 70348
},
[61642] = {
commodityId = 61642,
commodityName = "开心万岁",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630625
},
suitId = 70349
},
[61643] = {
commodityId = 61643,
commodityName = "星河之果",
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1750867200
},
shopTag = v8,
jumpId = 690,
jumpText = "旅者驿站",
minVersion = "1.3.88.112",
itemIds = {
630626
},
bOpenSuit = true,
suitId = 70350
},
[61644] = {
commodityId = 61644,
commodityName = "星河之果",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630627
}
},
[61645] = {
commodityId = 61645,
commodityName = "星河之果",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630628
}
},
[61646] = {
commodityId = 61646,
commodityName = "游鱼遗尾",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v8,
jumpId = 694,
jumpText = "幻猫音境",
itemIds = {
630629
},
bOpenSuit = true,
suitId = 70351
},
[61647] = {
commodityId = 61647,
commodityName = "游鱼遗尾",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630630
}
},
[61648] = {
commodityId = 61648,
commodityName = "游鱼遗尾",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630631
}
},
[61649] = {
commodityId = 61649,
commodityName = "雾隐重峦",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630632
},
suitId = 70352
},
[61650] = {
commodityId = 61650,
commodityName = "雾隐重峦",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630633
}
},
[61651] = {
commodityId = 61651,
commodityName = "雾隐重峦",
coinType = 200008,
price = 10,
beginTime = v6,
itemIds = {
630634
}
},
[61652] = {
commodityId = 61652,
commodityName = "匠心萌动",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630635
},
suitId = 70353
},
[61653] = {
commodityId = 61653,
commodityName = "匠心萌动",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630636
}
},
[61654] = {
commodityId = 61654,
commodityName = "匠心萌动",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630637
}
},
[61655] = {
commodityId = 61655,
commodityName = "半熟章丸丸",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630638
},
suitId = 70354
},
[61656] = {
commodityId = 61656,
commodityName = "半熟章丸丸",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630639
}
},
[61657] = {
commodityId = 61657,
commodityName = "半熟章丸丸",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630640
}
},
[61658] = {
commodityId = 61658,
commodityName = "萌喵之视",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630641
},
suitId = 70355
},
[61659] = {
commodityId = 61659,
commodityName = "萌喵之视",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630642
}
},
[61660] = {
commodityId = 61660,
commodityName = "萌喵之视",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630643
}
},
[61661] = {
commodityId = 61661,
commodityName = "轮回箭头",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630644
},
suitId = 70356
},
[61662] = {
commodityId = 61662,
commodityName = "香暖便当",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630645
},
suitId = 70357
},
[61663] = {
commodityId = 61663,
commodityName = "扬帆小船",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630646
},
suitId = 70358
},
[61664] = {
commodityId = 61664,
commodityName = "头顶机灵",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630647
},
suitId = 70359
},
[61665] = {
commodityId = 61665,
commodityName = "跃动喵喵",
beginTime = {
seconds = 1747584000
},
endTime = {
seconds = 1753372799
},
shopTag = v8,
jumpId = 900,
jumpText = "甜心琪琪",
itemIds = {
630650
},
suitId = 70362
},
[61666] = {
commodityId = 61666,
commodityName = "跃动喵喵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630651
}
},
[61667] = {
commodityId = 61667,
commodityName = "跃动喵喵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630652
}
},
[61668] = {
commodityId = 61668,
commodityName = "乌云绵绵",
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1752767999
},
shopTag = v8,
jumpId = 691,
jumpText = "怒海狂鲨",
itemIds = {
630653
},
suitId = 70363
},
[61669] = {
commodityId = 61669,
commodityName = "乌云绵绵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630654
}
},
[61670] = {
commodityId = 61670,
commodityName = "乌云绵绵",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630655
}
},
[61671] = {
commodityId = 61671,
commodityName = "囧囧先生",
beginTime = {
seconds = 1747584000
},
endTime = {
seconds = 1753372799
},
shopTag = v8,
jumpId = 900,
jumpText = "甜心琪琪",
itemIds = {
630656
},
suitId = 70364
},
[61672] = {
commodityId = 61672,
commodityName = "囧囧先生",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630657
}
},
[61673] = {
commodityId = 61673,
commodityName = "囧囧先生",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630658
}
},
[61674] = {
commodityId = 61674,
commodityName = "蛇小帕",
coinType = 6,
price = 10000,
endTime = v7,
shopTag = v8,
itemIds = {
630659
},
suitId = 70365
},
[61675] = {
commodityId = 61675,
commodityName = "蛇小帕",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630660
}
},
[61676] = {
commodityId = 61676,
commodityName = "蛇小帕",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630661
}
},
[61677] = {
commodityId = 61677,
commodityName = "心心蟹",
beginTime = {
seconds = 1748448000
},
endTime = {
seconds = 1753372799
},
shopTag = v8,
jumpId = 720,
jumpText = "炽光海岸",
minVersion = "1.3.88.112",
itemIds = {
630662
},
bOpenSuit = true,
suitId = 70366
},
[61678] = {
commodityId = 61678,
commodityName = "心心蟹",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630663
}
},
[61679] = {
commodityId = 61679,
commodityName = "心心蟹",
coinType = 200008,
price = 5,
beginTime = v6,
itemIds = {
630664
}
}
}

local mt = {
mallId = 11,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074768000
},
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data