--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 400
}
}

local v1 = 2

local v2 = {
seconds = 1706198400
}

local v3 = 10

local data = {
[400790] = {
id = 400790,
effect = true,
name = "梦奇",
desc = "我会把不开心都吃掉！",
icon = "CDN:Icon_BU_032",
getWay = "和梦奇玩耍",
jumpId = {
50
},
resourceConf = {
model = "SK_BU_032",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_032_Physics"
},
shareTexts = {
"万物皆可吃！"
},
beginTime = {
seconds = 1703174400
},
suitId = 58,
suitName = "梦奇",
suitIcon = "CDN:Icon_BU_032"
},
[400800] = {
id = 400800,
effect = true,
name = "时之奇旅  妲己",
desc = "不用躲藏，每个人都可以在人群中闪闪发光",
icon = "CDN:Icon_BU_030",
resourceConf = {
model = "SK_BU_030",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_030_Physics"
},
shareTexts = {
"用真心说再见，就会真的再见面"
},
beginTime = v2,
suitId = 132,
suitName = "时之奇旅  妲己",
suitIcon = "CDN:Icon_BU_030"
},
[400810] = {
id = 400810,
effect = true,
name = "缤纷奶糖",
desc = "陪你度过甜蜜时光！",
icon = "CDN:Icon_BU_026",
resourceConf = {
model = "SK_BU_026",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_026_Physics"
},
shareTexts = {
"我的糖果分你一半"
},
beginTime = {
seconds = 4101552000
},
suitId = 920,
suitName = "缤纷奶糖",
suitIcon = "CDN:Icon_BU_026"
},
[400811] = {
id = 400811,
effect = true,
name = "缤纷奶糖",
desc = "陪你度过甜蜜时光！",
icon = "CDN:Icon_BU_026_01",
outlookConf = {
belongTo = 400810,
fashionValue = 25,
belongToGroup = {
400811
}
},
resourceConf = {
model = "SK_BU_026",
material = "MI_BU_026_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_026_Physics",
materialSlot = "Skin"
},
commodityId = 10126,
shareTexts = {
"我的糖果分你一半"
}
},
[400820] = {
id = 400820,
effect = true,
name = "珍饺饺",
desc = "只要我吃得够圆，就没人能把我看扁",
icon = "CDN:Icon_BU_045",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SK_BU_045",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_045_Physics"
},
shareTexts = {
"我喜欢新鲜出炉的朝气"
},
beginTime = {
seconds = 1707408000
},
suitId = 119,
suitName = "珍饺饺",
suitIcon = "CDN:Icon_BU_045"
},
[400821] = {
id = 400821,
effect = true,
name = "珍饺饺",
desc = "只要我吃得够圆，就没人能把我看扁",
icon = "CDN:Icon_BU_045_01",
outlookConf = {
belongTo = 400820,
fashionValue = 25,
belongToGroup = {
400821
}
},
resourceConf = {
model = "SK_BU_045",
material = "MI_BU_045_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_045_Physics",
materialSlot = "Skin"
},
commodityId = 10128,
shareTexts = {
"我喜欢新鲜出炉的朝气"
}
},
[400830] = {
id = 400830,
effect = true,
name = "羊彬彬",
desc = "文质彬彬，绅士本色",
icon = "CDN:Icon_BU_028",
getWay = "赛季兑换",
jumpId = {
16
},
resourceConf = {
model = "SK_BU_028",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"向外优雅，向内从容"
},
beginTime = {
seconds = 1710432000
},
suitId = 157,
suitName = "羊彬彬",
suitIcon = "CDN:Icon_BU_028"
},
[400831] = {
id = 400831,
effect = true,
name = "羊彬彬",
desc = "文质彬彬，绅士本色",
icon = "CDN:Icon_BU_028_01",
outlookConf = {
belongTo = 400830,
fashionValue = 25,
belongToGroup = {
400831
}
},
resourceConf = {
model = "SK_BU_028",
material = "MI_BU_028_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 10130,
shareTexts = {
"向外优雅，向内从容"
}
},
[400840] = {
id = 400840,
effect = true,
name = "羊雅雅",
desc = "温文尔雅，淑女风范",
icon = "CDN:Icon_BU_029",
resourceConf = {
model = "SK_BU_029",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_029_Physics"
},
shareTexts = {
"休息的时候就要好好休息"
},
beginTime = {
seconds = 4101552000
},
suitId = 922,
suitName = "羊雅雅",
suitIcon = "CDN:Icon_BU_029"
},
[400841] = {
id = 400841,
effect = true,
name = "羊雅雅",
desc = "温文尔雅，淑女风范",
icon = "CDN:Icon_BU_029_01",
outlookConf = {
belongTo = 400840,
fashionValue = 25,
belongToGroup = {
400841
}
},
resourceConf = {
model = "SK_BU_029",
material = "MI_BU_029_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_029_Physics",
materialSlot = "Skin"
},
commodityId = 10132,
shareTexts = {
"休息的时候就要好好休息"
}
},
[400850] = {
id = 400850,
effect = true,
name = "荷小悦",
desc = "荷香四溢，悦动人心",
icon = "CDN:Icon_BU_049",
getWay = "桃源通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_049",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_049_Physics"
},
shareTexts = {
"荷风送香气，竹露滴清响"
},
beginTime = {
seconds = 1702569600
},
suitId = 53,
suitName = "荷小悦",
suitIcon = "CDN:Icon_BU_049"
},
[400851] = {
id = 400851,
effect = true,
name = "荷小悦",
desc = "荷香四溢，悦动人心",
icon = "CDN:Icon_BU_049_01",
outlookConf = {
belongTo = 400850,
fashionValue = 25,
belongToGroup = {
400851
}
},
resourceConf = {
model = "SK_BU_049",
material = "MI_BU_049_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_049_Physics",
materialSlot = "Skin"
},
commodityId = 10134,
shareTexts = {
"荷风送香气，竹露滴清响"
}
},
[400860] = {
id = 400860,
effect = true,
name = "跆拳道新星",
desc = "活力满满的一天开始啦!",
icon = "CDN:Icon_BU_033",
getWay = "薅鹅毛",
jumpId = {
114
},
resourceConf = {
model = "SK_BU_033",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_033_Physics"
},
shareTexts = {
"下一秒的我，会变得更强！"
},
beginTime = {
seconds = 1706846400
},
suitId = 109,
suitName = "跆拳道新星",
suitIcon = "CDN:Icon_BU_033"
},
[400861] = {
id = 400861,
effect = true,
name = "跆拳道新星",
desc = "活力满满的一天开始啦!",
icon = "CDN:Icon_BU_033_01",
outlookConf = {
belongTo = 400860,
fashionValue = 25,
belongToGroup = {
400861
}
},
resourceConf = {
model = "SK_BU_033",
material = "MI_BU_033_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_033_Physics",
materialSlot = "Skin"
},
commodityId = 10136,
shareTexts = {
"下一秒的我，会变得更强！"
}
},
[400870] = {
id = 400870,
effect = true,
name = "守护星",
desc = "用心呵护你的每一天",
icon = "CDN:Icon_BU_034",
getWay = "薅鹅毛",
jumpId = {
114
},
resourceConf = {
model = "SK_BU_034",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_034_Physics"
},
shareTexts = {
"治愈所有的不开心"
},
beginTime = v2,
suitId = 124,
suitName = "守护星",
suitIcon = "CDN:Icon_BU_034"
},
[400871] = {
id = 400871,
effect = true,
name = "守护星",
desc = "用心呵护你的每一天",
icon = "CDN:Icon_BU_034_01",
outlookConf = {
belongTo = 400870,
fashionValue = 25,
belongToGroup = {
400871
}
},
resourceConf = {
model = "SK_BU_034",
material = "MI_BU_034_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_034_Physics",
materialSlot = "Skin"
},
commodityId = 10138,
shareTexts = {
"治愈所有的不开心"
}
},
[400880] = {
id = 400880,
effect = true,
name = "魔力厨娘",
desc = "唯有美食与爱不可辜负",
icon = "CDN:Icon_BU_035",
getWay = "薅鹅毛",
jumpId = {
114
},
resourceConf = {
model = "SK_BU_035",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_035_Physics"
},
shareTexts = {
"好评不断，厨力满满！"
},
beginTime = {
seconds = 1706846400
},
suitId = 111,
suitName = "魔力厨娘",
suitIcon = "CDN:Icon_BU_035"
},
[400881] = {
id = 400881,
effect = true,
name = "魔力厨娘",
desc = "唯有美食与爱不可辜负",
icon = "CDN:Icon_BU_035_01",
outlookConf = {
belongTo = 400880,
fashionValue = 25,
belongToGroup = {
400881
}
},
resourceConf = {
model = "SK_BU_035",
material = "MI_BU_035_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_035_Physics",
materialSlot = "Skin"
},
commodityId = 10140,
shareTexts = {
"好评不断，厨力满满！"
}
},
[400890] = {
id = 400890,
effect = true,
name = "知识甜心",
desc = "追求梦想的旅程才刚刚开始",
icon = "CDN:Icon_BU_036",
getWay = "薅鹅毛",
jumpId = {
114
},
resourceConf = {
model = "SK_BU_036",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_036_Physics"
},
shareTexts = {
"请称我为行走的百科全书"
},
beginTime = {
seconds = 1706846400
},
suitId = 110,
suitName = "知识甜心",
suitIcon = "CDN:Icon_BU_036"
},
[400891] = {
id = 400891,
effect = true,
name = "知识甜心",
desc = "追求梦想的旅程才刚刚开始",
icon = "CDN:Icon_BU_036_01",
outlookConf = {
belongTo = 400890,
fashionValue = 25,
belongToGroup = {
400891
}
},
resourceConf = {
model = "SK_BU_036",
material = "MI_BU_036_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_036_Physics",
materialSlot = "Skin"
},
commodityId = 10142,
shareTexts = {
"请称我为行走的百科全书"
}
},
[400900] = {
id = 400900,
effect = true,
name = "蛙亚蛙",
desc = "生活不易，蛙蛙卖艺",
icon = "CDN:Icon_BU_040",
resourceConf = {
model = "SK_BU_040",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
beginTime = {
seconds = 4101552000
},
suitId = 924,
suitName = "蛙亚蛙",
suitIcon = "CDN:Icon_BU_040"
},
[400901] = {
id = 400901,
effect = true,
name = "蛙亚蛙",
desc = "生活不易，蛙蛙卖艺",
icon = "CDN:Icon_BU_040_01",
outlookConf = {
belongTo = 400900,
fashionValue = 25,
belongToGroup = {
400901
}
},
resourceConf = {
model = "SK_BU_040",
material = "MI_BU_040_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 10144
},
[400910] = {
id = 400910,
effect = true,
name = "蕉绿绿",
desc = "有一点点焦虑",
icon = "CDN:Icon_BU_046",
getWay = "蕉绿绿随机礼盒",
jumpId = {
25
},
resourceConf = {
model = "SK_BU_046",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_046_Physics"
},
shareTexts = {
"怎么办，谁能告诉我怎么办！"
},
beginTime = {
seconds = 1709308800
},
suitId = 140,
suitName = "蕉绿绿",
suitIcon = "CDN:Icon_BU_046"
},
[400911] = {
id = 400911,
effect = true,
name = "蕉绿绿",
desc = "有一点点焦虑",
icon = "CDN:Icon_BU_046_01",
outlookConf = {
belongTo = 400910,
fashionValue = 25,
belongToGroup = {
400911
}
},
resourceConf = {
model = "SK_BU_046",
material = "MI_BU_046_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_046_Physics",
materialSlot = "Skin"
},
commodityId = 10146,
shareTexts = {
"怎么办，谁能告诉我怎么办！"
}
},
[400920] = {
id = 400920,
effect = true,
name = "香嘟嘟",
desc = "你肿么了？",
icon = "CDN:Icon_BU_047",
getWay = "春季回馈",
jumpId = {
207
},
resourceConf = {
model = "SK_BU_047",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_047_Physics"
},
shareTexts = {
"从头到尾，都是真材实料！"
},
beginTime = {
seconds = 1709222400
},
suitId = 141,
suitName = "香嘟嘟",
suitIcon = "CDN:Icon_BU_047"
},
[400921] = {
id = 400921,
effect = true,
name = "香嘟嘟",
desc = "你肿么了？",
icon = "CDN:Icon_BU_047_01",
outlookConf = {
belongTo = 400920,
fashionValue = 25,
belongToGroup = {
400921
}
},
resourceConf = {
model = "SK_BU_047",
material = "MI_BU_047_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_047_Physics",
materialSlot = "Skin"
},
commodityId = 10148,
shareTexts = {
"从头到尾，都是真材实料！"
}
},
[400930] = {
id = 400930,
effect = true,
name = "龟蜜",
desc = "处个闺蜜？",
icon = "CDN:Icon_BU_048",
resourceConf = {
model = "SK_BU_048",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_048_Physics"
},
shareTexts = {
"拉过手指，要当一辈子的朋友"
},
beginTime = {
seconds = 1705075200
},
suitId = 64,
suitName = "龟蜜",
suitIcon = "CDN:Icon_BU_048"
},
[400931] = {
id = 400931,
effect = true,
name = "龟蜜",
desc = "处个闺蜜？",
icon = "CDN:Icon_BU_048_01",
outlookConf = {
belongTo = 400930,
fashionValue = 25,
belongToGroup = {
400931
}
},
resourceConf = {
model = "SK_BU_048",
material = "MI_BU_048_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_048_Physics",
materialSlot = "Skin"
},
commodityId = 10150,
shareTexts = {
"拉过手指，要当一辈子的朋友"
}
},
[400940] = {
id = 400940,
effect = true,
name = "特战小子",
desc = "我会是笑到最后的赢家",
icon = "CDN:Icon_BU_022",
resourceConf = {
model = "SK_BU_022",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
15,
10
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
beginTime = {
seconds = 4101552000
},
suitId = 926,
suitName = "特战小子",
suitIcon = "CDN:Icon_BU_022"
},
[400941] = {
id = 400941,
effect = true,
name = "特战小子",
desc = "我会是笑到最后的赢家",
icon = "CDN:Icon_BU_022_01",
outlookConf = {
belongTo = 400940,
fashionValue = 25,
belongToGroup = {
400941
}
},
resourceConf = {
model = "SK_BU_022",
material = "MI_BU_022_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
15,
15
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 10152
},
[400950] = {
id = 400950,
effect = true,
name = "荷小颜",
desc = "荷叶翩翩，清新展颜",
icon = "CDN:Icon_BU_050",
getWay = "桃源通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_050",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_050_Physics"
},
shareTexts = {
"荷香伴我读书"
},
beginTime = {
seconds = 1702569600
},
suitId = 54,
suitName = "荷小颜",
suitIcon = "CDN:Icon_BU_050"
},
[400951] = {
id = 400951,
effect = true,
name = "荷小颜",
desc = "荷叶翩翩，清新展颜",
icon = "CDN:Icon_BU_050_01",
outlookConf = {
belongTo = 400950,
fashionValue = 25,
belongToGroup = {
400951
}
},
resourceConf = {
model = "SK_BU_050",
material = "MI_BU_050_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_050_Physics",
materialSlot = "Skin"
},
commodityId = 10158,
shareTexts = {
"荷香伴我读书"
}
},
[400960] = {
id = 400960,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "气泡狗阿绿",
desc = "看上我了是不是？",
icon = "CDN:Icon_Body_021",
getWay = "星宝来了",
jumpId = {
42
},
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_021",
upper = "SK_Body_Upper_021",
bottom = "SK_Body_Under_021",
gloves = "SK_Body_Hands_021",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
beginTime = {
seconds = 1705593600
},
suitId = 135,
suitName = "气泡狗阿绿",
suitIcon = "CDN:Icon_Body_021"
},
[400970] = {
id = 400970,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "幸运鹅",
desc = "你的QQ好友已上线",
icon = "CDN:Icon_Body_022",
getWay = "星宝来了",
jumpId = {
42
},
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_022",
upper = "SK_Body_Upper_022",
bottom = "SK_Body_Under_022",
gloves = "SK_Body_Hands_022",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
beginTime = {
seconds = 1705593600
},
suitId = 136,
suitName = "幸运鹅",
suitIcon = "CDN:Icon_Body_022"
},
[400980] = {
id = 400980,
effect = true,
name = "薯星星",
desc = "薯薯出街！还有人没有我的同款吗？",
icon = "CDN:Icon_BU_062",
resourceConf = {
model = "SK_BU_062",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"今天你种草了吗？"
},
beginTime = {
seconds = 4101552000
},
suitId = 928,
suitName = "薯星星",
suitIcon = "CDN:Icon_BU_062"
},
[400990] = {
id = 400990,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "Human",
desc = "我不会被击倒，因为我永远都会站起来",
icon = "CDN:Icon_PL_038",
getWay = "砍价活动",
jumpId = {
12210
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_038",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_038_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_038",
outShow = "AS_CH_IdleShow_PL_038",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_038",
shareTexts = {
"人类永不一败涂地！"
},
shareAnim = "AS_CH_Pose_PL_038",
beginTime = {
seconds = 1730995200
},
timeToStartAnim = 0.5699999928474426,
suitId = 662,
suitName = "Human",
suitIcon = "CDN:Icon_PL_038"
},
[401000] = {
id = 401000,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "Toby",
desc = "除了卖萌我什么都不会",
icon = "CDN:Icon_PL_046",
getWay = "摩天乐园",
jumpId = {
158
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_046",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
15,
20
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_046",
outShow = "AS_CH_IdleShow_PL_046",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_046",
shareTexts = {
"只要我跑得够快，难过就追不上我",
"偶尔犯错也没有关系",
"凡事不要太纠结",
"对不起，今天只想当笨蛋",
"自我介绍一下，在下是赖床冠军"
},
shareAnim = "AS_CH_Pose_001_PL_046",
beginTime = {
seconds = 1704988800
},
timeToStartAnim = 0.38999998569488525,
sharePic = "T_Share_Suit_401000.astc",
suitId = 61,
suitName = "Toby",
themedId = 8,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_046",
shareNamePic = "T_Share_Suit_name_401000.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc",
ThemedShowIdList = {
{
key = 8,
value = 2
}
}
},
[401010] = {
id = 401010,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "奶龙",
desc = "我的大肚肚，真的很酷酷",
icon = "CDN:Icon_PL_050",
getWay = "奶龙祈愿",
jumpId = {
176
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_050",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_050",
outShow = "AS_CH_IdleShow_PL_050",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_050",
shareTexts = {
"我可是地表最强的龙！",
"像人家这样的宝藏男孩，都被你发现啦~",
"我的爱都给你！",
"我就是最棒的！",
"我看着不胖，但是三碗吃不饱！"
},
shareAnim = "AS_CH_Pose_001_PL_050",
beginTime = {
seconds = 1706889600
},
sharePic = "T_Share_Suit_401010.astc",
suitId = 103,
suitName = "奶龙",
themedId = 9,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_050",
shareNamePic = "T_Share_Suit_name_401010.astc",
shareBubblePic = "T_Share_Suit_frame_401010.astc",
ThemedShowIdList = {
{
key = 9,
value = 2
}
}
},
[401020] = {
id = 401020,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = " 红鲤仙  锦瞳",
desc = "诚心诚意更容易被幸运眷顾哦！",
icon = "CDN:Icon_PL_035",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_035",
material = "MI_PL_035_1;MI_PL_035_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_035_Physics",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_035",
outShow = "AS_CH_IdleShow_PL_035",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_035_Prop",
outPropShow = "AS_CH_IdleShow_PL_035_Prop",
outPropSkeletal = "SK_PL_Prop_035",
outEnterSequence = "SQC_Enter_PL_035",
shareTexts = {
"今天的幸运儿，就决定是你了！",
"想要转运?默念我的名字吧！",
"你相信奇迹会发生吗？",
"摸摸鱼，心愿达成！",
"与其仰仗运气,不如做自己的锦鲤！"
},
shareAnim = "AS_CH_Pose_001_PL_035",
beginTime = v2,
suitStoryTextKey = [[锦鲤家族里掌管运气事务的神仙叫锦瞳，外表看似一个小女孩，实际上她已经活了上百岁。锦瞳施展法术的时候，会佩戴一枚锦鲤家族的标志性发卡，镶着一颗转运珠。锦瞳最爱红色,常常身着一袭红裙,裙边绣着锦鲤图案,摆动时如同鱼尾招摇,泛着灿烂的光泽，因此人称红鲤仙。

锦瞳性格十分顽皮，虽是个神仙却一点也不正经，常常躲在转运池旁逗前来转运的星宝玩,让众神仙头疼不已。每当有星宝在河边许下心愿, 她就会蹑手蹑脚地藏起来探听。如果锦瞳觉得愿望不够真诚，还会变出小鱼漾起水花捉弄许愿的星宝。不过要是真心真意的愿望,锦瞳还是很照顾的，会传递好运，暗中帮助许愿的星宝渡过难关。

她来去无踪，从不按常理出牌，让星宝们琢磨不透。但每次红鲤仙真身出现，都意味着好运即将降临。只要被红鲤仙选中的星宝，好运都会爆棚。每个星宝都梦想着有朝一日能见到红鲤仙锦瞳,沾沾她身上的好运气]],
suitId = 66,
suitName = " 红鲤仙 锦瞳",
bpShowId = 2,
seasonId = 2,
suitIcon = "CDN:Icon_PL_035",
SeasonShowIdList = {
{
key = 2,
value = 2
}
}
},
[401021] = {
id = 401021,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = " 红鲤仙  锦瞳",
desc = "诚心诚意更容易被幸运眷顾哦！",
icon = "CDN:Icon_PL_035_01",
outlookConf = {
belongTo = 401020,
fashionValue = 35,
belongToGroup = {
401021,
401022
}
},
resourceConf = {
model = "SK_PL_035",
material = "MI_PL_035_1_HP01;MI_PL_035_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_035_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11001,
outEnter = "AS_CH_Enter_PL_035",
outShow = "AS_CH_IdleShow_PL_035",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_035_Prop",
outPropShow = "AS_CH_IdleShow_PL_035_Prop",
outPropSkeletal = "SK_PL_Prop_035",
outEnterSequence = "SQC_Enter_PL_035",
shareTexts = {
"今天的幸运儿，就决定是你了！",
"想要转运?默念我的名字吧！",
"你相信奇迹会发生吗？",
"摸摸鱼，心愿达成！",
"与其仰仗运气,不如做自己的锦鲤！"
},
shareAnim = "AS_CH_Pose_001_PL_035"
},
[401022] = {
id = 401022,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = " 红鲤仙  锦瞳",
desc = "诚心诚意更容易被幸运眷顾哦！",
icon = "CDN:Icon_PL_035_02",
outlookConf = {
belongTo = 401020,
fashionValue = 35,
belongToGroup = {
401021,
401022
}
},
resourceConf = {
model = "SK_PL_035",
material = "MI_PL_035_1_HP02;MI_PL_035_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_035_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11002,
outEnter = "AS_CH_Enter_PL_035",
outShow = "AS_CH_IdleShow_PL_035",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_035_Prop",
outPropShow = "AS_CH_IdleShow_PL_035_Prop",
outPropSkeletal = "SK_PL_Prop_035",
outEnterSequence = "SQC_Enter_PL_035",
shareTexts = {
"今天的幸运儿，就决定是你了！",
"想要转运?默念我的名字吧！",
"你相信奇迹会发生吗？",
"摸摸鱼，心愿达成！",
"与其仰仗运气,不如做自己的锦鲤！"
},
shareAnim = "AS_CH_Pose_001_PL_035"
},
[401030] = {
id = 401030,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "迪迦奥特曼",
desc = "终有一日，你也能变成光",
icon = "CDN:Icon_PL_036",
getWay = "奥特曼祈愿",
jumpId = {
179
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_036",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_036",
outShow = "AS_CH_IdleShow_PL_036",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_036",
shareTexts = {
"梦想成真，不会遥远！",
"在三分钟内解决战斗！",
"我是光之巨人，迪迦奥特曼！",
"信念之光为我照亮前路！",
"和我一起变成光吧"
},
shareAnim = "AS_CH_Pose_001_PL_036",
beginTime = {
seconds = 1706803200
},
suitId = 67,
suitName = "奥特曼-迪迦",
themedId = 7,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_036",
ThemedShowIdList = {
{
key = 7,
value = 3
}
}
},
[401040] = {
id = 401040,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔法师 哈奇",
desc = "猫猫拯救世界！",
icon = "CDN:Icon_PL_037",
getWay = "月夜歌吟",
jumpId = {
79
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_037",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_037_Physics"
},
outEnter = "AS_CH_Enter_PL_037",
outShow = "AS_CH_IdleShow_PL_037",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_037_Prop",
outPropShow = "AS_CH_IdleShow_PL_037_Prop",
outPropSkeletal = "SK_PL_Prop_037",
outEnterSequence = "SQC_Enter_PL_037",
shareTexts = {
"魔法怎么失灵啦？",
"谁能不爱魔法小猫呢？",
"我才不是流泪猫猫头！",
"有魔力更有魅力！",
"画个魔法圈圈送给你呀！"
},
shareAnim = "AS_CH_Pose_001_PL_037;AS_CH_Pose_001_PL_037_Porp",
beginTime = {
seconds = 1706803200
},
suitStoryTextKey = [[哈奇原本是一只好奇心十足的流浪猫，常常蹲在魔法师梅林家的窗户上偷看梅林施展魔法。有一天梅林不在家，哈奇悄悄地走到魔法书面前，小心翼翼地读着咒语。可谁知咒语竟然灵验了，霎那间火花四溅,一股强大的力量将他吞噬。等他醒来的时候,体内充满了神秘的元素力量,身上还多了一颗魔法铃铛。

从那以后，哈奇每天都会翻阅魔法书中的内容，并努力将所学应用于实践。有时候，哈奇也会为了解开某个魔法难题而整夜苦思冥想，眼前总是浮现出五光十色的魔法符文。

通过不断地魔法实践，哈奇酿造出了独一无二的魔法蜜瓶，装着甜甜的魔力蜂蜜，这也成为了哈奇的最爱。每当哈奇需要增加魔法威力时，他会小心翼翼地舔上一小勺蜂蜜，仿佛充满了魔法力量。]],
suitId = 68,
suitName = "魔法师 哈奇",
suitIcon = "CDN:Icon_PL_037"
},
[401041] = {
id = 401041,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔法师 哈奇",
desc = "猫猫拯救世界！",
icon = "CDN:Icon_PL_037_01",
outlookConf = {
belongTo = 401040,
fashionValue = 35,
belongToGroup = {
401041,
401042
}
},
resourceConf = {
model = "SK_PL_037",
material = "MI_PL_037_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_037_Physics",
materialSlot = "Skin"
},
commodityId = 11005,
outEnter = "AS_CH_Enter_PL_037",
outShow = "AS_CH_IdleShow_PL_037",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_037_Prop",
outPropShow = "AS_CH_IdleShow_PL_037_Prop",
outPropSkeletal = "SK_PL_Prop_037",
outEnterSequence = "SQC_Enter_PL_037",
shareTexts = {
"魔法怎么失灵啦？",
"谁能不爱魔法小猫呢？",
"我才不是流泪猫猫头！",
"有魔力更有魅力！",
"画个魔法圈圈送给你呀！"
},
shareAnim = "AS_CH_Pose_001_PL_037;AS_CH_Pose_001_PL_037_Porp"
},
[401042] = {
id = 401042,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔法师 哈奇",
desc = "猫猫拯救世界！",
icon = "CDN:Icon_PL_037_02",
outlookConf = {
belongTo = 401040,
fashionValue = 35,
belongToGroup = {
401041,
401042
}
},
resourceConf = {
model = "SK_PL_037",
material = "MI_PL_037_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_037_Physics",
materialSlot = "Skin"
},
commodityId = 11006,
outEnter = "AS_CH_Enter_PL_037",
outShow = "AS_CH_IdleShow_PL_037",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_037_Prop",
outPropShow = "AS_CH_IdleShow_PL_037_Prop",
outPropSkeletal = "SK_PL_Prop_037",
outEnterSequence = "SQC_Enter_PL_037",
shareTexts = {
"魔法怎么失灵啦？",
"谁能不爱魔法小猫呢？",
"我才不是流泪猫猫头！",
"有魔力更有魅力！",
"画个魔法圈圈送给你呀！"
},
shareAnim = "AS_CH_Pose_001_PL_037;AS_CH_Pose_001_PL_037_Porp"
},
[401050] = {
id = 401050,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "虎啸龙 毕小烈",
desc = "如果有任何危险，我会冲在前面",
icon = "CDN:Icon_PL_041",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_041",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_041_Physics"
},
outEnter = "AS_CH_Enter_PL_041",
outShow = "AS_CH_IdleShow_PL_041",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_PL_Prop_041",
outPropSkeletal = "SK_PL_Prop_041",
outEnterSequence = "SQC_Enter_PL_041",
shareTexts = {
"你尽管往前走，我会为你守候",
"龙行虎步，勇往直前！",
"厄运看到我，必将退散",
"虎啸山林，龙威四方！",
"我愿意做你最强大的后盾"
},
shareAnim = "AS_CH_Pose_001_PL_041",
beginTime = v2,
suitStoryTextKey = [[黑潭龙王第七个儿子名叫毕小烈，小烈生来就与众不同，尽管他生而为龙,面庞上却拥有老虎的纹络及一条大长尾巴。小烈天生具备非凡的智慧和正义感，他既有龙族的聪颖睿智，又像老虎一般威风凛凛，他能够明辨是非，并且总是心怀仗义之心，热衷于抱打不平。

小烈在幼年就展现出刚正不阿的品质。他对不公正的事情毫不容忍，总是不顾个人安危，勇敢地站出来为弱者争取正义。无论是在龙族中还是与其他灵族相处的过程中，小烈始终秉持着仗义执言的原则。他经常与那些欺凌弱小、做恶不悔的猛兽进行较量，保护弱者的权益。

随着年龄的增长，小烈主动加入了龙族护卫队，不仅守护龙族，也为世间百姓除恶扬善。小烈一身正义，对恶势力虎视眈眈。只要被小烈瞪一眼，大气都不敢出。

小烈用仁心对待每个星宝,用智慧破解险情。星宝们都称赞小烈是正义的化身,是万众的守护神。]],
suitId = 69,
suitName = "虎啸龙 毕小烈",
bpShowId = 5,
seasonId = 2,
suitIcon = "CDN:Icon_PL_041",
SeasonShowIdList = {
{
key = 2,
value = 5
}
}
},
[401051] = {
id = 401051,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "虎啸龙 毕小烈",
desc = "如果有任何危险，我会冲在前面",
icon = "CDN:Icon_PL_041_01",
outlookConf = {
belongTo = 401050,
fashionValue = 35,
belongToGroup = {
401051,
401052
}
},
resourceConf = {
model = "SK_PL_041",
material = "MI_PL_041_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_041_Physics",
materialSlot = "Skin"
},
commodityId = 11008,
outEnter = "AS_CH_Enter_PL_041",
outShow = "AS_CH_IdleShow_PL_041",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_PL_Prop_041",
outPropSkeletal = "SK_PL_Prop_041",
outEnterSequence = "SQC_Enter_PL_041",
shareTexts = {
"你尽管往前走，我会为你守候",
"龙行虎步，勇往直前！",
"厄运看到我，必将退散",
"虎啸山林，龙威四方！",
"我愿意做你最强大的后盾"
},
shareAnim = "AS_CH_Pose_001_PL_041"
},
[401052] = {
id = 401052,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "虎啸龙 毕小烈",
desc = "如果有任何危险，我会冲在前面",
icon = "CDN:Icon_PL_041_02",
outlookConf = {
belongTo = 401050,
fashionValue = 35,
belongToGroup = {
401051,
401052
}
},
resourceConf = {
model = "SK_PL_041",
material = "MI_PL_041_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_041_Physics",
materialSlot = "Skin"
},
commodityId = 11009,
outEnter = "AS_CH_Enter_PL_041",
outShow = "AS_CH_IdleShow_PL_041",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_PL_Prop_041",
outPropSkeletal = "SK_PL_Prop_041",
outEnterSequence = "SQC_Enter_PL_041",
shareTexts = {
"你尽管往前走，我会为你守候",
"龙行虎步，勇往直前！",
"厄运看到我，必将退散",
"虎啸山林，龙威四方！",
"我愿意做你最强大的后盾"
},
shareAnim = "AS_CH_Pose_001_PL_041"
},
[401060] = {
id = 401060,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "四叶草精灵  克洛洛",
desc = "我是幸运的化身",
icon = "CDN:Icon_PL_042",
getWay = "月夜歌吟",
jumpId = {
79
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_042",
material = "MI_PL_042_1;MI_PL_042_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_042_Physics",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_042",
outShow = "AS_CH_IdleShow_PL_042",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_Prop_042",
outPropShow = "AS_CH_IdleShow_PL_Prop_042",
outPropSkeletal = "SK_PL_Prop_042",
outEnterSequence = "SQC_Enter_PL_042",
shareTexts = {
"追上我，就送你一点小幸运！",
"四叶草的每片叶子，都藏着一个秘密哦！",
"想要送你没有保质期的幸福！",
"魔法四叶草，点亮你的希望！",
"奇迹即将发生！"
},
shareAnim = "AS_CH_Pose_001_PL_042;AS_CH_Pose_001_PL_042_Porp",
beginTime = {
seconds = 1706803200
},
suitStoryTextKey = [[
克洛洛是住在魔法森林深处的四叶草精灵,她身穿用四叶草编织的长裙,头上戴着一株巨大的幸运四叶草。克洛洛极其神秘,很少露出真身。想要遇见她必须拥有纯真善良的心,以及完全取决于克洛洛当天的心情。心情不好就谁也不见。心情好的时候，可能在哪都能遇到她。

传说在每个傍晚日落后的蓝调时刻,是能偶遇克洛洛的最佳时刻。这个时间段克洛洛会将自己的魔法四叶草点亮，吸收日落之前以及月亮初升之时的精华，同时也为日落后的森林小生物送去一点光照。克洛洛的四叶草魔法威力无穷，可以制造一种幻境，每个星宝都能在幻境中见到最想要的东西。克洛洛会让伤心的星宝重现微笑,为迷路的星宝指引方向。任何遇到克洛洛的生灵，都会感到内心被治愈,重拾幸福和勇气。

克洛洛用四叶草的魔法传递希望与喜乐,星宝们都期盼能遇见这个可遇不可求的四叶草精灵,得到她送出的永恒幸福。]],
suitId = 70,
suitName = "四叶草精灵  克洛洛",
suitIcon = "CDN:Icon_PL_042"
},
[401061] = {
id = 401061,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "四叶草精灵  克洛洛",
desc = "我是幸运的化身",
icon = "CDN:Icon_PL_042_01",
outlookConf = {
belongTo = 401060,
fashionValue = 35,
belongToGroup = {
401061,
401062
}
},
resourceConf = {
model = "SK_PL_042",
material = "MI_PL_042_HP01_1;MI_PL_042_HP01_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_042_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11011,
outEnter = "AS_CH_Enter_PL_042",
outShow = "AS_CH_IdleShow_PL_042",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_Prop_042",
outPropShow = "AS_CH_IdleShow_PL_Prop_042",
outPropSkeletal = "SK_PL_Prop_042",
outEnterSequence = "SQC_Enter_PL_042",
shareTexts = {
"追上我，就送你一点小幸运！",
"四叶草的每片叶子，都藏着一个秘密哦！",
"想要送你没有保质期的幸福！",
"魔法四叶草，点亮你的希望！",
"奇迹即将发生！"
},
shareAnim = "AS_CH_Pose_001_PL_042;AS_CH_Pose_001_PL_042_Porp"
},
[401062] = {
id = 401062,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "四叶草精灵  克洛洛",
desc = "我是幸运的化身",
icon = "CDN:Icon_PL_042_02",
outlookConf = {
belongTo = 401060,
fashionValue = 35,
belongToGroup = {
401061,
401062
}
},
resourceConf = {
model = "SK_PL_042",
material = "MI_PL_042_HP02_1;MI_PL_042_HP02_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_042_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11012,
outEnter = "AS_CH_Enter_PL_042",
outShow = "AS_CH_IdleShow_PL_042",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_Prop_042",
outPropShow = "AS_CH_IdleShow_PL_Prop_042",
outPropSkeletal = "SK_PL_Prop_042",
outEnterSequence = "SQC_Enter_PL_042",
shareTexts = {
"追上我，就送你一点小幸运！",
"四叶草的每片叶子，都藏着一个秘密哦！",
"想要送你没有保质期的幸福！",
"魔法四叶草，点亮你的希望！",
"奇迹即将发生！"
},
shareAnim = "AS_CH_Pose_001_PL_042;AS_CH_Pose_001_PL_042_Porp"
},
[401070] = {
id = 401070,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "雪小熊  雪诺比",
desc = "白雪外表下，是温暖的内心",
icon = "CDN:Icon_PL_044",
getWay = "山海通行证",
jumpId = {
9
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_044",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_044_Physics"
},
outEnter = "AS_CH_Enter_PL_044",
outShow = "AS_CH_IdleShow_PL_044",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_044",
shareTexts = {
"接下来去哪里堆雪人呢？",
"我是你的专属小火炉",
"轻轻柔柔，可可爱爱",
"玩乐时光，永远不要结束",
"天气冷了，要注意保暖！"
},
shareAnim = "AS_CH_Pose_001_PL_044",
beginTime = v2,
suitStoryTextKey = [[雪诺比原本是一只玩具熊，是巫师家族小主人最珍爱的宝物。一个冬日，小主人带着他在雪地游玩，却迷失了方向。逐渐失力的小主人再也抱不紧心爱的玩偶，随着玩偶陷在雪中，小主人的意识也渐渐消失。就在此时，斯诺比对主人的守护之心激活了巫师血脉里的力量——他正式有了生命。雪诺比身上盖着拖行时的皑皑白雪，但他对小主人的关爱让雪变成了能保暖的棉花。他站起来，给予小主人温暖，带他离开危险。

雪诺比自此成为了一只别样的“雪人”。他穿上了小主人给的冬装，和一代又一代的孩子们玩耍。只要玩偶与主人的关爱流淌在彼此之间，雪诺比就不会变得寒冷。]],
suitId = 71,
suitName = "雪小熊  雪诺比",
suitIcon = "CDN:Icon_PL_044"
},
[401071] = {
id = 401071,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "雪小熊  雪诺比",
desc = "白雪外表下，是温暖的内心",
icon = "CDN:Icon_PL_044_01",
outlookConf = {
belongTo = 401070,
fashionValue = 35,
belongToGroup = {
401071,
401072
}
},
resourceConf = {
model = "SK_PL_044",
material = "MI_PL_044_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_044_Physics",
materialSlot = "Skin"
},
commodityId = 11014,
outEnter = "AS_CH_Enter_PL_044",
outShow = "AS_CH_IdleShow_PL_044",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_044",
shareTexts = {
"接下来去哪里堆雪人呢？",
"我是你的专属小火炉",
"轻轻柔柔，可可爱爱",
"玩乐时光，永远不要结束",
"天气冷了，要注意保暖！"
},
shareAnim = "AS_CH_Pose_001_PL_044"
},
[401072] = {
id = 401072,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "雪小熊  雪诺比",
desc = "白雪外表下，是温暖的内心",
icon = "CDN:Icon_PL_044_02",
outlookConf = {
belongTo = 401070,
fashionValue = 35,
belongToGroup = {
401071,
401072
}
},
resourceConf = {
model = "SK_PL_044",
material = "MI_PL_044_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_044_Physics",
materialSlot = "Skin"
},
commodityId = 11015,
outEnter = "AS_CH_Enter_PL_044",
outShow = "AS_CH_IdleShow_PL_044",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_044",
shareTexts = {
"接下来去哪里堆雪人呢？",
"我是你的专属小火炉",
"轻轻柔柔，可可爱爱",
"玩乐时光，永远不要结束",
"天气冷了，要注意保暖！"
},
shareAnim = "AS_CH_Pose_001_PL_044"
},
[401080] = {
id = 401080,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小龙人 辰儿",
desc = "萌龙献瑞，福星高照",
icon = "CDN:Icon_PL_045",
getWay = "福星手账簿",
jumpId = {
143
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_045",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_045_Physics"
},
outEnter = "AS_CH_Enter_PL_045",
outShow = "AS_CH_IdleShow_PL_045",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_045_Prop",
outPropSkeletal = "SK_PL_Prop_045",
outEnterSequence = "SQC_Enter_PL_045",
shareTexts = {
"万事兴“龙”，“辰”风破浪",
"跟着我，就能找到好运！",
"让祥瑞伴你左右！",
"蛟龙入海，大有可为",
"龙翔万里，万象启新"
},
shareAnim = "AS_CH_Pose_001_PL_045;AS_CH_Pose_001_PL_045_Porp",
beginTime = v2,
suitStoryTextKey = [[东海里新得了一位小龙，他生在良辰吉日，诞生时海面无风无浪，一道彩虹跃然天际，皆是祥瑞之兆，故被称作“辰儿”。海里的人都说，等辰儿长大了，会成为东海的下一任守护神，惠泽四方。

于是辰儿从小活在源源不断的爱意中。饿了，便有新鲜出炉的糕点；乏了，便有琳琅满目的宝贝。辰儿生性纯良，这样的关心让他意识到自己不能辜负大家的期待。他努力精进自己的灵力，就是想早日回应这一份心意。

不过辰儿的悟性却稍稍拖了后腿。当他想略显身手帮助他人时，辰儿总是无法掌握自己的“洪荒之力”。但别担心，祥瑞所经之处必然万事无虞——因为辰儿会把所有的烂摊子收拾干净。热闹顺遂的每一天便是辰儿的生活，之后的功法，再找师傅请教吧。]],
suitId = 72,
suitName = "小龙人 辰儿",
suitIcon = "CDN:Icon_PL_045"
},
[401081] = {
id = 401081,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小龙人 辰儿",
desc = "萌龙献瑞，福星高照",
icon = "CDN:Icon_PL_045_01",
outlookConf = {
belongTo = 401080,
fashionValue = 35,
belongToGroup = {
401081,
401082
}
},
resourceConf = {
model = "SK_PL_045",
material = "MI_PL_045_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_045_Physics",
materialSlot = "Skin"
},
commodityId = 11017,
outEnter = "AS_CH_Enter_PL_045",
outShow = "AS_CH_IdleShow_PL_045",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_045_Prop",
outPropSkeletal = "SK_PL_Prop_045",
outEnterSequence = "SQC_Enter_PL_045",
shareTexts = {
"万事兴“龙”，“辰”风破浪",
"跟着我，就能找到好运！",
"让祥瑞伴你左右！",
"蛟龙入海，大有可为",
"龙翔万里，万象启新"
},
shareAnim = "AS_CH_Pose_001_PL_045;AS_CH_Pose_001_PL_045_Porp"
},
[401082] = {
id = 401082,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小龙人 辰儿",
desc = "萌龙献瑞，福星高照",
icon = "CDN:Icon_PL_045_02",
outlookConf = {
belongTo = 401080,
fashionValue = 35,
belongToGroup = {
401081,
401082
}
},
resourceConf = {
model = "SK_PL_045",
material = "MI_PL_045_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_045_Physics",
materialSlot = "Skin"
},
commodityId = 11018,
outEnter = "AS_CH_Enter_PL_045",
outShow = "AS_CH_IdleShow_PL_045",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_045_Prop",
outPropSkeletal = "SK_PL_Prop_045",
outEnterSequence = "SQC_Enter_PL_045",
shareTexts = {
"万事兴“龙”，“辰”风破浪",
"跟着我，就能找到好运！",
"让祥瑞伴你左右！",
"蛟龙入海，大有可为",
"龙翔万里，万象启新"
},
shareAnim = "AS_CH_Pose_001_PL_045;AS_CH_Pose_001_PL_045_Porp"
},
[401090] = {
id = 401090,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "未来之星 欧米",
desc = "正在加载过关秘籍……",
icon = "CDN:Icon_PL_047",
getWay = "幸运祈愿",
jumpId = {
188
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_047",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outEnter = "AS_CH_Enter_PL_047",
outShow = "AS_CH_IdleShow_PL_047",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_047_Prop",
outPropShow = "AS_CH_IdleShow_PL_047_Prop",
outPropSkeletal = "SK_PL_Prop_047",
outEnterSequence = "SQC_Enter_PL_047",
shareTexts = {
"欢乐组件，加载完毕",
"我的热情可以点燃引擎",
"派对很热闹，也带上我一起去好吗？",
"定期保养的时间到了",
"欢迎发掘我的新功能"
},
shareAnim = "AS_CH_Pose_001_PL_047",
beginTime = {
seconds = 1709568000
},
suitStoryTextKey = [[欧米是新星博士的第一个作品。博士的专攻武装机械，目标是制造出首个有自我进攻意识的机器人。几经挫折后，欧米诞生了，可他不像预料中冷酷无情，反而热情体贴。博士绞尽脑汁，无论怎么调适，都无法改变欧米的性格。

这段时间内，欧米一直陪伴在博士身旁。当他熬夜做研究时，欧米总会为他热上一杯咖啡；在他刚想找一份资料时，欧米早已拿着目标文件在旁等候。久而久之，博士再也离不开如此聪明又体贴的欧米，放弃了改造它的念头。

直到有一天，博士遭遇危险，欧米在情急之下使用了武器，博士才了解到欧米的心情——他并不希望被当作只会打斗的机器。自此，欧米再也没有参加过任何实验，它可以自由自在地待在博士身边，像一个孩子一样做着自己喜欢的事。]],
suitId = 73,
suitName = "未来之星 欧米",
suitIcon = "CDN:Icon_PL_047"
},
[401091] = {
id = 401091,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "未来之星 欧米",
desc = "正在加载过关秘籍……",
icon = "CDN:Icon_PL_047_01",
outlookConf = {
belongTo = 401090,
fashionValue = 35,
belongToGroup = {
401091,
401092
}
},
resourceConf = {
model = "SK_PL_047",
material = "MI_PL_047_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11020,
outEnter = "AS_CH_Enter_PL_047",
outShow = "AS_CH_IdleShow_PL_047",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_047_Prop",
outPropShow = "AS_CH_IdleShow_PL_047_Prop",
outPropSkeletal = "SK_PL_Prop_047",
outEnterSequence = "SQC_Enter_PL_047",
shareTexts = {
"欢乐组件，加载完毕",
"我的热情可以点燃引擎",
"派对很热闹，也带上我一起去好吗？",
"定期保养的时间到了",
"欢迎发掘我的新功能"
},
shareAnim = "AS_CH_Pose_001_PL_047"
},
[401092] = {
id = 401092,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "未来之星 欧米",
desc = "正在加载过关秘籍……",
icon = "CDN:Icon_PL_047_02",
outlookConf = {
belongTo = 401090,
fashionValue = 35,
belongToGroup = {
401091,
401092
}
},
resourceConf = {
model = "SK_PL_047",
material = "MI_PL_047_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11021,
outEnter = "AS_CH_Enter_PL_047",
outShow = "AS_CH_IdleShow_PL_047",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_047_Prop",
outPropShow = "AS_CH_IdleShow_PL_047_Prop",
outPropSkeletal = "SK_PL_Prop_047",
outEnterSequence = "SQC_Enter_PL_047",
shareTexts = {
"欢乐组件，加载完毕",
"我的热情可以点燃引擎",
"派对很热闹，也带上我一起去好吗？",
"定期保养的时间到了",
"欢迎发掘我的新功能"
},
shareAnim = "AS_CH_Pose_001_PL_047"
},
[401100] = {
id = 401100,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "赛罗奥特曼",
desc = "想追上我的实力，你还差两万年呢！",
icon = "CDN:Icon_PL_039",
getWay = "奥特曼祈愿",
jumpId = {
179
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_039",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_039",
outShow = "AS_CH_IdleShow_PL_039",
outShowIntervalTime = 10,
shareTexts = {
"英雄总是会在最后登场！",
"我是没有极限的！",
"闪耀的未来，就在你的眼中！",
"我只用实力说话",
"想赢过我，还早了两万年呢！"
},
shareAnim = "AS_CH_Pose_001_PL_039",
beginTime = {
seconds = 1706803200
},
suitId = 106,
suitName = "赛罗奥特曼",
themedId = 7,
bpShowId = 4,
suitIcon = "CDN:Icon_PL_039",
ThemedShowIdList = {
{
key = 7,
value = 4
}
}
},
[401110] = {
id = 401110,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小怪兽 芝顿",
desc = "我是凌驾于奥特曼之上的存在！",
icon = "CDN:Icon_PL_040",
getWay = "光之复苏",
jumpId = {
205
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_040",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_040",
outShow = "AS_CH_IdleShow_PL_040",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_040",
shareTexts = {
"怪兽不发威，你当我好欺负啊！",
"加入我的怪兽大军！",
"征服世界，就在眼前！",
"我的戏份还没结束呢！",
"芝顿~芝顿~"
},
shareAnim = "AS_CH_Pose_001_PL_040",
beginTime = {
seconds = 1706803200
},
suitId = 211,
suitName = "小怪兽 芝顿",
themedId = 7,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_040",
ThemedShowIdList = {
{
key = 7,
value = 1
}
}
},
[401120] = {
id = 401120,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "泽塔奥特曼",
desc = "喊出我的名字吧，泽塔奥特曼！",
icon = "CDN:Icon_PL_049",
getWay = "奥特曼祈愿",
jumpId = {
179
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_049",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_049",
outShow = "AS_CH_IdleShow_PL_049",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_049",
shareTexts = {
"永不言弃，坚持到底！",
"来看我的超级表现吧！",
"奥特之力，迸发火花！",
"出发吧，去保护更多的星宝！",
"这种时候只能靠气势了！"
},
shareAnim = "AS_CH_Pose_001_PL_049",
beginTime = {
seconds = 1706803200
},
suitId = 107,
suitName = "泽塔奥特曼",
themedId = 7,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_049",
ThemedShowIdList = {
{
key = 7,
value = 2
}
}
},
[401130] = {
id = 401130,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "神龙大侠 阿宝",
desc = "在下功夫熊猫",
icon = "CDN:Icon_PL_043",
getWay = "功夫熊猫祈愿",
jumpId = {
190
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_043",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_043_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_043",
outShow = "AS_CH_IdleShow_PL_043",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_043_Prop",
outPropSkeletal = "SK_PL_Prop_043",
outEnterSequence = "SQC_Enter_PL_043",
shareTexts = {
"神龙大侠在此， 还不快束手就擒"
},
shareAnim = "AS_CH_Pose_001_PL_043",
beginTime = {
seconds = 1711036800
},
sharePic = "T_Share_Suit_401130.astc",
suitId = 161,
suitName = "神龙大侠 阿宝",
themedId = 2,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_043",
shareNamePic = "T_Share_Suit_name_401130.astc",
shareBubblePic = "T_Share_Suit_frame_401130.astc",
ThemedShowIdList = {
{
key = 2,
value = 2
}
}
},
[401140] = {
id = 401140,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "木偶王子 安德尔",
desc = "你愿意做我的朋友吗？",
icon = "CDN:Icon_PL_048",
getWay = "星光剧场",
jumpId = {
183
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_048",
material = "MI_PL_048_1;MI_PL_048_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_048_Physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_048",
outShow = "AS_CH_IdleShow_PL_048",
outShowIntervalTime = 10,
outPropSkeletal = "SK_PL_Prop_048",
shareTexts = {
"每一天都是新的故事！",
"前方，会有谁在等着我呢？",
"送你花花，做我的朋友吧",
"今天会发生怎样的奇遇呢？",
"在这里会交到星朋友吗？"
},
shareAnim = "AS_CH_Pose_001_PL_048",
beginTime = {
seconds = 1709222400
},
suitStoryTextKey = [[在一个古老的木偶戏剧院里，有一个名叫安德尔的木偶王子。他曾经是剧院中最耀眼的明星，每一个动作，每一次微笑，都能轻易地俘获观众的心。然而随着时代的变迁，新的玩偶更新迭代，越来越多光鲜亮丽的新生代玩偶登上剧院舞台，获得了更多的鲜花与掌声，而曾经闪耀光环的安德尔逐渐被遗忘在角落里。

落满灰的安德尔不再像一个王子，他的木偶手指变得松动，华丽的油漆也慢慢脱落，因此，安德尔也逐渐变得忧郁。他回顾过去的辉煌，思考着自己存在的意义。在长时间的孤独和沉思中，他逐渐明白，他渴望的并不是在舞台上被操纵获得掌声，而是活出自己的精彩。他不再满足于作为一个被操纵的木偶，而是梦想着成为生活的主宰。

于是安德尔开始尝试踏上追梦的旅途。虽然他的动作不再像从前那样灵活，但每一个简单的动作都透露出他对生活的热爱和对自由的渴望。他不再等待被转动的发条，而开始尝试创作自己的故事，让发条配合着自己，诉说着独一无二的故事。安德尔相信，总有一天，他会回到舞台中央，演绎一段属于自己的，精彩绝伦的舞台剧。]],
suitId = 142,
suitName = "木偶王子 安德尔",
suitIcon = "CDN:Icon_PL_048"
},
[401141] = {
id = 401141,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "木偶王子 安德尔",
desc = "你愿意做我的朋友吗？",
icon = "CDN:Icon_PL_048_01",
outlookConf = {
belongTo = 401140,
fashionValue = 35,
belongToGroup = {
401141,
401142
}
},
resourceConf = {
model = "SK_PL_048",
material = "MI_PL_048_1_HP01;MI_PL_048_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_048_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11052,
outEnter = "AS_CH_Enter_PL_048",
outShow = "AS_CH_IdleShow_PL_048",
outShowIntervalTime = 10,
outPropSkeletal = "SK_PL_Prop_048",
shareTexts = {
"每一天都是新的故事！",
"前方，会有谁在等着我呢？",
"送你花花，做我的朋友吧",
"今天会发生怎样的奇遇呢？",
"在这里会交到星朋友吗？"
},
shareAnim = "AS_CH_Pose_001_PL_048"
},
[401142] = {
id = 401142,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "木偶王子 安德尔",
desc = "你愿意做我的朋友吗？",
icon = "CDN:Icon_PL_048_02",
outlookConf = {
belongTo = 401140,
fashionValue = 35,
belongToGroup = {
401141,
401142
}
},
resourceConf = {
model = "SK_PL_048",
material = "MI_PL_048_1_HP02;MI_PL_048_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_048_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11053,
outEnter = "AS_CH_Enter_PL_048",
outShow = "AS_CH_IdleShow_PL_048",
outShowIntervalTime = 10,
outPropSkeletal = "SK_PL_Prop_048",
shareTexts = {
"每一天都是新的故事！",
"前方，会有谁在等着我呢？",
"送你花花，做我的朋友吧",
"今天会发生怎样的奇遇呢？",
"在这里会交到星朋友吗？"
},
shareAnim = "AS_CH_Pose_001_PL_048"
},
[401150] = {
id = 401150,
effect = true,
name = "精小卫",
desc = "精卫衔微木，将以填沧海",
icon = "CDN:Icon_BU_052",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_052",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_052_Physics"
},
shareTexts = {
"不被理解，但仍然勇往直前"
},
beginTime = v2,
suitId = 76,
suitName = "精小卫",
bpShowId = 3,
seasonId = 2,
suitIcon = "CDN:Icon_BU_052",
SeasonShowIdList = {
{
key = 2,
value = 3
}
}
},
[401151] = {
id = 401151,
effect = true,
name = "精小卫",
desc = "精卫衔微木，将以填沧海",
icon = "CDN:Icon_BU_052_01",
outlookConf = {
belongTo = 401150,
fashionValue = 25,
belongToGroup = {
401151
}
},
resourceConf = {
model = "SK_BU_052",
material = "MI_BU_052_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_052_Physics",
materialSlot = "Skin"
},
commodityId = 11027,
shareTexts = {
"不被理解，但仍然勇往直前"
}
},
[401160] = {
id = 401160,
effect = true,
name = "夔牛牛",
desc = "勇气和力量的化身",
icon = "CDN:Icon_BU_053",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_053",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_053_Physics"
},
shareTexts = {
"力量与智慧的结合，你的冒险伙伴"
},
beginTime = v2,
suitId = 77,
suitName = "夔牛牛",
bpShowId = 6,
seasonId = 2,
suitIcon = "CDN:Icon_BU_053",
SeasonShowIdList = {
{
key = 2,
value = 6
}
}
},
[401161] = {
id = 401161,
effect = true,
name = "夔牛牛",
desc = "勇气和力量的化身",
icon = "CDN:Icon_BU_053_01",
outlookConf = {
belongTo = 401160,
fashionValue = 25,
belongToGroup = {
401161
}
},
resourceConf = {
model = "SK_BU_053",
material = "MI_BU_053_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_053_Physics",
materialSlot = "Skin"
},
commodityId = 11029,
shareTexts = {
"力量与智慧的结合，你的冒险伙伴"
}
},
[401170] = {
id = 401170,
effect = true,
name = "鹿灵灵",
desc = "鹿角绒绒，可爱无双",
icon = "CDN:Icon_BU_054",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_054",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_054_Physics"
},
shareTexts = {
"鹿鸣与野，岁月悠悠"
},
beginTime = v2,
suitId = 78,
suitName = "鹿灵灵",
bpShowId = 4,
seasonId = 2,
suitIcon = "CDN:Icon_BU_054",
SeasonShowIdList = {
{
key = 2,
value = 4
}
}
},
[401171] = {
id = 401171,
effect = true,
name = "鹿灵灵",
desc = "鹿角绒绒，可爱无双",
icon = "CDN:Icon_BU_054_01",
outlookConf = {
belongTo = 401170,
fashionValue = 25,
belongToGroup = {
401171
}
},
resourceConf = {
model = "SK_BU_054",
material = "MI_BU_054_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_054_Physics",
materialSlot = "Skin"
},
commodityId = 11031,
shareTexts = {
"鹿鸣与野，岁月悠悠"
}
},
[401180] = {
id = 401180,
effect = true,
name = "梅中客",
desc = "寻梅问道，诗意满怀",
icon = "CDN:Icon_BU_055",
getWay = "山海通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_055",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_055_Physics"
},
shareTexts = {
"邀月对雪，醉卧寒梅之下"
},
beginTime = v2,
suitId = 79,
suitName = "梅中客",
suitIcon = "CDN:Icon_BU_055"
},
[401181] = {
id = 401181,
effect = true,
name = "梅中客",
desc = "寻梅问道，诗意满怀",
icon = "CDN:Icon_BU_055_01",
outlookConf = {
belongTo = 401180,
fashionValue = 25,
belongToGroup = {
401181
}
},
resourceConf = {
model = "SK_BU_055",
material = "MI_BU_055_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_055_Physics",
materialSlot = "Skin"
},
commodityId = 11033,
shareTexts = {
"邀月对雪，醉卧寒梅之下"
}
},
[401190] = {
id = 401190,
effect = true,
name = "梅上仙",
desc = "仙隐于雪，飘然出尘",
icon = "CDN:Icon_BU_056",
getWay = "山海通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_056",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_056_Physics"
},
shareTexts = {
"挽一缕梅香，演绎一段风雅仙韵"
},
beginTime = v2,
suitId = 80,
suitName = "梅上仙",
suitIcon = "CDN:Icon_BU_056"
},
[401191] = {
id = 401191,
effect = true,
name = "梅上仙",
desc = "仙隐于雪，飘然出尘",
icon = "CDN:Icon_BU_056_01",
outlookConf = {
belongTo = 401190,
fashionValue = 25,
belongToGroup = {
401191
}
},
resourceConf = {
model = "SK_BU_056",
material = "MI_BU_056_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_056_Physics",
materialSlot = "Skin"
},
commodityId = 11035,
shareTexts = {
"挽一缕梅香，演绎一段风雅仙韵"
}
},
[401200] = {
id = 401200,
effect = true,
name = "角梦梦",
desc = "我的魔法，可以将日常变成童话",
icon = "CDN:Icon_BU_057",
getWay = "月夜歌吟",
jumpId = {
79
},
resourceConf = {
model = "SK_BU_057",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_057_Physics"
},
shareTexts = {
"跟我一起做五彩斑斓的梦"
},
beginTime = {
seconds = 1707408000
},
suitId = 118,
suitName = "角梦梦",
suitIcon = "CDN:Icon_BU_057"
},
[401201] = {
id = 401201,
effect = true,
name = "角梦梦",
desc = "我的魔法，可以将日常变成童话",
icon = "CDN:Icon_BU_057_01",
outlookConf = {
belongTo = 401200,
fashionValue = 25,
belongToGroup = {
401201
}
},
resourceConf = {
model = "SK_BU_057",
material = "MI_BU_057_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_057_Physics",
materialSlot = "Skin"
},
commodityId = 11037,
shareTexts = {
"跟我一起做五彩斑斓的梦"
}
},
[401210] = {
id = 401210,
effect = true,
name = "花灯灯",
desc = "照亮夜空，照亮心灵",
icon = "CDN:Icon_BU_058",
resourceConf = {
model = "SK_BU_058",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_058_Physics"
},
shareTexts = {
"星宝在灯火阑珊处等你哦！"
},
beginTime = v2,
suitId = 128,
suitName = "花灯灯",
suitIcon = "CDN:Icon_BU_058"
},
[401211] = {
id = 401211,
effect = true,
name = "花灯灯",
desc = "照亮夜空，照亮心灵",
icon = "CDN:Icon_BU_058_01",
outlookConf = {
belongTo = 401210,
fashionValue = 25,
belongToGroup = {
401211
}
},
resourceConf = {
model = "SK_BU_058",
material = "MI_BU_058_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_058_Physics",
materialSlot = "Skin"
},
commodityId = 11039,
shareTexts = {
"星宝在灯火阑珊处等你哦！"
}
},
[401220] = {
id = 401220,
effect = true,
name = "游梦梦",
desc = "春困秋乏夏打盹，睡不醒的冬三月",
icon = "CDN:Icon_BU_059",
resourceConf = {
model = "SK_BU_059",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_059_Physics"
},
shareTexts = {
"今天先做哪个美梦呢？"
},
beginTime = v2,
suitId = 129,
suitName = "游梦梦",
suitIcon = "CDN:Icon_BU_059"
},
[401221] = {
id = 401221,
effect = true,
name = "游梦梦",
desc = "春困秋乏夏打盹，睡不醒的冬三月",
icon = "CDN:Icon_BU_059_01",
outlookConf = {
belongTo = 401220,
fashionValue = 25,
belongToGroup = {
401221
}
},
resourceConf = {
model = "SK_BU_059",
material = "MI_BU_059_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_059_Physics",
materialSlot = "Skin"
},
commodityId = 11041,
shareTexts = {
"今天先做哪个美梦呢？"
}
},
[401230] = {
id = 401230,
effect = true,
name = "凌小霜",
desc = "山海的秘密由我守护",
icon = "CDN:Icon_BU_060",
getWay = "赛季兑换",
jumpId = {
16
},
resourceConf = {
model = "SK_BU_060",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_060_Physics"
},
shareTexts = {
"凌尽秋霜，只为守护山海"
},
beginTime = v2,
suitId = 125,
suitName = "凌小霜",
suitIcon = "CDN:Icon_BU_060"
},
[401231] = {
id = 401231,
effect = true,
name = "凌小霜",
desc = "山海的秘密由我守护",
icon = "CDN:Icon_BU_060_01",
outlookConf = {
belongTo = 401230,
fashionValue = 25,
belongToGroup = {
401231
}
},
resourceConf = {
model = "SK_BU_060",
material = "MI_BU_060_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_060_Physics",
materialSlot = "Skin"
},
commodityId = 11043,
shareTexts = {
"凌尽秋霜，只为守护山海"
}
},
[401240] = {
id = 401240,
effect = true,
name = "树芽芽",
desc = "你在二三月种下的种子，七八月才会结果",
icon = "CDN:Icon_BU_064",
resourceConf = {
model = "SK_BU_064",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_064_Physics"
},
shareTexts = {
"在春天种下一颗希望的种子"
},
beginTime = v2,
suitId = 130,
suitName = "树芽芽",
suitIcon = "CDN:Icon_BU_064"
},
[401241] = {
id = 401241,
effect = true,
name = "树芽芽",
desc = "你在二三月种下的种子，七八月才会结果",
icon = "CDN:Icon_BU_064_01",
outlookConf = {
belongTo = 401240,
fashionValue = 25,
belongToGroup = {
401241
}
},
resourceConf = {
model = "SK_BU_064",
material = "MI_BU_064_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_064_Physics",
materialSlot = "Skin"
},
commodityId = 11055,
shareTexts = {
"在春天种下一颗希望的种子"
}
},
[401250] = {
id = 401250,
effect = true,
name = "摩羯星",
desc = "从不随便相信谁，除了自己给的安全感",
icon = "CDN:Icon_BU_067",
resourceConf = {
model = "SK_BU_067",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_067_Physics"
},
shareTexts = {
"摩羯的心思，不是你能猜透"
},
beginTime = v2,
suitId = 120,
suitName = "摩羯星",
suitIcon = "CDN:Icon_BU_067"
},
[401251] = {
id = 401251,
effect = true,
name = "摩羯星",
desc = "从不随便相信谁，除了自己给的安全感",
icon = "CDN:Icon_BU_067_01",
outlookConf = {
belongTo = 401250,
fashionValue = 25,
belongToGroup = {
401251
}
},
resourceConf = {
model = "SK_BU_067",
material = "MI_BU_067_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_067_Physics",
materialSlot = "Skin"
},
commodityId = 11057,
shareTexts = {
"摩羯的心思，不是你能猜透"
}
},
[401260] = {
id = 401260,
effect = true,
name = "萧遥遥",
desc = "逍遥自在心境明，不忧风雨不忧晴",
icon = "CDN:Icon_BU_065",
getWay = "百变星宝秀",
jumpId = {
146
},
resourceConf = {
model = "SK_BU_065",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_065_Physics"
},
shareTexts = {
"逍遥自在心境明，不忧风雨不忧晴"
},
beginTime = v2,
suitId = 121,
suitName = "萧遥遥",
suitIcon = "CDN:Icon_BU_065"
},
[401261] = {
id = 401261,
effect = true,
name = "萧遥遥",
desc = "逍遥自在心境明，不忧风雨不忧晴",
icon = "CDN:Icon_BU_065_01",
outlookConf = {
belongTo = 401260,
fashionValue = 25,
belongToGroup = {
401261
}
},
resourceConf = {
model = "SK_BU_065",
material = "MI_BU_065_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_065_Physics",
materialSlot = "Skin"
},
commodityId = 11060,
shareTexts = {
"逍遥自在心境明，不忧风雨不忧晴"
}
},
[401270] = {
id = 401270,
effect = true,
name = "鸽王",
desc = "其实说鸽就鸽，亦是一种不鸽",
icon = "CDN:Icon_BU_066",
getWay = "百变星宝秀",
jumpId = {
146
},
resourceConf = {
model = "SK_BU_066",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"咕咕，下次一定！"
},
beginTime = v2,
suitId = 122,
suitName = "鸽王",
suitIcon = "CDN:Icon_BU_066"
},
[401271] = {
id = 401271,
effect = true,
name = "鸽王",
desc = "其实说鸽就鸽，亦是一种不鸽",
icon = "CDN:Icon_BU_066_01",
outlookConf = {
belongTo = 401270,
fashionValue = 25,
belongToGroup = {
401271
}
},
resourceConf = {
model = "SK_BU_066",
material = "MI_BU_066_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11062,
shareTexts = {
"咕咕，下次一定！"
}
},
[401280] = {
id = 401280,
effect = true,
name = "咖啡师小瑞",
desc = "每天的幸运，从一杯瑞幸咖啡开始",
icon = "CDN:Icon_BU_086",
resourceConf = {
model = "SK_BU_086",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_086_Physics"
},
shareTexts = {
"这一杯，幸运在握"
},
beginTime = v2,
suitId = 126,
suitName = "咖啡师小瑞",
suitIcon = "CDN:Icon_BU_086"
},
[401290] = {
id = 401290,
effect = true,
name = "最佳笋友",
desc = "我，是真的笋",
icon = "CDN:Icon_BU_071",
resourceConf = {
model = "SK_BU_071",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"让友情在互损中升华"
},
beginTime = {
seconds = 4101552000
},
suitId = 930,
suitName = "最佳笋友",
suitIcon = "CDN:Icon_BU_071"
},
[401291] = {
id = 401291,
effect = true,
name = "最佳笋友",
desc = "我，是真的笋",
icon = "CDN:Icon_BU_071_01",
outlookConf = {
belongTo = 401290,
fashionValue = 25,
belongToGroup = {
401291
}
},
resourceConf = {
model = "SK_BU_071",
material = "MI_BU_071_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11068,
shareTexts = {
"让友情在互损中升华"
}
},
[401330] = {
id = 401330,
effect = true,
name = "豹卷卷",
desc = "冬天的快乐是滑雪给的",
icon = "CDN:Icon_BU_061",
getWay = "发现",
jumpId = {
20
},
resourceConf = {
model = "SK_BU_061",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_061_Physics"
},
shareTexts = {
"一起在雪道上留下快乐的痕迹"
},
beginTime = v2,
suitId = 127,
suitName = "豹卷卷",
suitIcon = "CDN:Icon_BU_061"
},
[401331] = {
id = 401331,
effect = true,
name = "豹卷卷",
desc = "冬天的快乐是滑雪给的",
icon = "CDN:Icon_BU_061_01",
outlookConf = {
belongTo = 401330,
fashionValue = 25,
belongToGroup = {
401331
}
},
resourceConf = {
model = "SK_BU_061",
material = "MI_BU_061_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_061_Physics",
materialSlot = "Skin"
},
commodityId = 11045,
shareTexts = {
"一起在雪道上留下快乐的痕迹"
}
},
[401340] = {
id = 401340,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "小龙女 洛灵",
desc = "快乐如清泉涌现，永不干涸！",
icon = "CDN:Icon_OG_005",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_005",
material = "MI_OG_005_1;MI_OG_005_2;MI_OG_005_3;MI_OG_005_4;MI_OG_005_5;MI_OG_005_6;MI_OG_005_7",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_005_Physics",
materialSlot = "Skin;Skin_Opaque_01;Skin_Translucent;Skin_Translucent_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_005_PV/Level_OG_005_Intact",
outIdle = "AS_CH_inIdle_OG_005",
outShow = "AS_CH_IdleShow_OG_005",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_OG_005_Prop",
outPropSkeletal = "SK_OG_Prop_005",
soundId = {
4009,
4010
},
outIdle_pv = "LS_PV_OG_005_Idle",
outEnterSequence = "LS_PV_OG_005",
shareTexts = {
"愿意与我风雨同舟吗？",
"山水定会相逢！",
"不积小流,无以成江海",
"逆水行舟，不进则退",
"上善若水，细水长流"
},
shareAnim = "AS_CH_Pose_001_OG_005",
beginTime = v2,
suitStoryTextKey = [[玉渊龙王最小的女儿叫做洛灵，她诞生于玉泉之中，身上散发着清新的气息，宛若一道清泉。洛灵拥有一双明亮的眼睛，仿佛浸水后澄澈的水晶。胸前的宝珠闪耀着蓝色的光芒，象征着她与水元素的纽带。

从小洛灵就展示出与众不同的特殊能力。她可以呼风唤雨，操控水流，甚至与水元素进行心灵交流。她能够分别出小溪和瀑布不同水流的声音，也能够与在水中的鱼儿贝类畅谈。洛灵的每一滴泪水都蕴含着神秘的力量，每一声笑语都如泉水流淌般清澈。

她能够轻松地操纵水流，形成华丽的水幕和奇妙的形状。最不可思议的是，洛灵还可以召唤出百年一见的水龙。小水龙是洛灵的守护神，在洛灵需要召唤甘霖或是遇到危险之时，水龙总会盘旋于她身边。

洛灵心地善良，总是尽力用她的能力帮助求水之人。在干旱的季节，她会用自己的力量召唤雨水，让枯萎的庄稼重新焕发生机。在炎热的夏日，她会创造水雾，为人们带来清凉。但洛灵同时也是一个瞌睡虫，如果长时间没有降雨，一定是洛灵睡过头了。偶尔洛灵也会利用小水花，捉弄亲密的朋友。

别看洛灵总是一副没有烦恼，上天入地开心玩乐的样子，该认真的时候绝不会掉链子。洛灵肩负着守护水元素的使命，与哥哥分别守护着山海奇境中的一方水域。]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2",
suitId = 74,
suitName = "小龙女 洛灵",
bpShowId = 1,
seasonId = 2,
suitIcon = "CDN:Icon_OG_005",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
SeasonShowIdList = {
{
key = 2,
value = 1
}
}
},
[401341] = {
id = 401341,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "小龙女 洛灵",
desc = "快乐如清泉涌现，永不干涸！",
icon = "CDN:Icon_OG_005_01",
outlookConf = {
belongTo = 401340,
fashionValue = 125,
belongToGroup = {
401341,
401342
}
},
resourceConf = {
model = "SK_OG_005",
material = "MI_OG_005_1_HP01;MI_OG_005_2_HP01;MI_OG_005_3_HP01;MI_OG_005_4_HP01;MI_OG_005_5_HP01;MI_OG_005_6_HP01;MI_OG_005_7_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_005_Physics",
materialSlot = "Skin;Skin_Opaque_01;Skin_Translucent;Skin_Translucent_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04"
},
commodityId = 11047,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_005_PV/Level_OG_005_Intact",
outIdle = "AS_CH_inIdle_OG_005",
outShow = "AS_CH_IdleShow_OG_005_HP01",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_OG_005_Prop_HP01",
outPropSkeletal = "SK_OG_Prop_005_HP01",
soundId = {
4009,
4010
},
outIdle_pv = "LS_PV_OG_005_HP01_Idle",
outEnterSequence = "LS_PV_OG_005_HP01",
shareTexts = {
"愿意与我风雨同舟吗？",
"山水定会相逢！",
"不积小流,无以成江海",
"逆水行舟，不进则退",
"上善若水，细水长流"
},
shareAnim = "AS_CH_Pose_001_OG_005",
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030"
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data