com.tencent.wea.xlsRes.table_InGameItemResource
excel/xls/D_道具表_时装.xlsm sheet:局内时装资源
rows {
  id: 411660
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_CurveTest;Skin_Translucent_01:MI_CurveTest;Skin_Opaque_05:MI_CurveTest;Skin_Opaque_04:MI_CurveTest;Skin_Opaque_02:MI_CurveTest"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411661
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_OG_051_HP01"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411662
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_OG_051_HP02"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411670
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_CurveTest_2;Skin_Translucent_01:MI_CurveTest_2;Skin_Opaque_05:MI_CurveTest_2;Skin_Opaque_04:MI_CurveTest_2;Skin_Opaque_02:MI_CurveTest_2"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411671
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_OG_051_HP02"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411672
  headWear: "SK_OG_051_LOD1"
  type: ItemType_Suit
  quality: 1
  characterID: 2
  resourceConf {
    material: "Skin:MI_OG_051"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_OG_051_Physics"
    bBlendWithHandHoldIdleAnim: true
  }
  bodytype: 2
  FootwearHeight: 0.0
}
rows {
  id: 411680
  headWear: "SK_BU_320_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411681
  headWear: "SK_BU_320_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    material: "MI_BU_320_HP01_LOD1;MI_BU_320_HP01_LOD2"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411690
  headWear: "SK_BU_312_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411691
  headWear: "SK_BU_312_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    material: "MI_BU_312_HP01_LOD1;MI_BU_312_HP01_LOD2"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411700
  headWear: "SK_PL_321_LOD1"
  type: ItemType_Suit
  quality: 2
  characterID: 3
  resourceConf {
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "SK_PL_321_Physics"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411710
  headWear: "SK_BU_352_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411711
  headWear: "SK_BU_352_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    material: "MI_BU_352_HP01_LOD1;MI_BU_352_HP01_LOD2"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411720
  headWear: "SK_BU_353_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
rows {
  id: 411721
  headWear: "SK_BU_353_LOD1"
  type: ItemType_Suit
  quality: 3
  characterID: 3
  resourceConf {
    material: "MI_BU_353_HP01_LOD1;MI_BU_353_HP01_LOD2"
    headOffset: 0
    headOffset: 0
    backOffset: 0
    backOffset: 0
    faceOffset: 0
    faceOffset: 0
    physics: "ALL_ceshi_ModSuit_Physics_Head"
  }
  bodytype: 0
  FootwearHeight: 0.0
}
