com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_miles.xlsx sheet:幸运值配置
rows {
  id: 40000001
  poolId: 40000001
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 40000002
  poolId: 40000002
  lvThreshold: 30
  groupWeight: 1
  groupWeight: 1000
  groupWeight: 9000
}
rows {
  id: 40000003
  poolId: 40000002
  lvThreshold: 60
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 9000
}
rows {
  id: 40000004
  poolId: 40000002
  lvThreshold: 90
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 9000
}
rows {
  id: 40000005
  poolId: 40000003
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 40000006
  poolId: 40000003
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 40000007
  poolId: 40000003
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 40000008
  poolId: 40000003
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 40000009
  poolId: 40000003
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 40000010
  poolId: 400000040
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 40000011
  poolId: 400000040
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 40000012
  poolId: 400000040
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 40000013
  poolId: 400000040
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 40000014
  poolId: 400000040
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 40000015
  poolId: 400000040
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 40000016
  poolId: 400000040
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 40000017
  poolId: 400000040
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 40000018
  poolId: 400000040
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 40000019
  poolId: 400000040
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 40000020
  poolId: 400000040
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 40000021
  poolId: 400000041
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
rows {
  id: 40000022
  poolId: 40000005
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 40000023
  poolId: 40000005
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 40000024
  poolId: 40000005
  lvThreshold: 8
  groupWeight: 40
  groupWeight: 120
  groupWeight: 1500
}
rows {
  id: 40000025
  poolId: 40000005
  lvThreshold: 10
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000026
  poolId: 40000005
  lvThreshold: 11
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 40000027
  poolId: 40000005
  lvThreshold: 12
  groupWeight: 1600
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000028
  poolId: 40000006
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 40000029
  poolId: 40000006
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 40000030
  poolId: 40000006
  lvThreshold: 8
  groupWeight: 40
  groupWeight: 120
  groupWeight: 1500
}
rows {
  id: 40000031
  poolId: 40000006
  lvThreshold: 10
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000032
  poolId: 40000006
  lvThreshold: 11
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 40000033
  poolId: 40000006
  lvThreshold: 12
  groupWeight: 1600
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000034
  poolId: 40000007
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 40000035
  poolId: 40000007
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 40000036
  poolId: 40000007
  lvThreshold: 8
  groupWeight: 40
  groupWeight: 120
  groupWeight: 1500
}
rows {
  id: 40000037
  poolId: 40000007
  lvThreshold: 10
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000038
  poolId: 40000007
  lvThreshold: 11
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 40000039
  poolId: 40000007
  lvThreshold: 12
  groupWeight: 1600
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 40000040
  poolId: 40000008
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 40000041
  poolId: 40000009
  lvThreshold: 12
  groupWeight: 1
  groupWeight: 10
  groupWeight: 100
  groupWeight: 1000
}
rows {
  id: 40000042
  poolId: 40000010
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000043
  poolId: 40000010
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000044
  poolId: 40000010
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000045
  poolId: 40000010
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000046
  poolId: 40000010
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000047
  poolId: 40000011
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000048
  poolId: 40000011
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000049
  poolId: 40000011
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000050
  poolId: 40000011
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000051
  poolId: 40000011
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000052
  poolId: 40000012
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000053
  poolId: 40000012
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000054
  poolId: 40000012
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000055
  poolId: 40000012
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000056
  poolId: 40000012
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000057
  poolId: 40000013
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000058
  poolId: 40000013
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000059
  poolId: 40000013
  lvThreshold: 7
  groupWeight: 4000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000060
  poolId: 40000013
  lvThreshold: 8
  groupWeight: 10000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 40000061
  poolId: 40000013
  lvThreshold: 9
  groupWeight: 1000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 40000062
  poolId: 400000140
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 40000063
  poolId: 400000140
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 40000064
  poolId: 400000140
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 40000065
  poolId: 400000140
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 40000066
  poolId: 400000140
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 40000067
  poolId: 400000140
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 40000068
  poolId: 400000140
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 40000069
  poolId: 400000140
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 40000070
  poolId: 400000140
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 40000071
  poolId: 400000140
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 40000072
  poolId: 400000140
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 40000073
  poolId: 400000141
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
rows {
  id: 40000074
  poolId: 40000009
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
  groupWeight: 900
  groupWeight: 9000
}
rows {
  id: 40000075
  poolId: 40000009
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 900
  groupWeight: 4000
  groupWeight: 5000
}
rows {
  id: 40000076
  poolId: 40000009
  lvThreshold: 9
  groupWeight: 10
  groupWeight: 4990
  groupWeight: 4000
  groupWeight: 1000
}
rows {
  id: 40000077
  poolId: 40000009
  lvThreshold: 12
  groupWeight: 1000
  groupWeight: 500
  groupWeight: 3900
  groupWeight: 4600
}
rows {
  id: 40000080
  poolId: 40000016
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 999
  groupWeight: 9000
  groupWeight: 90000
}
rows {
  id: 40000081
  poolId: 40000016
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 90
  groupWeight: 4900
  groupWeight: 5000
}
rows {
  id: 40000082
  poolId: 40000016
  lvThreshold: 9
  groupWeight: 100
  groupWeight: 4900
  groupWeight: 4000
  groupWeight: 1000
}
rows {
  id: 40000083
  poolId: 40000016
  lvThreshold: 12
  groupWeight: 1000
  groupWeight: 500
  groupWeight: 3900
  groupWeight: 4600
}
rows {
  id: 40000084
  poolId: 400000150
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 40000085
  poolId: 400000150
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 40000086
  poolId: 400000150
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 40000087
  poolId: 400000150
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 40000088
  poolId: 400000150
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 40000089
  poolId: 400000150
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 40000090
  poolId: 400000150
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 40000091
  poolId: 400000150
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 40000092
  poolId: 400000150
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 40000093
  poolId: 400000150
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 40000094
  poolId: 400000150
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 40000095
  poolId: 400000151
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
