com.tencent.wea.xlsRes.table_TextEntryXiaowo
excel/xls/W_文本表_小窝.xlsx sheet:文本配置
rows {
  id: "MoneyTree_1"
  content: "下一级："
  switch: 1
}
rows {
  id: "MoneyTree_2"
  content: "摇一摇福之树就能掉落装扮星家园的元件哦"
  switch: 1
}
rows {
  id: "MoneyTree_3"
  content: "为摇钱树们浇水能更快获得摇树次数，并有机会获得赠礼。当访客帮助主人浇水时获得赠礼，主人也能同时得到一份哦！"
  switch: 1
}
rows {
  id: "MoneyTree_4"
  content: "已满级"
  switch: 1
}
rows {
  id: "MoneyTree_5"
  content: "您确定要花费"
  switch: 1
}
rows {
  id: "MoneyTree_6"
  content: "摇一摇吗？"
  switch: 1
}
rows {
  id: "MoneyTree_7"
  content: "下次不再提醒"
  switch: 1
}
rows {
  id: "MoneyTree_8"
  content: "浇水成功！减少20分钟摇树等待时间"
  switch: 1
}
rows {
  id: "MoneyTree_9"
  content: "摇树次数已用完，请等待次数恢复"
  switch: 1
}
rows {
  id: "MoneyTree_10"
  content: "占用"
  switch: 1
}
rows {
  id: "MoneyTree_11"
  content: "植树经验+{0}"
  switch: 1
}
rows {
  id: "HomeParty_ClosePartyTip"
  content: "结束后，你的星家园将不被展示在派对公告板中。"
  switch: 1
}
rows {
  id: "HomeParty_InHomeTip"
  content: "你已经在这个星家园里啦"
  switch: 1
}
rows {
  id: "HomeParty_SendFriendRequest"
  content: "你发出了一份好友申请"
  switch: 1
}
rows {
  id: "HomeParty_PartyEmpty"
  content: "现在没有在开派对的星家园呢，请稍后再来看看吧。"
  switch: 1
}
rows {
  id: "HomeParty_FriendEmpty"
  content: "好友们的星家园将展示在这里，快邀他创建家园吧。"
  switch: 1
}
rows {
  id: "HomeParty_CollectEmpty"
  content: "拜访其他星宝的家园时，可以把喜欢的家园收藏起来。"
  switch: 1
}
rows {
  id: "HomeParty_SearchEmpty"
  content: "请输入您要搜索的玩家"
  switch: 1
}
rows {
  id: "HomeParty_My"
  content: "我"
  switch: 1
}
rows {
  id: "HomePhoto_Unopen"
  content: "C代码尚未更新,无法使用该相机"
  switch: 1
}
rows {
  id: "HomeParty_StateTip"
  content: "离开自己的星家园或离线时，你的星家园将不被显示在派对公告板中哦"
  switch: 1
}
rows {
  id: "HomeParty_NotSearchTip"
  content: "没搜到匹配的家园，请重新尝试"
  switch: 1
}
rows {
  id: "HomeParty_HomeName"
  content: "{0}的星家园"
  switch: 1
}
rows {
  id: "HomeParty_NotHaveParty"
  content: "很抱歉，现在没有在开派对的星家园呢"
  switch: 1
}
rows {
  id: "HomeParty_NotOpenParty"
  content: "这是您的家园信息，快进入家园看看吧！"
  switch: 1
}
rows {
  id: "HomePartyTab_1"
  content: "派对"
  switch: 1
}
rows {
  id: "HomePartyTab_2"
  content: "好友"
  switch: 1
}
rows {
  id: "HomePartyTab_3"
  content: "收藏"
  switch: 1
}
rows {
  id: "HomePartyTab_4"
  content: "搜索玩家"
  switch: 1
}
rows {
  id: "HomeParty_1"
  content: "发布成功！期待星宝们的加入吧~"
  switch: 1
}
rows {
  id: "HomeParty_2"
  content: "发生错误了哦，请重新尝试发布"
  switch: 1
}
rows {
  id: "HomeParty_3"
  content: "封面图上传失败了哦，请再试一次吧"
  switch: 1
}
rows {
  id: "HomeParty_4"
  content: "上传中…"
  switch: 1
}
rows {
  id: "HomeParty_5"
  content: "审核中…"
  switch: 1
}
rows {
  id: "HomeParty_6"
  content: "请输入昵称或UID"
  switch: 1
}
rows {
  id: "HomeParty_7"
  content: "输入关键字搜索"
  switch: 1
}
rows {
  id: "HomeInfo_1"
  content: "这位主人重新发布了星家园，请重新拜访吧"
  switch: 1
}
rows {
  id: "HomeInfo_2"
  content: "登入失败"
  switch: 1
}
rows {
  id: "HomeInfo_3"
  content: "星家园已重新发布，请重新进入"
  switch: 1
}
rows {
  id: "HomeInfo_4"
  content: "尝试进入星家园失败"
  switch: 1
}
rows {
  id: "HomeInfo_5"
  content: "摇钱树放置数量上限"
  switch: 1
}
rows {
  id: "HomeInfo_6"
  content: "星家园美观度：将能够提升美观度的元件摆放在家园里能提高美观度评分，评分达到要求即可在管理面板为家园升级。"
  switch: 1
}
rows {
  id: "HomeInfo_7"
  content: "要离开星家园吗？"
  switch: 1
}
rows {
  id: "HomeInfo_8"
  content: "出现了神秘的网络波动，请重新进入星家园"
  switch: 1
}
rows {
  id: "HomeInfo_9"
  content: "其他星宝进入了家园，星能源信号正在切换多人环境"
  switch: 1
}
rows {
  id: "HomeInfo_10"
  content: "联机中"
  switch: 1
}
rows {
  id: "HomeInfo_11"
  content: "家园中无法使用创建界面"
  switch: 1
}
rows {
  id: "HomeInfo_12"
  content: "检测到您正在访问的家园版本较高，您将自动返回大厅"
  switch: 1
}
rows {
  id: "HomeInfo_13"
  content: "检测到您正在访问的家园版本较低，您将自动返回大厅"
  switch: 1
}
rows {
  id: "HomeLvlUp_1"
  content: "货币数量不足！"
  switch: 1
}
rows {
  id: "HomeLvlUp_2"
  content: "美观度没有达到要求哦"
  switch: 1
}
rows {
  id: "HomeLvlUp_3"
  content: "级"
  switch: 1
}
rows {
  id: "HomeLvlUp_4"
  content: "恭"
  switch: 1
}
rows {
  id: "HomeLvlUp_5"
  content: "喜"
  switch: 1
}
rows {
  id: "HomeLvlUp_6"
  content: "解"
  switch: 1
}
rows {
  id: "HomeLvlUp_7"
  content: "锁"
  switch: 1
}
rows {
  id: "HomeLvlUp_8"
  content: "家园"
  switch: 1
}
rows {
  id: "HomeLvlUp_9"
  content: "占用"
  switch: 1
}
rows {
  id: "HomeLvlUp_10"
  content: "占用"
  switch: 1
}
rows {
  id: "HomeReport_1"
  content: "该星家园有违规行为，暂时无法进入哦"
  switch: 1
}
rows {
  id: "HomeReport_2"
  content: "很抱歉，您的星家园存在违规行为，已暂时关闭他人的拜访通道，请您清除违规内容后重新发布并等待审核。"
  switch: 1
}
rows {
  id: "HomeReport_3"
  content: "永久。"
  switch: 1
}
rows {
  id: "HomeReport_4"
  content: "由于违规行为，当前无法进入星家园，截止日期为：{0}"
  switch: 1
}
rows {
  id: "HomeReport_5"
  content: "由于违规行为，当前无法进入星家园"
  switch: 1
}
rows {
  id: "HomeReport_6"
  content: "星家园入口关闭"
  switch: 1
}
rows {
  id: "HomeReport_7"
  content: "很抱歉，检测到家园内存在异常元件，请修改后重新发布。"
  switch: 1
}
rows {
  id: "HomeShop_1"
  content: "天"
  switch: 1
}
rows {
  id: "HomeShop_2"
  content: "永久"
  switch: 1
}
rows {
  id: "HomeShop_3"
  content: "乐之叶不足，将乐之树放置在星家园里，摇晃它就能获得乐之叶。"
  switch: 1
}
rows {
  id: "HomeShop_4"
  content: "梦之叶不足，将梦之树放置在星家园里，摇晃它就能获得梦之叶。"
  switch: 1
}
rows {
  id: "HomeShop_5"
  content: "确定购买这些元件吗？"
  switch: 1
}
rows {
  id: "HomeShop_6"
  content: "已提交购买，请等待"
  switch: 1
}
rows {
  id: "HomePublish_1"
  content: "发布"
  switch: 1
}
rows {
  id: "HomePublish_2"
  content: "您还可以在<Orange28>自然</>中找到<Orange28>摇钱树</>继续摆放，不摆放会损失不少好东西，依然要现在发布吗？"
  switch: 1
}
rows {
  id: "HomePublish_3"
  content: "描述文字不能超过30字"
  switch: 1
}
rows {
  id: "HomePublish_4"
  content: "正在发布中"
  switch: 1
}
rows {
  id: "HomePublish_5"
  content: "封面图上传中，请稍后重试"
  switch: 1
}
rows {
  id: "HomePublish_6"
  content: "封面图审核中，请稍后重试"
  switch: 1
}
rows {
  id: "HomePublish_7"
  content: "地图发布失败，请稍后重试"
  switch: 1
}
rows {
  id: "HomePublish_8"
  content: "未放置家园出生点，请在“玩法-流程”中进行放置"
  switch: 1
}
rows {
  id: "HomePublish_9"
  content: "出生点的摆放需同下方平台保持合适的距离"
  switch: 1
}
rows {
  id: "HomePublish_10"
  content: "加载地图失败"
  switch: 1
}
rows {
  id: "HomePublish_11"
  content: "服务器已断开，是否重新连接？"
  switch: 1
}
rows {
  id: "HomePublish_12"
  content: "你有尚未获得的元件哦，现在就去购买吗？"
  switch: 1
}
rows {
  id: "HomePublish_13"
  content: "前往购物车"
  switch: 1
}
rows {
  id: "HomePublish_14"
  content: "放弃并离开"
  switch: 1
}
rows {
  id: "HomePublish_15"
  content: "退出"
  switch: 1
}
rows {
  id: "HomePublish_16"
  content: "你刚刚进行的装修还没有保存，\n确定退出装修模式吗？"
  switch: 1
}
rows {
  id: "HomePublish_17"
  content: "放弃保存并退出"
  switch: 1
}
rows {
  id: "HomePublish_18"
  content: "取消"
  switch: 1
}
rows {
  id: "HomePublish_19"
  content: "请等待{0}秒再尝试发布哦"
  switch: 1
}
rows {
  id: "HomePublish_20"
  content: "下载星家园地图失败"
  switch: 1
}
rows {
  id: "HomePublish_21"
  content: "获取星家园数据失败"
  switch: 1
}
rows {
  id: "HomePublish_22"
  content: "下载星家园编辑地图失败"
  switch: 1
}
rows {
  id: "HomePublish_23"
  content: "获取星家园编辑数据失败"
  switch: 1
}
rows {
  id: "HomePublish_24"
  content: "描述文字不能空着哦"
  switch: 1
}
rows {
  id: "HomePublish_25"
  content: "星家园发布成功"
  switch: 1
}
rows {
  id: "HomePublish_26"
  content: "星家园发布失败"
  switch: 1
}
rows {
  id: "HomePublish_27"
  content: "家园重新发布，<ReconnectTime>{0}秒</>后将重新进入。"
  switch: 1
}
rows {
  id: "HomePublish_28"
  content: "家园封面及描述修改成功"
  switch: 1
}
rows {
  id: "HomePublish_29"
  content: "此家园版本已升级，即将返回大厅"
  switch: 2
}
rows {
  id: "HomePublish_30"
  content: "建议出生点摆放的高度与下方平台保持合适的距离，否则玩家进入家园后可能会被卡住"
  switch: 1
}
rows {
  id: "HomePublish_31"
  content: "继续发布"
  switch: 1
}
rows {
  id: "HomePublish_32"
  content: "重新调整"
  switch: 1
}
rows {
  id: "TencentVideo_1"
  content: "资源打开成功"
  switch: 1
}
rows {
  id: "TencentVideo_2"
  content: "资源打开失败，请稍后再试"
  switch: 1
}
rows {
  id: "TencentVideo_3"
  content: "支付失败，请稍后再试"
  switch: 1
}
rows {
  id: "TencentVideo_4"
  content: "刷新会员状态失败，请稍后再试"
  switch: 1
}
rows {
  id: "TencentVideo_5"
  content: "点播失败，请稍后再试"
  switch: 1
}
rows {
  id: "TencentVideo_6"
  content: "用户信息获取失败，请稍后重试"
  switch: 1
}
rows {
  id: "TencentVideo_7"
  content: "不同意"
  switch: 1
}
rows {
  id: "TencentVideo_8"
  content: "为了更好的保障您的个人权益，在使用本产品前，请您审慎阅读<a style=\"TecentVideoProtocolBlock\" mark=\"https://m.v.qq.com/about/privacy.html\">《腾讯视频用户服务协议》</>、<a style=\"TecentVideoProtocolBlock\" mark=\"https://privacy.qq.com/document/preview/3fab9c7fc1424ebda42c3ce488322c8a\">《腾讯视频隐私保护指引》</>以及 <a style=\"TecentVideoProtocolBlock\" mark=\"https://privacy.qq.com/policy/kids-privacypolicy\">《儿童隐私保护声明》</>，以了解我们如何收集、使用、存储、保护、对外提供您的个人信息以及您享有的相关权利。\n\n您可以通过<a style=\"TecentVideoProtocolBlock\" mark=\"https://privacy.qq.com/document/preview/18f3ac8cb4364ed8b2ba596a9bfe3111\">《腾讯视频己收集个人信息清单》</>和<a style=\"TecentVideoProtocolBlock\" mark=\"https://privacy.qq.com/document/preview/ba4294dc9d4a45a89f3d682eb07a489b\">《腾讯视频与第三方共享个人信息清单》</>了解我们收集和共享您个人信息的情况;此外，您可以通过<a style=\"TecentVideoProtocolBlock\" mark=\"https://privacy.qq.com/document/priview/7900b34b6f8248e08313cf3f1899ea37\">《腾讯视频接入第三方SDK目录》</>了解本产品接入的第三方SDK类服务商的具体信息。\n\n如您同意，请点击下方同意按钮使用当前登录元梦之星的QQ/微信账号登录对应的腾讯视频账号。"
  switch: 1
}
rows {
  id: "TencentVideo_9"
  content: "提示"
  switch: 1
}
rows {
  id: "TencentVideo_10"
  content: "点播后将关闭场景内其他已点播的腾讯视频，确认继续吗？"
  switch: 1
}
rows {
  id: "TencentVideo_11"
  content: "确定"
  switch: 1
}
rows {
  id: "TencentVideo_12"
  content: "取消"
  switch: 1
}
rows {
  id: "TencentVideo_13"
  content: "正在播放： %s"
  switch: 1
}
rows {
  id: "TencentVideo_14"
  content: "正在播放： 第%d集"
  switch: 1
}
rows {
  id: "TencentVideo_15"
  content: "正在使用流量播放视频，请注意"
  switch: 1
}
rows {
  id: "TencentVideo_16"
  content: "试玩模式下无法使用"
  switch: 1
}
rows {
  id: "TencentVideo_17"
  content: "播放失败，请检查网络设置"
  switch: 1
}
rows {
  id: "TencentVideo_18"
  content: "试看中，开通腾讯视频VIP免费看"
  switch: 1
}
rows {
  id: "TencentVideo_19"
  content: "版权原因，无法观看该视频"
  switch: 1
}
rows {
  id: "TencentVideo_20"
  content: "正在播放： %s  第%d集"
  switch: 1
}
rows {
  id: "TencentVideo_21"
  content: "请更新到最新版本的腾讯视频APP"
  switch: 1
}
rows {
  id: "TencentVideo_22"
  content: "直播已结束"
  switch: 1
}
rows {
  id: "TencentVideo_23"
  content: "该功能需下载安装腾讯视频APP"
  switch: 1
}
rows {
  id: "TencentVideo_24"
  content: "请选择登录腾讯视频的方式"
  switch: 1
}
rows {
  id: "TencentVideo_25"
  content: "以游戏账号登录"
  switch: 1
}
rows {
  id: "TencentVideo_26"
  content: "使用其他账号"
  switch: 1
}
rows {
  id: "TencentVideo_27"
  content: "请使用相同的账号登录腾讯视频APP"
  switch: 1
}
rows {
  id: "TencentVideo_28"
  content: "直播信号搜索中"
  switch: 1
}
rows {
  id: "TencentVideo_29"
  content: "暂不支持手机号登录，请切换QQ或微信登录腾讯视频APP"
  switch: 1
}
rows {
  id: "HomeEdit_1"
  content: "关闭"
  switch: 1
}
rows {
  id: "HomeEdit_2"
  content: "每隔5分钟"
  switch: 1
}
rows {
  id: "HomeEdit_3"
  content: "每隔10分钟"
  switch: 1
}
rows {
  id: "HomeEdit_4"
  content: "每隔20分钟"
  switch: 1
}
rows {
  id: "HomeEdit_5"
  content: "放松自在的小天地~"
  switch: 1
}
rows {
  id: "HomeEdit_6"
  content: "享受惬意时光~"
  switch: 1
}
rows {
  id: "HomeEdit_7"
  content: "快乐、温馨、又浪漫。"
  switch: 1
}
rows {
  id: "HomeEdit_8"
  content: "当前地图介绍包含敏感词，将被视为无效内容哦"
  switch: 1
}
rows {
  id: "HomeEdit_9"
  content: "地图介绍"
  switch: 1
}
rows {
  id: "HomeReturn"
  content: "你正处于匹配状态，无法返回广场哦"
  switch: 1
}
rows {
  id: "HomeBlock_1"
  content: "你被这位星宝屏蔽，无法进入他的家园哦"
  switch: 1
}
rows {
  id: "HomeBlock_2"
  content: "你屏蔽了这位星宝，无法邀请他哦"
  switch: 1
}
rows {
  id: "friend_AtXiaoWo"
  content: "家园"
  switch: 1
}
rows {
  id: "JS_ENDReport_Tips_1"
  content: "未完成"
  switch: 1
}
rows {
  id: "JS_ENDReport_Tips_2"
  content: "您已到达终点"
  switch: 1
}
rows {
  id: "JS_ENDReport_Tips_3"
  content: "确认返回广场吗？"
  switch: 1
}
rows {
  id: "JS_ENDReport_Tips_4"
  content: "现在退出将无法获得奖励，且排位模式还会导致扣分，确认返回广场吗？"
  switch: 1
}
rows {
  id: "JS_ENDReport_Tips_5"
  content: "团队总评第{0}名"
  switch: 1
}
rows {
  id: "JS_Start_Tips_1"
  content: "快速通关，为队伍争取更高名次！"
  switch: 1
}
rows {
  id: "JS_Start_Tips_2"
  switch: 1
}
rows {
  id: "Home_NoEnter_1"
  content: "你正处于匹配状态，无法前往家园哦"
  switch: 1
}
rows {
  id: "Home_NoEnter_2"
  content: "由于某种原因需要退出当前家园，请继续等待进入游戏"
  switch: 1
}
rows {
  id: "Home_NoEnter_3"
  content: "由于某种原因需要退出当前家园，请重新登录\n <ReconnectTime>{0}秒</>后自动返回"
  switch: 1
}
rows {
  id: "Home_NoEnter_4"
  content: "由于队伍成员取消匹配，即将返回广场\n <ReconnectTime>{0}秒</>后自动返回"
  switch: 1
}
rows {
  id: "Home_NoEnter_5"
  content: "继续等待"
  switch: 1
}
rows {
  id: "Home_NoEnter_6"
  content: "重新登录"
  switch: 1
}
rows {
  id: "Home_NoEnter_7"
  content: "返回广场"
  switch: 1
}
rows {
  id: "HomeJump_1"
  content: "星宝位置已失效，请稍后再试"
  switch: 1
}
rows {
  id: "HomeBluePrint_1"
  content: "您上次的装修图纸未保存，是否回到上次的编辑？"
  switch: 1
}
rows {
  id: "HomeBluePrint_2"
  content: "警告：使用该图纸将覆盖当前家园的装修，并重置摇钱树和农作物状态，确认使用吗？\n（请保存好当前的家园图纸再使用!）"
  switch: 1
}
rows {
  id: "HomeBluePrint_3"
  content: "发生错误，图纸保存失败"
  switch: 1
}
rows {
  id: "HomeBluePrint_4"
  content: "发生错误，图纸资源下载失败"
  switch: 1
}
rows {
  id: "HomeBluePrint_5"
  content: "发生错误，图纸应用失败"
  switch: 1
}
rows {
  id: "HomeBluePrint_6"
  content: "发生错误，历史记录还原失败"
  switch: 1
}
rows {
  id: "HomeBluePrint_7"
  content: "发生错误，图纸发布失败"
  switch: 1
}
rows {
  id: "HomeBluePrint_8"
  content: "您尚未拥有当前图纸包含的全部元件，暂时无法应用发布该图纸哦"
  switch: 1
}
rows {
  id: "HomeBluePrint_9"
  content: "应用图纸将覆盖当前的装修编辑，未保存成图纸的装修将无法找回，确认应用吗？"
  switch: 1
}
rows {
  id: "HomeBluePrint_10"
  content: "未解锁的元件不会加入结算菜单"
  switch: 1
}
rows {
  id: "HomeBluePrint_11"
  content: "未解锁的元件不会布置在场景中"
  switch: 1
}
rows {
  id: "HomeBluePrint_12"
  content: "该图纸包含您未解锁的元件，无法使用，请先升级家园"
  switch: 1
}
rows {
  id: "HomeBluePrint_13"
  content: "试玩图纸将退出装修，未保存成图纸的装修将无法找回，确定试玩吗？"
  switch: 1
}
rows {
  id: "HomeBluePrint_14"
  content: "请输入描述信息"
  switch: 1
}
rows {
  id: "HomeBluePrint_15"
  content: "已使用新图纸，如需还原之前的装修效果可前往发布记录查找"
  switch: 1
}
rows {
  id: "FurnitureChangeScaleTips"
  content: "缩放或镜像会影响家具交互的表现哦"
  switch: 1
}
rows {
  id: "HomePlan_1"
  content: "积木"
  switch: 1
}
rows {
  id: "HomePlan_2"
  content: "玩法"
  switch: 1
}
rows {
  id: "HomePlan_3"
  content: "自然"
  switch: 1
}
rows {
  id: "HomePlan_4"
  content: "建筑"
  switch: 1
}
rows {
  id: "HomePlan_5"
  content: "装扮"
  switch: 1
}
rows {
  id: "HomePlan_6"
  content: "生活"
  switch: 1
}
rows {
  id: "HomePlan_7"
  content: "图纸名不能为空"
  switch: 1
}
rows {
  id: "HomePlan_8"
  content: "图纸名不能超过15个字哦"
  switch: 1
}
rows {
  id: "HomePlan_9"
  content: "审核中"
  switch: 1
}
rows {
  id: "HomePlan_10"
  content: "图纸保存失败"
  switch: 1
}
rows {
  id: "HomePlan_11"
  content: "上传中…"
  switch: 1
}
rows {
  id: "HomePlan_12"
  content: "家园%s级解锁"
  switch: 1
}
rows {
  id: "HomePlan_13"
  content: "（未使用）"
  switch: 1
}
rows {
  id: "HomePlan_14"
  content: "官方图纸"
  switch: 1
}
rows {
  id: "HomePlan_15"
  content: "我的图纸"
  switch: 1
}
rows {
  id: "HomePlan_16"
  content: "创作家"
  switch: 1
}
rows {
  id: "HomePlan_17"
  content: "我的图纸"
  switch: 1
}
rows {
  id: "HomePlan_18"
  content: "家园图纸还原成功"
  switch: 1
}
rows {
  id: "HomePlan_19"
  content: "家园图纸还原失败"
  switch: 1
}
rows {
  id: "HomePlan_20"
  content: "是否清空结算菜单？"
  switch: 1
}
rows {
  id: "HomePlan_21"
  content: "需要配齐所需元件才能发布该图纸哦，是否前往购买？"
  switch: 1
}
rows {
  id: "HomePlan_22"
  content: "是否删除此图纸"
  switch: 1
}
rows {
  id: "HomePlan_23"
  content: "缺失元件已全部加入结算菜单"
  switch: 1
}
rows {
  id: "HomePlan_24"
  content: "图纸记录"
  switch: 1
}
rows {
  id: "HomePlan_25"
  content: "读取图纸"
  switch: 1
}
rows {
  id: "HomePlan_26"
  content: "保存图纸"
  switch: 1
}
rows {
  id: "HomePlan_27"
  content: "发布记录"
  switch: 1
}
rows {
  id: "HomePlan_28"
  content: "确定要用该图纸覆盖当前位置的图纸吗？"
  switch: 1
}
rows {
  id: "HomePlan_29"
  content: "确定要将该图纸保存吗？"
  switch: 1
}
rows {
  id: "HomePlan_30"
  content: "你还没有自己保存的方案哟~"
  switch: 1
}
rows {
  id: "HomePlan_31"
  content: "图纸改名成功"
  switch: 1
}
rows {
  id: "HomePlan_32"
  content: "图纸名不能为空"
  switch: 1
}
rows {
  id: "HomePlan_33"
  content: "图纸名超过七个字"
  switch: 1
}
rows {
  id: "HomePlan_34"
  content: "图纸封面不能为空"
  switch: 1
}
rows {
  id: "HomePlan_35"
  content: "警告：覆盖后原位置处的图纸将无法恢复，确定要覆盖吗?"
  switch: 1
}
rows {
  id: "HomePlan_36"
  content: "场景没有元件，无法保存"
  switch: 1
}
rows {
  id: "HomePlan_37"
  content: "已达最大数量，无法继续添加"
  switch: 1
}
rows {
  id: "HomePlan_38"
  content: "您已拥有该图纸的所有元件"
  switch: 1
}
rows {
  id: "HomePlan_39"
  content: "自动保存"
  switch: 1
}
rows {
  id: "HomePlan_40"
  content: "组队时无法试玩图纸"
  switch: 1
}
rows {
  id: "HomePlan_41"
  content: "开房间时无法试玩图纸"
  switch: 1
}
rows {
  id: "HomePlan_42"
  content: "匹配时无法试玩图纸"
  switch: 1
}
rows {
  id: "HomePlan_43"
  content: "试玩图纸时无法组队"
  switch: 1
}
rows {
  id: "HomePlan_44"
  content: "试玩图纸时无法开房间"
  switch: 1
}
rows {
  id: "HomePlan_45"
  content: "试玩图纸时无法匹配"
  switch: 1
}
rows {
  id: "HomePlan_46"
  content: "您和对方的版本不一致，无法前往"
  switch: 1
}
rows {
  id: "QQMusic_1"
  content: "不可以操作别人的音乐盒哦~"
  switch: 1
}
rows {
  id: "WelcomeDragonDefault"
  content: "可在装修中编辑文本~"
  switch: 1
}
rows {
  id: "HomePlayer_1"
  content: "家园玩家"
  switch: 1
}
rows {
  id: "HomePlayer_2"
  content: "家园里还没有玩家！"
  switch: 1
}
rows {
  id: "PianoMusic_1"
  content: "钢琴"
  switch: 1
}
rows {
  id: "PianoMusic_2"
  content: "古筝"
  switch: 1
}
rows {
  id: "PianoMusic_3"
  content: "唢呐"
  switch: 1
}
rows {
  id: "HomePhoto_1"
  content: "作品未审核 ID：%s"
  switch: 1
}
rows {
  id: "HomePhoto_2"
  content: "ID：%s"
  switch: 1
}
rows {
  id: "HomePhoto_3"
  content: "作者：%s"
  switch: 1
}
rows {
  id: "HomePhoto_4"
  content: "地图：%s"
  switch: 1
}
rows {
  id: "Farm_Tips_1"
  content: "1.浇水能加快植物生长\n2.有可能出现银光效果，获得被浇灌植物的幼苗\n3.还有可能出现金光效果，获得金色幼苗！"
  switch: 1
}
rows {
  id: "Farm_Tips_2"
  content: "今日通过浇水获得的幼苗(含邮件)已达上限"
  switch: 1
}
rows {
  id: "Farm_Tips_3"
  content: "今日通过收获获得的幼苗已达上限"
  switch: 1
}
rows {
  id: "Farm_Tips_4"
  content: "浇灌植物出现金光效果的星宝会在浇水记录中显示带有“金色皇冠”的特殊头像，出现银光效果则会显示“银色皇冠”。访客浇灌植物获得的奖励，家园主人会同样获得，并通过邮件发放。"
  switch: 1
}
rows {
  id: "Farm_Tips_5"
  content: "装修里有新幼苗可放置哦"
  switch: 1
}
rows {
  id: "Farm_Collection_Tips_1"
  content: "已收获%d个"
  switch: 1
}
rows {
  id: "Farm_Collection_Tips_2"
  content: "收获<OrangeBookInfoNum>%d</>个"
  switch: 1
}
rows {
  id: "Farm_Collection_Tips_3"
  content: "已完成"
  switch: 1
}
rows {
  id: "Farm_Collection_Tips_4"
  content: "1.进入装修模式，在自然-种植页签找到植物幼苗。\n2.将幼苗拖出元件栏，妥善摆放后发布家园。\n3.幼苗会自然生长，浇水能加快植物成熟。成熟后的植物可以通过“收获”得到该植物的果实元件。\n4.收获植物能完成图鉴任务并获得种植积分。\n5.种植积分能升级种植图鉴从而解锁更多幼苗。"
  switch: 1
}
rows {
  id: "Farm_WaterTips_1"
  content: "已成熟的农作物不能浇水哦"
  switch: 1
}
rows {
  id: "Farm_WaterTips_2"
  content: "农田干涸的时候才能浇水哦"
  switch: 1
}
rows {
  id: "Farm_UGC_Tips_1"
  content: "没有剩余元件了"
  switch: 1
}
rows {
  id: "Farm_UGC_Tips_2"
  content: "剩余元件数量不足"
  switch: 1
}
rows {
  id: "HomeWelcome_1"
  content: "超出字数上限，请修改后发布"
  switch: 1
}
rows {
  id: "HomeWelcome_2"
  content: "由于违规行为，当前无法修改欢迎词，截止日期为：{0}"
  switch: 1
}
rows {
  id: "HomeWelcome_3"
  content: "检测到敏感词，请修改后再发布"
  switch: 1
}
rows {
  id: "HomeWelcome_4"
  content: "欢迎词设置成功"
  switch: 1
}
rows {
  id: "HomeWelcome_5"
  content: "点击设置家园欢迎词"
  switch: 1
}
rows {
  id: "HomeMessageBoard_1"
  content: "超出字数上限，请修改后发布"
  switch: 1
}
rows {
  id: "HomeMessageBoard_2"
  content: "已达今日留言上限，请明天再来吧"
  switch: 1
}
rows {
  id: "HomeMessageBoard_3"
  content: "检测到敏感词，请修改后再发布"
  switch: 1
}
rows {
  id: "HomeMessageBoard_4"
  content: "由于违规行为，当前无法留言，截止日期为：{0}"
  switch: 1
}
rows {
  id: "HomeMessageBoard_5"
  content: "精选留言到达上限"
  switch: 1
}
rows {
  id: "HomeMessageBoard_6"
  content: "说说这里有什么有趣的地方"
  switch: 1
}
rows {
  id: "HomeMessageBoard_7"
  content: "取消精选"
  switch: 1
}
rows {
  id: "HomeMessageBoard_8"
  content: "精选留言到达上限"
  switch: 1
}
rows {
  id: "HomeMessageBoard_9"
  content: "删除"
  switch: 1
}
rows {
  id: "HomeMessageBoard_10"
  content: "举报"
  switch: 1
}
rows {
  id: "HomeMessageBoard_11"
  content: "确认要删除这条留言吗？"
  switch: 1
}
rows {
  id: "PortalTips"
  content: "是否前往<Orange28>{0}</>的星家园？"
  switch: 1
}
rows {
  id: "PortalTrialTips"
  content: "传送功能在发布后才能生效哦~"
  switch: 1
}
rows {
  id: "PortalTrialTips2"
  content: "在自己的家园才可以设置传送目的地哦~"
  switch: 1
}
rows {
  id: "PianoMusic_4"
  content: "拍照模式下无法和乐器交互"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff"
  content: "铲除后返还<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange28>{1}</>，确定铲除吗？"
  switch: 1
}
rows {
  id: "Farmyard_PackUp"
  content: "收纳后返还饲料费并<Orange28>重置状态</>，确定收纳么？"
  switch: 1
}
rows {
  id: "Farmyard_DoNotRemind"
  content: "今日不再提醒"
  switch: 1
}
rows {
  id: "Farmyard_ComingSoon"
  content: "更多内容，敬请期待~"
  switch: 1
}
rows {
  id: "Farmyard_LandLocked"
  content: "这块土地尚未开垦"
  switch: 1
}
rows {
  id: "Farmyard_LandOccupied"
  content: "需要在空土地上操作"
  switch: 1
}
rows {
  id: "Farmyard_NeedLand"
  content: "需要在开垦过的土地上操作"
  switch: 1
}
rows {
  id: "Farmyard_LandTypeError1"
  content: "作物需要播种在农田上"
  switch: 1
}
rows {
  id: "Farmyard_LandTypeError2"
  content: "动物需要养殖在牧场里"
  switch: 1
}
rows {
  id: "Farmyard_LandTypeError3"
  content: "加工原料需要投放在加工器里"
  switch: 1
}
rows {
  id: "Farmyard_NotEnoughMoney"
  content: "<Orange24>{0}</>不足，可在<Orange24>仓库</>出售农产品获得农场币"
  switch: 1
}
rows {
  id: "Farmyard_WaterTime"
  content: "水分还能维持{0}"
  switch: 1
}
rows {
  id: "Farmyard_FoodTime"
  content: "饲料还能维持{0}"
  switch: 1
}
rows {
  id: "Farmyard_Dry"
  content: "土地干涸，浇水能加快作物成熟"
  switch: 1
}
rows {
  id: "Farmyard_Hungary"
  content: "动物饥饿会暂停生产哦，请添加饲料"
  switch: 1
}
rows {
  id: "Farmyard_Hungary2"
  content: "<Gray19F>饥饿时暂停生产</>"
  switch: 1
}
rows {
  id: "Farmyard_Locked"
  content: "需要{0}级农场小屋才能养殖"
  switch: 1
}
rows {
  id: "Farmyard_FoodConsumption"
  content: "维持一次收获周期所需饲料"
  switch: 1
}
rows {
  id: "Farmyard_AnimalInventory"
  content: "剩余{0}"
  switch: 1
}
rows {
  id: "Farmyard_BreedMenu"
  content: "养殖菜单"
  switch: 1
}
rows {
  id: "Farmyard_SellAnimal"
  content: "出售一组<Orange28>{0}</>获得<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange28>{1}</>，确定出售么？"
  switch: 1
}
rows {
  id: "Farmyard_Text_1"
  content: "{0}的农场"
  switch: 1
}
rows {
  id: "Farmyard_Text_2"
  content: "<Orange19F>{0}</><Gray19F>后成熟</>"
  switch: 1
}
rows {
  id: "Farmyard_Text_2_2"
  content: "<Orange19F>{0}</><Gray19F>后收获</>"
  switch: 1
}
rows {
  id: "Farmyard_Text_3"
  content: "干涸"
  switch: 1
}
rows {
  id: "Farmyard_Text_3_2"
  content: "饥饿"
  switch: 1
}
rows {
  id: "Farmyard_Text_4"
  content: "<Gray19H>可收获</> <Orange19H>{0}/{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Text_5"
  content: "<Gray19F>农作物已成熟</>"
  switch: 1
}
rows {
  id: "Farmyard_Text_5_2"
  content: "<Gray19F>可收获</>"
  switch: 1
}
rows {
  id: "Farmyard_Blocked_1"
  content: "该农场存在违规行为，暂时无法进入哦"
  switch: 1
}
rows {
  id: "Farmyard_Blocked_2"
  content: "您存在违规行为，截止{0}前无法进入农场哦"
  switch: 1
}
rows {
  id: "Farmyard_Text_6"
  content: "当前玩家还未创建农场"
  switch: 1
}
rows {
  id: "Farmyard_Text_7"
  content: "您已经在当前农场中了"
  switch: 1
}
rows {
  id: "Farmyard_Text_8"
  content: "该农场的访客人数已达上限，暂时不能访问"
  switch: 1
}
rows {
  id: "Farmyard_Text_9"
  content: "永久"
  switch: 1
}
rows {
  id: "Farmyard_Text_10"
  content: "未获取农场信息，进入失败"
  switch: 1
}
rows {
  id: "Farmyard_Steal_1"
  content: "您今日的拿取次数已达上限，明天再拿吧"
  switch: 1
}
rows {
  id: "Farmyard_Steal_2"
  content: "您已经拿过这个啦，换个试试"
  switch: 1
}
rows {
  id: "Farmyard_Steal_3"
  content: "给主人留点吧……换个试试"
  switch: 1
}
rows {
  id: "Farmyard_Steal_4"
  content: "凌晨0点至早上8点不能拿取，好好休息吧"
  switch: 1
}
rows {
  id: "Farmyard_Steal_5"
  content: "对方是您的好友时才能拿取"
  switch: 1
}
rows {
  id: "Farmyard_Steal_6"
  content: "今日剩余拿取次数只有{0}次了，请注意"
  switch: 1
}
rows {
  id: "Farmyard_Steal_11"
  content: "{0},{1},{2}等来您农场拿取了东西"
  switch: 1
}
rows {
  id: "Farmyard_Steal_12"
  content: "您被农场主人请离后，10分钟内不能拜访该农场"
  switch: 1
}
rows {
  id: "Farmyard_Steal_13"
  content: "该农场访客人数已达上限"
  switch: 1
}
rows {
  id: "Farmyard_Steal_14"
  content: "您被赶走了，已返回自己的农场"
  switch: 1
}
rows {
  id: "Farmyard_Social_1"
  content: "其它星宝进入了农场，星能源信号正在切换多人环境"
  switch: 1
}
rows {
  id: "Farmyard_Social_2"
  content: "出现了神秘的网络波动，请重新进入场景"
  switch: 1
}
rows {
  id: "Farmyard_Social_3"
  content: "还没人来拿取或祈福过"
  switch: 1
}
rows {
  id: "Farmyard_Social_4"
  content: "您的有缘人还没有出现"
  switch: 1
}
rows {
  id: "Farmyard_Social_5"
  content: "您还没有玩过星宝农场的好友哦"
  switch: 1
}
rows {
  id: "FarmBuilding_1"
  content: "请选择您要出售的物品"
  switch: 1
}
rows {
  id: "FarmBuilding_2"
  content: "请设定您要出售的数量"
  switch: 1
}
rows {
  id: "FarmBuilding_3"
  content: "农场经验不足无法升级，通过收获农作物等方式获取更多经验吧"
  switch: 1
}
rows {
  id: "FarmBuilding_4"
  content: "需要农场小屋达到%d级"
  switch: 1
}
rows {
  id: "FarmBuilding_5"
  content: "农田产量提升："
  switch: 1
}
rows {
  id: "FarmBuilding_6"
  content: "获得农场经验："
  switch: 1
}
rows {
  id: "FarmBuilding_7"
  content: "<Orange24>农场币</>不足，可在<Orange24>仓库</>出售农产品获得农场币"
  switch: 1
}
rows {
  id: "FarmBuilding_8"
  content: "需要提升农场小屋等级"
  switch: 1
}
rows {
  id: "FarmBuilding_9"
  content: "解锁物品"
  switch: 1
}
rows {
  id: "FarmBuilding_10"
  content: "仓库"
  switch: 1
}
rows {
  id: "FarmBuilding_11"
  content: "农场小屋"
  switch: 1
}
rows {
  id: "FarmBuilding_12"
  content: "蔬菜摊"
  switch: 1
}
rows {
  id: "FarmBuilding_12_2"
  content: "动物小铺"
  switch: 1
}
rows {
  id: "FarmBuilding_13"
  content: "土地"
  switch: 1
}
rows {
  id: "FarmBuilding_14"
  content: "需要将农场小屋升至%d级才能开垦"
  switch: 1
}
rows {
  id: "FarmBuilding_15"
  content: "开垦土地需要支付农场币，确定开垦吗？"
  switch: 1
}
rows {
  id: "FarmBuilding_16"
  content: "解锁成功！"
  switch: 1
}
rows {
  id: "FarmBuilding_17"
  content: "解锁失败！"
  switch: 1
}
rows {
  id: "FarmBuilding_18"
  content: "蔬菜摊升到{0}级"
  switch: 1
}
rows {
  id: "FarmBuilding_18_2"
  content: "动物小铺升到{0}级"
  switch: 1
}
rows {
  id: "FarmBuilding_19"
  content: "土地升到{0}级"
  switch: 1
}
rows {
  id: "FarmBuilding_20"
  content: "蔬菜售卖倍率：{0}"
  switch: 1
}
rows {
  id: "FarmBuilding_20_2"
  content: "动物农产品售卖倍率：{0}"
  switch: 1
}
rows {
  id: "FarmBuilding_21"
  content: "拥有数量：<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}"
  switch: 1
}
rows {
  id: "FarmBuilding_22"
  content: "作物"
  switch: 1
}
rows {
  id: "FarmBuilding_23"
  content: "动物"
  switch: 1
}
rows {
  id: "FarmBuilding_23_2"
  content: "水产"
  switch: 1
}
rows {
  id: "FarmBuilding_24"
  content: "{0}级"
  switch: 1
}
rows {
  id: "FarmBuilding_25"
  content: "开垦"
  switch: 1
}
rows {
  id: "FarmBuilding_26"
  content: "农场小屋升至{0}级后可解锁"
  switch: 1
}
rows {
  id: "FarmBuilding_27"
  content: "本级效果"
  switch: 1
}
rows {
  id: "FarmBuilding_28"
  content: "本级解锁"
  switch: 1
}
rows {
  id: "FarmBuilding_29"
  content: "确定要出售所选农产品吗？"
  switch: 1
}
rows {
  id: "Farmyard_Intro1"
  content: "基础介绍"
  switch: 1
}
rows {
  id: "Farmyard_Intro1_Text"
  content: "打造丰富多彩的农场：种植农作物，养育可爱的动物们。\n用养殖所得换取金钱，还能每天去好友的农场搜刮一番哦。"
  switch: 1
}
rows {
  id: "Farmyard_Intro2"
  content: "种田"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_1"
  content: "播种"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_1_Text"
  content: "点击右下角“清单”按钮选择种子，\n在开垦过的农田上再次按下按钮即可播种。"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_2"
  content: "浇水"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_2_Text"
  content: "浇水能加快农作物成熟。"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_3"
  content: "收获"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_3_Text"
  content: "农作物成熟后，可收获至作物仓库并前往摊位售卖。\n周末时作物成熟可产出得更多农产品。"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_4"
  content: "铲除"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_4_Text"
  content: "不想要的农作物可以点击左侧“铲除”按钮铲除。"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_5"
  content: "作物等级"
  switch: 1
}
rows {
  id: "Farmyard_Intro2_5_Text"
  content: "每种农作物有自己的等级，收获该作物即可获得作物经验。\n作物等级越高，售价越高。"
  switch: 1
}
rows {
  id: "Farmyard_Intro3"
  content: "畜牧"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_1"
  content: "养殖"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_1_Text"
  content: "点击右下角“清单”按钮选择动物，\n开垦过的牧场上再次按下按钮即可养殖。"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_2"
  content: "喂食"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_2_Text"
  content: "喂食能让动物产出农产品。\n待收获的动物也会持续消耗饲料哦！"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_3"
  content: "助产"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_3_Text"
  content: "在动物进入待产状态后，可以助产。\n助产后可收获农产品！"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_4"
  content: "收获"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_4_Text"
  content: "可将动物产出的农产品收获至动物仓库，并前往动物小铺售卖。\n周末时段成熟能得到更多农产品。"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_5"
  content: "收纳"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_5_Text"
  content: "点击左侧“收纳”按钮可将动物收回至动物栏。\n被收纳的动物将清空成长状态哦！"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_6"
  content: "出售"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_6_Text"
  content: "对于存放在动物栏中的动物，可点击出售按钮低价卖出。"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_7"
  content: "动物等级"
  switch: 1
}
rows {
  id: "Farmyard_Intro3_7_Text"
  content: "收获农产品可获得相关动物经验，积累动物经验能提升动物等级。\n动物等级越高，产出的农产品售价越高。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4"
  content: "钓鱼"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_1"
  content: "撒饵"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_1_Text"
  content: "在鱼塘边点击右下角“钓鱼清单”按钮设置深度、\n鱼饵的种类和收获期，即可完成撒饵。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_2"
  content: "保护期"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_2_Text"
  content: "鱼塘进入收获期后会有一段保护期，保护期内好友无法拿取，\n可以点击左侧“X”按钮提前解除。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_3"
  content: "钓鱼"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_3_Text"
  content: "鱼塘进入收获期后，在鱼塘边点击右下角“钓鱼”按钮即可开始钓鱼，按钮亮起时要及时拉杆哦。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_4"
  content: "捕鱼"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_4_Text"
  content: "在好友的农场里，对着鱼塘丢出炸弹有可能捕到好友的鱼，但也有可能空手而归。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_5"
  content: "出售"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_5_Text"
  content: "捕获的水生物将存放在仓库，出售时注意勾选“普通”或“全部”，用以批量出售对应品质的鱼。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_6"
  content: "熟练度等级"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_6_Text"
  content: "捕获某种水生物或获得相应鱼卡都能提升该水生物的熟练度，\n熟练度将影响捕获水生物的重量或稀有鱼的出现概率。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_7"
  content: "水生物卡"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_7_Text"
  content: "捕鱼时有几率获得当前水层的鱼卡卡包，\n卡包中的鱼卡将增加对应水生物的熟练度经验值。"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_8"
  content: "图鉴"
  switch: 1
}
rows {
  id: "Farmyard_Intro4_8_Text"
  content: "在“钓鱼清单”中打开鱼类图鉴，可以查看捕获到的所有水生物，\n这里记录了它们的重量记录和熟练度等级等情况。"
  switch: 1
}
rows {
  id: "Farmyard_Intro5"
  content: "社交"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_1"
  content: "拜访好友"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_1_Text"
  content: "点击社交按钮，拜访星宝农场的好友；查看谁来过拿取过农产品；\n还可以前往陌生人农场转转。"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_2"
  content: "拿取"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_2_Text"
  content: "在好友农场里，靠近成熟果实点击“拿取”按钮，果实就归你啦。\n凌晨0点至早上8点为休息时段，不能拿取。"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_3"
  content: "驱逐"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_3_Text"
  content: "可以用尖叫鸡打飞来你农场里拿取果实的人。"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_4"
  content: "祈福"
  switch: 1
}
rows {
  id: "Farmyard_Intro5_4_Text"
  content: "可以对其它人的作物或者动物进行祈福，\n会有机会丰收或者大丰收哦，每日8点恢复祈福次数。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6"
  content: "建筑"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_1"
  content: "农场小屋"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_1_Text"
  content: "提升农场小屋等级能解锁作物、动物、可开垦土地、摊位及家具。\n收获农产品、升级摊位、开垦、升级土地，扩建小屋可获取农场经验。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_2"
  content: "蔬菜摊"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_2_Text"
  content: "蔬菜摊可售卖作物果实换取农场币。\n升级蔬菜摊可提升售卖价格。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_3"
  content: "动物小铺"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_3_Text"
  content: "动物小铺可售卖动物产品换取农场币。\n升级动物小铺可提升售卖价格。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_4"
  content: "水产摊"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_4_Text"
  content: "水产摊可售卖水生物换取农场币。\n升级水产摊可提升售卖价格。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_5"
  content: "鱼塘升级"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_5_Text"
  content: "鱼塘升级可以提升稀有水生物出现概率并解锁水层。\n钓鱼、升级鱼塘等级、升级鱼塘深度均可获取农场经验。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_6"
  content: "鱼缸"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_6_Text"
  content: "可以将捕获的水生物放入鱼缸观赏，\n解锁鱼缸的钥匙需要通过升级水产摊获得。"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_7"
  content: "无人机"
  switch: 1
}
rows {
  id: "Farmyard_Intro6_7_Text"
  content: "购买月卡后，可解锁无人机功能。\n无人机可实现农田与牧场的自动化工作。"
  switch: 1
}
rows {
  id: "Farmyard_Intro7"
  content: "土地"
  switch: 1
}
rows {
  id: "Farmyard_Intro7_1"
  content: "开垦土地"
  switch: 1
}
rows {
  id: "Farmyard_Intro7_1_Text"
  content: "升级农场小屋并消耗农场币，可以开垦更多农田或牧场。\n开垦土地能扩大养殖规模。"
  switch: 1
}
rows {
  id: "Farmyard_Intro7_2"
  content: "升级土地"
  switch: 1
}
rows {
  id: "Farmyard_Intro7_2_Text"
  content: "农田或牧场全部开垦完毕后，可对该类型土地进行升级。\n土地升级能提升作物或动物的产量。"
  switch: 1
}
rows {
  id: "Farmyard_Intro8"
  content: "加工"
  switch: 1
}
rows {
  id: "Farmyard_Intro8_1"
  content: "产物加工"
  switch: 1
}
rows {
  id: "Farmyard_Intro8_1_Text"
  content: "进入农场小屋解锁加工器，可对动植物产物进行加工，加工产物的售价更高哦。\n解锁购买更高等级的加工器，可以提升加工物产量。"
  switch: 1
}
rows {
  id: "Farmyard_Social_Button_1"
  content: "农场内无法使用该功能"
  switch: 1
}
rows {
  id: "Farmyard_Social_6"
  content: "<Orange19F>{0}</>来过，带走"
  switch: 1
}
rows {
  id: "Farmyard_Social_7"
  content: "<Orange19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_8"
  content: "加好友"
  switch: 1
}
rows {
  id: "Farmyard_Social_9"
  content: "头像"
  switch: 1
}
rows {
  id: "Farmyard_Social_10"
  content: "拜访"
  switch: 1
}
rows {
  id: "Farmyard_Warning_1"
  content: "出现了神秘的网络波动，请重新进入农场"
  switch: 1
}
rows {
  id: "Farmyard_Warning_2"
  content: "检测到您正在访问的农场版本较高，您将自动返回"
  switch: 1
}
rows {
  id: "Farmyard_Warning_3"
  content: "检测到您正在访问的农场版本较低，您将自动返回"
  switch: 1
}
rows {
  id: "Farmyard_Warning_4"
  content: "该农场有违规行为，暂时无法进入哦"
  switch: 1
}
rows {
  id: "Farmyard_ActionLocked"
  content: "农场目前不支持该动作"
  switch: 1
}
rows {
  id: "FarmWelcome_1"
  content: "超出字数上限，请修改后发布"
  switch: 1
}
rows {
  id: "FarmWelcome_2"
  content: "由于违规行为，当前无法修改寄语，截止日期为：{0}"
  switch: 1
}
rows {
  id: "FarmWelcome_3"
  content: "检测到敏感词，请修改后再发布"
  switch: 1
}
rows {
  id: "FarmWelcome_4"
  content: "寄语设置成功"
  switch: 1
}
rows {
  id: "FarmWelcome_5"
  content: "点击设置农场寄语"
  switch: 1
}
rows {
  id: "FarmWelcome_6"
  content: "暂时还没有设置寄语哦"
  switch: 1
}
rows {
  id: "FarmMessageBoard_1"
  content: "超出字数上限，请修改后发布"
  switch: 1
}
rows {
  id: "FarmMessageBoard_2"
  content: "已达今日留言上限，请明天再来吧"
  switch: 1
}
rows {
  id: "FarmMessageBoard_3"
  content: "检测到敏感词，请修改后再发布"
  switch: 1
}
rows {
  id: "FarmMessageBoard_4"
  content: "由于违规行为，当前无法留言，截止日期为：{0}"
  switch: 1
}
rows {
  id: "FarmMessageBoard_5"
  content: "和谐相处，友善发言"
  switch: 1
}
rows {
  id: "FarmMessageBoard_6"
  content: "删除"
  switch: 1
}
rows {
  id: "FarmMessageBoard_7"
  content: "举报"
  switch: 1
}
rows {
  id: "FarmMessageBoard_8"
  content: "确定要删除这条留言吗？"
  switch: 1
}
rows {
  id: "Farmyard_AffectPrice"
  content: "提升养殖物等级和商店等级，获得装饰可以提升售价"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_1"
  content: "您的祈福次数已用尽，<Orange24>每日8点</>会恢复"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_2"
  content: "您已经对该土地祈福过了"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_3"
  content: "已经是大丰收了，不能再祈福了"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_4"
  content: "你对<Orange24>{0}</>进行了祈福，但无事发生"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_5"
  content: "<Orange24>{0}</>收到了你的诚意，进入<Orange24>丰收</>状态！"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_6"
  content: "<Orange24>{0}</>感动到无以复加，进入<Orange24>大丰收</>状态！"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_7"
  content: "您今日的祈福次数还剩余<Orange24>{0}</>次，<Orange24>每日8点</>会恢复"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_11"
  content: "<Orange19F>{0} </>来过，给你的"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_12"
  content: "<Orange19F>{0}</>进行祈福，无事发生"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_13"
  content: "<Orange19F>{0}</>进行祈福，丰收了！"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_14"
  content: "<Orange19F>{0}</>进行祈福，大丰收了！"
  switch: 1
}
rows {
  id: "DroneIncompatible"
  content: "目前没有可以工作的地块"
  switch: 1
}
rows {
  id: "DroneMonthlyCard"
  content: "需要购买农场月卡才可以使用无人机功能哦"
  switch: 1
}
rows {
  id: "DronePlayerOffline"
  content: "离开农场后，无人机将停止工作"
  switch: 1
}
rows {
  id: "DronePastureUnlock"
  content: "需要在20级解锁牧场后才能使用功能哦"
  switch: 1
}
rows {
  id: "DroneCropSelection"
  content: "未选择播种作物"
  switch: 1
}
rows {
  id: "DroneFarmWork"
  content: "无人机开始前往农场工作"
  switch: 1
}
rows {
  id: "DroneRanchWork"
  content: "无人机开始前往牧场工作"
  switch: 1
}
rows {
  id: "DroneEndWork"
  content: "无人机结束工作，正在返回中"
  switch: 1
}
rows {
  id: "DroneBelowLevel3"
  content: "农场3级后，购买农场月卡才可以使用哦"
  switch: 1
}
rows {
  id: "DroneNoAnimals"
  content: "牧场内有动物或选中库存动物时，无人机才能工作哦"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_1"
  content: "一键轻松务农钓鱼"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_2"
  content: "农场无人机"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_3"
  content: "所有专属特权在月卡有效期间生效"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_4"
  content: "<MonthCardOrange>丰收</>产量从3倍提升至<MonthCardOrange>4倍</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_5"
  content: "<MonthCardOrange>大丰收</>产量从10倍提升至<MonthCardOrange>13倍</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_6"
  content: "收获时获得的<MonthCardOrange>农场经验</>提升<MonthCardOrange>15%</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_7"
  content: "社交列表中展示<MonthCardOrange>尊享V标志</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_8"
  content: "（1）成功购买农场月卡后，会激活30天有效期，可以享受下述专享福利。\n（2）月卡生效期间，可以解锁使用农场无人机，实现一键轻松务农，畜牧和钓鱼。\n（3）土地或者加工器变为可收获时如果月卡处于有效期内，则丰收后的收益从基础产量的3倍提升到4倍，大丰收后的收益从基础产量的10倍提升到13倍。\n（4）土地变为可收获时如果月卡处于有效期内，则收获时获得农场经验提升15%。解锁土地，升级建筑等获得农场经验不在此范围。钓鱼收杆获得鱼时，收获加工物时，餐厅顾客结账或收取离店收益时，如果月卡处于有效期内，则获得农场经验提升15%。\n（5）月卡生效期间，您好友的社交相关界面，以及左上角头像区域，会在您的名字后面显示尊享V标志。\n（6）月卡生效期间，在自己农场或别人农场进行钓鱼行为时，若获得卡包，则出现紫色卡包的概率会翻倍。\n（7）月卡可多次续费叠加，即每次续费增加30天有效时长。\n（8）月卡赠送规则：需要双方元梦之星游戏等级（非农场等级）达到5级才能操作（否则在列表中不会看到对方），赠送之后无法退回，请提醒好友尽快领取邮件，邮件过期即会消失。且受赠方领取邮件后，月卡会立刻自动生效，不需要在背包中使用。\n（9）月卡索要规则：同样需要双方游戏等级达到5级，且好友亲密度达到10并且对方允许好友索要礼物才可操作（否则在列表中不会看到对方）。其余邮件相关规则与赠送一致。"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_9"
  content: "<MoonCardBtnNum>%d</> <MoonCardBtnText>开通30天</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_10"
  content: "<MoonCardBtnNum>%d</> <MoonCardBtnText>续费30天</>"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_11"
  content: "农场月卡"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_12"
  content: "规则说明"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_13"
  content: "“%d天%d小时”"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_14"
  content: "小游戏内无法使用该功能，请前往游戏圈了解更多"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_15"
  content: "该功能暂未开启"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_16"
  content: "农场无人机"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_17"
  content: "使用期限：30天"
  switch: 1
}
rows {
  id: "Farmyard_Social_11"
  content: "已屏蔽该好友，您和对方已双向不能互访农场"
  switch: 1
}
rows {
  id: "Farmyard_Social_12"
  content: "你们的农场处于屏蔽状态，不能拜访哦"
  switch: 1
}
rows {
  id: "Farmyard_Social_13"
  content: "微信/QQ好友不能在游戏内修改备注"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_15"
  content: "显示全部记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_16"
  content: "显示拿取记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_17"
  content: "显示祈福记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_18"
  content: "<Orange19F>{0}</>来过，进行了"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_19"
  content: "祈福"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_20"
  content: "<Orange19F>{0}</>来过，带走"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_21"
  content: "一些东西"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_22"
  content: "拿取记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_23"
  content: "祈福记录"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_18"
  content: "0天1小时"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_1"
  content: "功能尚未开放，敬请期待~"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_2"
  content: "需要将农场小屋等级升至{0}级才能解锁哦"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_3"
  content: "畜牧功能{0}级才能解锁哦"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_4"
  content: "未开垦农田"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_5"
  content: "农田"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_6"
  content: "未开垦牧场"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_7"
  content: "牧场"
  switch: 1
}
rows {
  id: "Farm_PlantLevelUp"
  content: "售卖价格提升了！"
  switch: 1
}
rows {
  id: "Farm_AnimalLevelUp"
  content: "农产品售卖价格提升了！"
  switch: 1
}
rows {
  id: "Farm_Egg"
  content: "出现裂缝了！好期待能快点和它见面"
  switch: 1
}
rows {
  id: "Home_Prohibit_Editing"
  content: "欢迎来我的派对，享受快乐时光！"
  switch: 1
}
rows {
  id: "Farmyard_Social_14"
  content: "不能在游戏内删除微信/QQ好友"
  switch: 1
}
rows {
  id: "Farmyard_XumuText_8"
  content: "功能尚未开放，敬请期待~"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_24"
  content: "自己农场等级达到<Orange24>{0}</>才能祈福哦"
  switch: 1
}
rows {
  id: "FarmClockPlant"
  content: "<Orange19F>{0}</><Gray19F>成熟</>"
  switch: 1
}
rows {
  id: "FarmClockAnimal"
  content: "<Orange19F>{0}</><Gray19F>收获</>"
  switch: 1
}
rows {
  id: "FarmTomorrow"
  content: "明天"
  switch: 1
}
rows {
  id: "FarmDayAfterTomorrow"
  content: "后天"
  switch: 1
}
rows {
  id: "FarmDaysLater"
  content: "<Orange19F>{0}</><Gray19F>{0}天后</>"
  switch: 1
}
rows {
  id: "FarmClockSetting"
  content: "时间显示设置成功~"
  switch: 1
}
rows {
  id: "FarmClockAnimalCountdown"
  content: "<Orange19F>{0}</><Gray19F>后可助产</>"
  switch: 1
}
rows {
  id: "FarmClockAnimalTime"
  content: "<Orange19F>{0}</><Gray19F>可助产</>"
  switch: 1
}
rows {
  id: "FarmClockTitle1"
  content: "成熟时间显示"
  switch: 1
}
rows {
  id: "FarmClockTitle2"
  content: "牧场收获显示"
  switch: 1
}
rows {
  id: "Farmyard_Gift_1"
  content: "您的送礼次数已用尽，<Orange24>每日8点</>会恢复"
  switch: 1
}
rows {
  id: "Farmyard_Gift_2"
  content: "场景里已经有很多礼物了，先等主人领取吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_3"
  content: "对方今天已经收了太多礼物了，明天再来试试吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_4"
  content: "周围已经有礼物了，换个地方试试吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_5"
  content: "请在开阔的地面上放置礼物哦"
  switch: 1
}
rows {
  id: "Farmyard_Gift_6"
  content: "写下你想对ta说的话吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_7"
  content: "超出字数上限"
  switch: 1
}
rows {
  id: "Farmyard_Gift_8"
  content: "点击礼物盒按钮，放入礼物吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_9"
  content: "选择<Orange28H>1种</>送给Ta"
  switch: 1
}
rows {
  id: "Farmyard_Gift_10"
  content: "由于违规行为，当前无法修改赠言，截止日期为：{0}"
  switch: 1
}
rows {
  id: "Farmyard_Gift_11"
  content: "今天已经送过了，去其他人农场里试试吧"
  switch: 1
}
rows {
  id: "Farmyard_Gift_12"
  content: "对方游戏版本较低，暂时无法送礼"
  switch: 1
}
rows {
  id: "Farmyard_Gift_13"
  content: "只能领取自己农场的礼物哦"
  switch: 1
}
rows {
  id: "Farmyard_Time1"
  content: "{0}后即可助产成熟"
  switch: 1
}
rows {
  id: "Farmyard_ExchangeMoney_1"
  content: "是否使用{0}个{1}兑换{2}个{3}?"
  switch: 1
}
rows {
  id: "Farmyard_ExchangeMoney_2"
  content: "农场等级越高，{0}兑换{1}的比例越高"
  switch: 1
}
rows {
  id: "Farmyard_ExchangeMoney_3"
  content: "当前{0}：{1}"
  switch: 1
}
rows {
  id: "Farmyard_ExchangeMoney_4"
  content: "{0}不足"
  switch: 1
}
rows {
  id: "Farm_FishLevelUp"
  content: "鱼售卖价格提升了！"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_19"
  content: "是否消耗{0}星钻购买<YellowCurrency>30天农场月卡</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_15"
  content: "确定要取消本次<YellowCurrency>可拿取</>提醒么?对方农场如有变动会再次显示<YellowCurrency>可拿取</>提醒您。"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_0"
  content: "奖励详情"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_1"
  content: "包含<Orange26F>{0}</>张水生物卡\n高概率获得当前层<Orange26F>稀有、奇珍、金冠稀世</>水生物卡"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_2"
  content: "卡包产出：\n白色水生物、绿色水生物、蓝色水生物、稀有水生物、奇珍水生物、金冠稀世水生物的水生物卡\n水生物卡抽取概率：\n白色水生物卡获取概率为：35%\n绿色水生物卡获取概率为：28%\n蓝色水生物卡获取概率为：22%\n稀有水生物卡获取概率为：7%\n奇珍水生物卡获取概率为：5%\n金冠稀世水生物卡获取概率为：3%\n卡包说明：\n不同水层的卡仅包含当前水层所有水生物类\n任何途径获取到熟练度已满的水生物卡，将自动转化为农场币"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_1"
  content: "这种水生物的大小不匹配，无法放入"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_2"
  content: "没有选中要放入的水生物"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_3"
  content: "{0}的最佳纪录："
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_4"
  content: "钥匙数量不足，提升<Orange24>水产摊</>等级可以获得"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_3"
  content: "熟练度升级"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_4"
  content: "熟练度+{0}"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_5"
  content: "确定要消耗<Orange28F>{0}</>把钥匙<img id=\"T_Common_Icon_Coin_103\"></>解锁<Orange28F>{1}型鱼缸</>吗?"
  switch: 1
}
rows {
  id: "Farmyard_FishingProtect_1"
  content: "现在是鱼塘保护期，暂时无法捕鱼哦"
  switch: 1
}
rows {
  id: "Farmyard_FishingProtect_2"
  content: "取消后农场好友即可前来捕鱼\n确定取消吗？"
  switch: 1
}
rows {
  id: "Farmyard_FishingProtect_3"
  content: "已经撒过鱼饵了"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_6"
  content: "当前钥匙：{0}"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_7"
  content: "解锁"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_1"
  content: "已收集{0}种"
  switch: 1
}
rows {
  id: "Farmyard_Social_16"
  content: "该玩家不是您的好友"
  switch: 1
}
rows {
  id: "Farmyard_FishingCardLimit_1"
  content: "已达购买上限"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_5"
  content: "农场币数量不足！"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_6"
  content: "鱼饵数量不足！"
  switch: 1
}
rows {
  id: "Farmyard_Social_17"
  content: "还没有被农场屏蔽的好友"
  switch: 1
}
rows {
  id: "Farmyard_Social_18"
  content: "默认排序"
  switch: 1
}
rows {
  id: "Farmyard_Social_19"
  content: "等级升序"
  switch: 1
}
rows {
  id: "Farmyard_Social_20"
  content: "等级降序"
  switch: 1
}
rows {
  id: "Farmyard_FishingOpenLimit_1"
  content: "农场等级达到<Orange24>{0}</>才能解锁钓鱼哦"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_7"
  content: "哎呀!没有捕到鱼"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_8"
  content: "<Orange20F>{0}</>后鱼可收获，\n之后会有<Orange20F>{1}</>保护期"
  switch: 1
}
rows {
  id: "Farmyard_Social_21"
  content: "没有搜索到对应的好友"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_9"
  content: "请选择收获周期和鱼饵种类！"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_10"
  content: "请选择鱼饵!"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_11"
  content: "此处不可撒饵"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_20"
  content: "钓鱼获得<MonthCardOrange>紫色卡包</>概率<MonthCardOrange>翻倍</>"
  switch: 1
}
rows {
  id: "Farmyard_Steal_7"
  content: "剩余{0}"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_12"
  content: "此处不可撒饵,去岸边撒吧！"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_13"
  content: "此处不可捕鱼,去岸边捕鱼吧！"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_14"
  content: "此处不可钓鱼,去岸边钓吧！"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_2"
  content: "熟练度下一级效果：提升出现概率{0}\n等级越高，更重水生物出现概率越高"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_3"
  content: "熟练度越高，获得更大重量水生物概率越大"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_4"
  content: "奖励："
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_5"
  content: "太可惜了，鱼好像跑掉了"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_6"
  content: "太棒了！获得{0}！"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_7"
  content: "太棒了！第一次获得了{0}！"
  switch: 1
}
rows {
  id: "Farmyard_FishScale_1"
  content: "小型"
  switch: 1
}
rows {
  id: "Farmyard_FishScale_2"
  content: "中型"
  switch: 1
}
rows {
  id: "Farmyard_FishScale_3"
  content: "大型"
  switch: 1
}
rows {
  id: "Farmyard_FishScale_4"
  content: "超大型"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_16"
  content: "收起后返还<img id=\"T_Farmyard_Icon_ActivityFishBait\"></><Orange28>{1}</>，确定收起吗？"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_8"
  content: "还没有获得过这种水生物哦"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_9"
  content: "当前熟练度效果：提升出现概率{0}\n熟练度越高，获得更大重量水生物概率越大"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_10"
  content: "当前层所有水生物熟练度全满，确认还要抽取吗"
  switch: 1
}
rows {
  id: "Farmyard_FishingShow_1"
  content: "确认要出售<MantelCardCount>{0}</>吗"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_11"
  content: "钓起重量提升/出现概率提升"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_12"
  content: "钓起重量提升"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_13"
  content: "钓鱼意外中断了，本次获得<Orange24>{0}</>已进入仓库"
  switch: 1
}
rows {
  id: "Farmyard_FishingGuide_14"
  content: "钓鱼意外中断了，本次获得一包<Orange24>{0}</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingRecord_1"
  content: "<Orange19F>{0}</>来过，捕鱼失败，获得了一些农场币"
  switch: 1
}
rows {
  id: "Farmyard_FishingShowBan_1"
  content: "钓鱼的时候不能炫耀    "
  switch: 1
}
rows {
  id: "Farmyard_FishingText_17"
  content: "撒饵成功，请等待一段时间尽情收获"
  switch: 1
}
rows {
  id: "Farmyard_Loading_1"
  content: "周末产量翻倍，适合种植金币型作物"
  switch: 1
}
rows {
  id: "Farmyard_Loading_2"
  content: "有的作物产出金币多，有的产出经验多"
  switch: 1
}
rows {
  id: "Farmyard_Loading_3"
  content: "0点至8点不能拿取，这段时间你的作物很安全"
  switch: 1
}
rows {
  id: "Farmyard_Loading_4"
  content: "写句温馨的“寄语”，让好友帮你多多祈福吧"
  switch: 1
}
rows {
  id: "Farmyard_Loading_5"
  content: "稻草人处可以留言，写下想说的话吧"
  switch: 1
}
rows {
  id: "Farmyard_Loading_6"
  content: "多多拜访陌生人的农场，能找到优质的种菜搭子"
  switch: 1
}
rows {
  id: "Farmyard_Loading_7"
  content: "聊天频道发送“农场好友dd”，能收获超多农场好友"
  switch: 1
}
rows {
  id: "Farmyard_Loading_8"
  content: "通过时钟可以选择成熟时间的显示方式"
  switch: 1
}
rows {
  id: "Farmyard_Loading_9"
  content: "升级摊位能把作物卖出更高的价格"
  switch: 1
}
rows {
  id: "Farmyard_Loading_10"
  content: "升级农场小屋能解锁新的作物、增加可开垦的土地"
  switch: 1
}
rows {
  id: "Farmyard_Loading_11"
  content: "农场小屋到达规定等级可以解锁牧场、钓鱼等新玩法"
  switch: 1
}
rows {
  id: "Farmyard_Loading_12"
  content: "去好友的农场可以拿取成熟的作物，但每天有次数限制哦"
  switch: 1
}
rows {
  id: "Farmyard_ActionWarning_1"
  content: "当前状态下不支持该动作"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_18"
  content: "给主人留点吧……"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_19"
  content: "您在这个鱼塘的拿取次数已达上限，下次再来吧"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_20"
  content: "价格昂贵，前期易亏损，建议将本层鱼累积到一定熟练度等级后再使用"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_21"
  content: "价格非常昂贵，前期易产生较大亏损，建议将本层鱼累积到较高熟练度等级后再使用"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_22"
  content: "本次钓鱼意外中断，鱼已进入仓库或可钓次数已返还，请继续吧~"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_23"
  content: "升级水产摊到<Orange24>30</>级以上可以获得钥匙哦~"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_24"
  content: "确定要出售所选农产品吗?\n（包括<Purple28>稀有</>、<Orange28>奇珍、金冠稀世</>水生物）"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_25"
  content: "确定要出售所选农产品吗?"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_26"
  content: "每次钓鱼都会积累本层<Purple20F>稀有</>及<Orange20F>以上品质</>鱼的幸运加成，使用中级、高级鱼饵积累更快\n积累一定值后会获得<img id=\"T_FarmyardFish_Codex_img_luckStar\"></><Orange20F>幸运加成</>，<img id=\"T_FarmyardFish_Codex_img_luckStar\"></>越多该鱼出现概率越大\n获得该条鱼后幸运加成会重新积累"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_27"
  content: "未选择水生物"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_28"
  content: "钓鱼功能暂未开启"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_29"
  content: "钓鱼功能暂未开启"
  switch: 1
}
rows {
  id: "Farmyard_FishingText_30"
  content: "钓鱼功能暂未开启"
  switch: 1
}
rows {
  id: "Farmyard_HighExpCropRemind"
  content: "高经验养殖物可快速获得经验，但价格昂贵~建议在农场币充裕时购买。"
  switch: 1
}
rows {
  id: "Farmyard_HighCoinCropRemind"
  content: "高金币养殖物可快速获得农场币，但没有经验哦~建议在急缺农场币时购买。"
  switch: 1
}
rows {
  id: "Farmyard_HighExpRemind"
  content: "<Orange24>高经验</>养殖物农场币收益较低哦！"
  switch: 1
}
rows {
  id: "Farmyard_HighCoinRemind"
  content: "<Orange24>高金币</>养殖物经验收益很低哦！"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_1"
  content: "祈愿活动已结束"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_1"
  content: "<LightGray19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_2"
  content: "<Green19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_3"
  content: "<Blue19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_4"
  content: "<Purple19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_5"
  content: "<Orange19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishRarity_6"
  content: "<Orange19F>{0}个{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_2"
  content: "当前已开通星宝农场月卡<Yellow19F>{0}/{1}</>天"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_3"
  content: "升级建筑、获得装饰或藏品可提升售卖价格\n{0}提升：{1}\n装饰和藏品提升：{2}"
  switch: 1
}
rows {
  id: "HouseExpandTips1"
  content: "确定花费<MantelCardCount>%d</>农场币把小屋扩建至<MantelCardCount>%d</>平吗?"
  switch: 1
}
rows {
  id: "HouseExpandTips2"
  content: "尚未选择扩建区域"
  switch: 1
}
rows {
  id: "HouseExpandTips3"
  content: "扩建的区域需要在发布后才能生效哦"
  switch: 1
}
rows {
  id: "HouseExpandTips4"
  content: "尚未达到可扩建等级"
  switch: 1
}
rows {
  id: "HouseExpandTips5"
  content: "确认扩建"
  switch: 1
}
rows {
  id: "HouseExpandTips6"
  content: "扩建{0}平"
  switch: 1
}
rows {
  id: "HouseExpandTips7"
  content: "室内可扩建"
  switch: 1
}
rows {
  id: "HouseExpandTips8"
  content: "下一次扩建条件：小屋等级%d级"
  switch: 1
}
rows {
  id: "HouseExpandTips9"
  content: "已达到最大面积"
  switch: 1
}
rows {
  id: "HouseExpandTips10"
  content: "扩建"
  switch: 1
}
rows {
  id: "HouseExpandTips11"
  content: "是否花费<Orange28>{0}</>农场币扩建该区域？"
  switch: 1
}
rows {
  id: "HouseExpandGuestTips1"
  content: "当前小屋已进行扩建，请重新进入"
  switch: 1
}
rows {
  id: "HouseDecorationTips1"
  content: "当前位置不可放置家具哦"
  switch: 1
}
rows {
  id: "HouseDecorationTips2"
  content: "是否以当前方案装修小屋？"
  switch: 1
}
rows {
  id: "HouseDecorationTips3"
  content: "当前小屋已进行装修，请重新进入"
  switch: 1
}
rows {
  id: "HouseDecorationTips4"
  content: "装修需要发布才能生效，是否确认离开？"
  switch: 1
}
rows {
  id: "HouseDecorationTips5"
  content: "该物品已达到放置上限，场景内不可以再放置"
  switch: 1
}
rows {
  id: "HouseDecorationTips6"
  content: "收起后将停止加工并返还所有原料，是否确认收起？"
  switch: 1
}
rows {
  id: "FurniturePurchaseTips1"
  content: "你有尚未获得的家具哦，现在就去购买吗？"
  switch: 1
}
rows {
  id: "FurniturePurchaseTips2"
  content: "确认购买这些家具吗？"
  switch: 1
}
rows {
  id: "FurniturePurchaseTips3"
  content: "升级后解锁家具"
  switch: 1
}
rows {
  id: "FurniturePurchaseTips4"
  content: "确认购买这些家具吗？购买后将直接发布哦。"
  switch: 1
}
rows {
  id: "HouseUnlock"
  content: "需要{0}级农场小屋才能解锁"
  switch: 1
}
rows {
  id: "HouseGuestReEntry"
  content: "房主发布了小屋装修，请重新拜访吧"
  switch: 1
}
rows {
  id: "Farmyard_EnterText_1"
  content: "农场小屋即将开放，敬请期待"
  switch: 1
}
rows {
  id: "Farmyard_EnterText_2"
  content: "农场小屋室内10级后开放"
  switch: 1
}
rows {
  id: "WallpaperAlreadyHave"
  content: "已拥有"
  switch: 1
}
rows {
  id: "WallpaperAlreadyDecorate"
  content: "已装饰"
  switch: 1
}
rows {
  id: "DecoratePublishFail"
  content: "发布失败"
  switch: 1
}
rows {
  id: "Farmyard_Teleporter_1"
  content: "那里太远啦，找个近点的地方吧"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_8"
  content: "{0}/小时"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_9"
  content: "<Orange28>{0}</>后储蓄满"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_10"
  content: "已储蓄<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}\n已储蓄满"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_11"
  content: "已储蓄<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}\n<Orange20F>{1}</>后储蓄满"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_12"
  content: "已储蓄<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}\n<Orange20F>{1}</>后储蓄满"
  switch: 1
}
rows {
  id: "DroneFishingWork"
  content: "无人机前往鱼塘工作"
  switch: 1
}
rows {
  id: "DroneNoDefaultPlantFishSetting"
  content: "未选择鱼饵"
  switch: 1
}
rows {
  id: "DroneFishingPoolNotRipe"
  content: "鱼塘尚未达到可钓鱼时间"
  switch: 1
}
rows {
  id: "CanNotFishingWhenDroneFishing"
  content: "无人机捕鱼中，请勿操作~"
  switch: 1
}
rows {
  id: "StopFishingWhenDroneFishing"
  content: "无人机开始捕鱼，请勿操作~"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_1"
  content: "收纳后返还原材料并重置状态，确定要收纳么?"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_2"
  content: "{0}后收获"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_3"
  content: "请设定您要加工的数量"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_4"
  content: "收纳后会销毁加工产品并返还原材料且重置状态，确定要收纳么?"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_5"
  content: "确认返回农场？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_6"
  content: "当前设备仍有剩余容量，是否继续"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_7"
  content: "该设备可提前<Orange19F>{0}</>收取"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_8"
  content: "原材料加工后售价不受原等级影响"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_9"
  content: "<Gray19H>可收获 </><Orange19H>{0}</>"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_10"
  content: "加工设备可放置上限"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_11"
  content: "当前等级下只能购买<Orange28>{0}</>个<Orange28>{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_12"
  content: "\n确认将{0}个<Orange28F>{1}</>加工成{2}个<Orange28F>{3}</>么？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_13"
  content: "请先清空加工器"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_14"
  content: "{0}<Orange19F>{1}个</>"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_15"
  content: "您还可以放置更多加工器，确认要发布么？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_16"
  content: "场景中不能放置更多加工器"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_17"
  content: "{0}级可拥有下一个"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_18"
  content: "等级越高容量越大，当前可放置总数：{0}个"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_19"
  content: "加工"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_20"
  content: "小屋等级到达10级可解锁加工玩法"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_21"
  content: "当前加工物的数量小于加工器容量，是否继续放入加工？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_22"
  content: "原料"
  switch: 1
}
rows {
  id: "Farmyard_LevelUPTips1"
  content: "元梦之星游戏等级经验"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_13"
  content: "鱼类总产出:<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange20F>{0}</>/小时"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_14"
  content: "还没有{0}"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_15"
  content: "鱼位"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_16"
  content: "已全部解锁"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_17"
  content: "鱼缸将变为大型<Orange28>豪华水族箱</>，并增加<Orange28>产出农场币</>的功能，确认升级吗?\n （ 升级会<Orange28>返还</>鱼缸钥匙及鱼缸内的鱼）"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_18"
  content: "已拿出<Orange24>{0}</>获得产出<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange24>{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingBuildingMax"
  content: "已达最高级"
  switch: 1
}
rows {
  id: "HouseDecorationTips7"
  content: "拖动到需要放置的地方"
  switch: 1
}
rows {
  id: "HouseDecorationTips8"
  content: "点击放置生效"
  switch: 1
}
rows {
  id: "Land_Update_1"
  content: "请在空地时升级"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_19"
  content: "已产出<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}\n产出已达上限"
  switch: 1
}
rows {
  id: "FarmHouseReport_1"
  content: "由于违规行为，当前无法进入该小屋，截止日期为：{0}"
  switch: 1
}
rows {
  id: "FarmHouseReport_2"
  content: "检测到违规行为，暂时无法访问"
  switch: 1
}
rows {
  id: "Farmyard_HoldAnimalLimit"
  content: "请把动物放下再农作吧~"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_20"
  content: "待放入{0}鱼"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_21"
  content: "现在没有可以收集的农场币"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_22"
  content: "确定要消耗<Orange28F>{0}</>把钥匙<img id=\"T_Common_Icon_Coin_103\"></>解锁一个<Orange28F>{1}鱼位</>吗?"
  switch: 1
}
rows {
  id: "Farmyard_BaseText_1"
  content: "您已经在当前小屋中了"
  switch: 1
}
rows {
  id: "Farmyard_BaseText_2"
  content: "未获取小屋信息，进入失败"
  switch: 1
}
rows {
  id: "Farmyard_BaseText_3"
  content: "当前玩家还未创建小屋"
  switch: 1
}
rows {
  id: "Farmyard_BaseText_4"
  content: "该小屋的访客人数已达上限，暂时不能访问"
  switch: 1
}
rows {
  id: "Farmyard_BaseText_5"
  content: "{0}的小屋"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_23"
  content: "共{0}条鱼"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_24"
  content: "收益降序"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_25"
  content: "收益升序"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_21"
  content: "%d天"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_5"
  content: "这个建筑还没有解锁呢"
  switch: 1
}
rows {
  id: "HouseRevertRoom1"
  content: "请明确需要缩小的横向或纵向空间"
  switch: 1
}
rows {
  id: "HouseRevertRoom2"
  content: "是否确定缩小该区域？该区域的家具将自动返回家具列表，下次扩建不再消耗农场币。"
  switch: 1
}
rows {
  id: "HouseRevertRoom3"
  content: "房间面积修改需要在发布后才能生效哦。"
  switch: 1
}
rows {
  id: "HouseRevertRoom4"
  content: "该区域存在加工器，需要将加工器收起才能缩小该区域哦。"
  switch: 1
}
rows {
  id: "HouseRevertRoomUp"
  content: "是否确定缩小上面区域吗？该区域的家具将自动返回家具列表，下次扩建不再消耗农场币。"
  switch: 1
}
rows {
  id: "HouseRevertRoomLeft"
  content: "是否确定缩小左面区域吗？该区域的家具将自动返回家具列表，下次扩建不再消耗农场币。"
  switch: 1
}
rows {
  id: "HouseRevertRoomRight"
  content: "是否确定缩小右面区域吗？该区域的家具将自动返回家具列表，下次扩建不再消耗农场币。"
  switch: 1
}
rows {
  id: "HouseRevertRoom5"
  content: "缩小"
  switch: 1
}
rows {
  id: "HouseRevertRoom6"
  content: "目前没有可缩小区域"
  switch: 1
}
rows {
  id: "HouseRevertRoom7"
  content: "确认扩建该区域吗？重新扩建不消耗农场币。"
  switch: 1
}
rows {
  id: "FarmDroneProcessorWork"
  content: "无人机开始前往小屋门前工作"
  switch: 1
}
rows {
  id: "FarmDroneAllWork"
  content: "无人机开始执行已设置的工作"
  switch: 1
}
rows {
  id: "FarmDroneNoGoods"
  content: "未选择加工物"
  switch: 1
}
rows {
  id: "FarmDroneVegetables"
  content: "作物"
  switch: 1
}
rows {
  id: "FarmDroneAnimals"
  content: "动物"
  switch: 1
}
rows {
  id: "FarmDroneFish"
  content: "水产"
  switch: 1
}
rows {
  id: "FarmDroneProcessor"
  content: "加工"
  switch: 1
}
rows {
  id: "FarmDroneAll"
  content: "全部"
  switch: 1
}
rows {
  id: "FarmDroneFarmUnder10"
  content: "农场等级10级后开放加工器功能哦~"
  switch: 1
}
rows {
  id: "FarmDroneProcessorWorking"
  content: "加工器正在加工中"
  switch: 1
}
rows {
  id: "FarmDroneProcessorButton"
  content: "请靠近加工器或使用无人机放置加工物"
  switch: 1
}
rows {
  id: "FarmDroneChangeObject"
  content: "设置成功~"
  switch: 1
}
rows {
  id: "FarmDroneNoProcessor"
  content: "小屋内没有加工器"
  switch: 1
}
rows {
  id: "FarmDroneSelectAll"
  content: "无人机会根据你的设置执行全部地块的指令"
  switch: 1
}
rows {
  id: "Farmyard_FishShare"
  content: "当前无法分享"
  switch: 1
}
rows {
  id: "Farmyard_Pet_1"
  content: "<Orange19F>{0}</>来过，"
  switch: 1
}
rows {
  id: "Farmyard_Pet_2"
  content: "显示宠物记录"
  switch: 1
}
rows {
  id: "Farmyard_Pet_3"
  content: "守护农场"
  switch: 1
}
rows {
  id: "Farmyard_Pet_4"
  content: "<Orange19F>{0}</><Gray19F>后饥饿</>"
  switch: 1
}
rows {
  id: "Farmyard_Pet_5"
  content: "<Orange19F>{0}</><Gray19F>饥饿</>"
  switch: 1
}
rows {
  id: "Farmyard_Pet_6"
  content: "饥饿中…"
  switch: 1
}
rows {
  id: "Farmyard_Pet_7"
  content: "关闭看家模式后，宠物将无法驱逐来拿取的客人，是否确认关闭?"
  switch: 1
}
rows {
  id: "Farmyard_Pet_8"
  content: "开启看家模式后，宠物可以帮助驱逐来拿取的客人，是否确认开启?"
  switch: 1
}
rows {
  id: "Farmyard_Pet_9"
  content: "请前往狗窝旁投喂饲料"
  switch: 1
}
rows {
  id: "Farmyard_Pet_10"
  content: "狗狗即将到来，敬请期待"
  switch: 1
}
rows {
  id: "Farmyard_Pet_11"
  content: "宠物将在5级解锁"
  switch: 1
}
rows {
  id: "Farmyard_Pet_12"
  content: "宠物驱逐记录"
  switch: 1
}
rows {
  id: "Farmyard_Pet_13"
  content: "<Orange19F>{0}</>来过，和"
  switch: 1
}
rows {
  id: "Farmyard_Pet_14"
  content: "<Orange19F>{0}</>一起进行了"
  switch: 1
}
rows {
  id: "Farmyard_Pet_15"
  content: "祈福"
  switch: 1
}
rows {
  id: "Farmyard_Pet_16"
  content: "<Orange19F>{0}</>来过，给你的"
  switch: 1
}
rows {
  id: "Farmyard_Pet_17"
  content: "<Orange19F>{0}</>进行祈福，无事发生，"
  switch: 1
}
rows {
  id: "Farmyard_Pet_18"
  content: "<Orange19F>{0}</>进行祈福，丰收了，"
  switch: 1
}
rows {
  id: "Farmyard_Pet_19"
  content: "<Orange19F>{0}</>进行祈福，大丰收了，"
  switch: 1
}
rows {
  id: "Farmyard_Pet_20"
  content: "\n<Orange19F>{0}</>感到好奇，对你的"
  switch: 1
}
rows {
  id: "Farmyard_Pet_21"
  content: "<Orange19F>{0}</>也进行了祈福，无事发生"
  switch: 1
}
rows {
  id: "Farmyard_Pet_22"
  content: "<Orange19F>{0}</>也进行了祈福，丰收了！"
  switch: 1
}
rows {
  id: "Farmyard_Pet_23"
  content: "<Orange19F>{0}</>也进行了祈福，大丰收了！"
  switch: 1
}
rows {
  id: "Farmyard_Pet_24"
  content: "<Orange19F>{0}</>将TA赶走了"
  switch: 1
}
rows {
  id: "DecoratePublishNetworkError"
  content: "网络已断开，请稍后发布"
  switch: 1
}
rows {
  id: "FarmDroneProcessorShortage"
  content: "仓库加工物不足"
  switch: 1
}
rows {
  id: "HouseDecorationTips9"
  content: "限时活动获取"
  switch: 1
}
rows {
  id: "HouseDecorationTips10"
  content: "当前没有剩余的家具了哦"
  switch: 1
}
rows {
  id: "HouseDecorationTips11"
  content: "参与活动获取"
  switch: 1
}
rows {
  id: "HouseDecorationTips12"
  content: "需要靠近并面对墙壁装饰"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Ignore"
  content: "狗狗现在不想互动"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_26"
  content: "农场等级到达{0}级后解锁"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_27"
  content: "默认排序"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_28"
  content: "解锁成功"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_29"
  content: "放入成功"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_30"
  content: "您的领取频率太快了，每五分钟可以领取一次哦"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_31"
  content: "水族箱功能将在水产摊50级开启"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_6"
  content: "活动已结束"
  switch: 1
}
rows {
  id: "Farmyard_Collection_1"
  content: "拥有后获得对应小屋家具（已发放）"
  switch: 1
}
rows {
  id: "Farmyard_Collection_2"
  content: "藏品"
  switch: 1
}
rows {
  id: "Farmyard_Collection_3"
  content: "时间排序"
  switch: 1
}
rows {
  id: "Farmyard_Collection_4"
  content: "品质排序"
  switch: 1
}
rows {
  id: "Farmyard_Collection_5"
  content: "拥有后根据等级获得一定数量的{0}（已发放）"
  switch: 1
}
rows {
  id: "Farmyard_Social_OnlineTips_1"
  content: "农场小屋"
  switch: 1
}
rows {
  id: "Farmyard_Social_OnlineTips_2"
  content: "星宝农场"
  switch: 1
}
rows {
  id: "Farmyard_Social_Setting_1"
  content: "拜访后取消可拿取提醒"
  switch: 1
}
rows {
  id: "Farmyard_Pet_CannotInteraction"
  content: "需要先把手上的事儿做完再和宠物互动~"
  switch: 1
}
rows {
  id: "Farmyard_PackUp1"
  content: "收纳后返还饲料费并重置状态，是收纳<Orange19F>此地格</>还是收纳所有相连且未成熟的<Orange28>{0}</>？"
  switch: 1
}
rows {
  id: "Farmyard_PackUp2"
  content: "收纳当前"
  switch: 1
}
rows {
  id: "Farmyard_PackUp3"
  content: "收纳所有"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_1"
  content: "没有符合的好友"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_2"
  content: "邀请好友"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_3"
  content: "请求祈愿"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_4"
  content: "邀请已发送"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_5"
  content: "邀请失败"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_6"
  content: "邀请已发送"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_7"
  content: "邀请失败"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_8"
  content: "你是我的幸运大神，为你比心，帮我农场祈福获得了丰收"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_9"
  content: "你是我的幸运大神，为你比心，帮我农场祈福获得了大丰收"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_10"
  content: "邀请已发送"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_11"
  content: "游戏等级{0}级解锁"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_12"
  content: "消息已发送"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_13"
  content: "发送失败"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_14"
  content: "是否在QQ里邀请{0}加入农场？"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_15"
  content: "下次不再提醒"
  switch: 1
}
rows {
  id: "Farmyard_FriendsText_16"
  content: "每天只能邀请一次哦"
  switch: 1
}
rows {
  id: "Farmyard_LevelLock"
  content: "农场小屋需要到达{0}级才能养殖"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext"
  content: "100级后农场新能力即将上线，敬请期待！"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_32"
  content: "您共消耗了<Orange28>{0}</>把钥匙，确定要重置吗？\n重置后鱼、钥匙和储蓄农场币都会返还，每次重置需间隔<Orange28>7</>日"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_33"
  content: "重置"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_34"
  content: "<Orange21F>{0}</>后可重置"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_35"
  content: "当前无法重置，每次重置需间隔7日"
  switch: 1
}
rows {
  id: "Farmyard_Collection_6"
  content: "获得于"
  switch: 1
}
rows {
  id: "HouseDecorationTips_378"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "HouseDecorationTips_10681"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmDroneAllSetting"
  content: "请点击大按钮设置无人机需要执行的工作"
  switch: 1
}
rows {
  id: "Farmyard_Collection_7"
  content: "当前场景内还有<Orange28>藏品</>未拾取，超过一定时间后会<Orange28>消失</>，确定要离开吗？"
  switch: 1
}
rows {
  id: "Farmyard_PackUp4"
  content: "确认收纳所有相连且未成熟的<Orange28>{0}</>么？"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_1"
  content: "下小雨，<Orange20F>{0}</>雨停，下雨期间\n成熟，会有<Orange20F>{1}</>的安全保护期。"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_2"
  content: "{0}"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_3"
  content: "雷电预警"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_4"
  content: "拿取可能被雷击"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_5"
  content: "下暴雨，<Orange20F>{0}</>雨停，下雨期间\n成熟，会有<Orange20F>{1}</>的安全保护期。"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_6"
  content: "雷击预警，偷菜会被雷击！"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_7"
  content: "当前正处于保护期，暂时不能偷取哦"
  switch: 1
}
rows {
  id: "FarmDroneAllSettingTips"
  content: "没有可以工作的地块或农场币不足"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_23"
  content: "当前可放置{0}{1}，是否确认发布"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_24"
  content: "{0}个{1}级加工器"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_8"
  content: "取消保护后，作物可被农场好友拿取"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_9"
  content: "取消全部"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_10"
  content: "取消当前"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_1"
  content: "<Orange24F>%s前%s</>钓鱼结束，<Orange24F>%s</>鱼饵消耗<Orange24F>%s</>，钓了<Orange24F>%d杆</>%s，价值<Orange24F>%s</>的鱼，耗时<Orange24F>%s</>获得<Orange24F>%s</>农场经验（折算后每小时<Orange24F>%s</>农场经验）"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_2"
  content: "撒下了<Orange24F>%s</><Orange24F>%s</><Orange24F>%s</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_3"
  content: "撒下了<Orange24F>%s</><Orange24F>%s</>，花费了<Orange24F>%s</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_4"
  content: "钓上了重量为<Orange24F>%s%s级</>的%s，价值<Orange24F>%s</>农场币，获得<Orange24F>%s</>农场经验"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_5"
  content: "钓上了%s，提升%s<Orange24F>%d点</>熟练度，获得<Orange24F>%s</>农场经验"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_6"
  content: "钓上了%s，提升%s<Orange24F>%d点</>熟练度，获得<Orange24F>%s</>农场经验，多余熟练度转为<Orange24F>%s农场币</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_7"
  content: "<Orange24F>%s</>来过，带走了1个%s，你获得了<Orange24F>%s</>农场经验"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_8"
  content: "总共<Orange20F>%d杆</>，其中被拿取<Orange20F>%d杆</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_9"
  content: "包含共<Orange20F>%d</>条鱼，价值<Orange20F>%s</>，卡包熟练度转换价值<Orange20F>%s</>"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_10"
  content: "包含<Orange20F>%d</>条奇珍鱼，<Orange20F>%d</>条金冠稀世鱼"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_11"
  content: "，鱼卡合计提升了<Orange24F>%s%s</>熟练度"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_12"
  content: "，卡包熟练度转换<Orange24F>%s</>农场币"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_11"
  content: "天降甘霖，作物感动到无以复加！产量提升了！"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_7"
  content: "农场主人已更换农场场景，重新载入完成"
  switch: 1
}
rows {
  id: "Farmyard_Proficiency"
  content: "熟练度:{0}"
  switch: 1
}
rows {
  id: "Farmyard_Intimacy"
  content: "亲密度{0}"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff1"
  content: "铲除后返还90%种植费用，是铲除<Orange19F>此地格</>还是铲除所有相连且未成熟的<Orange19F>{0}</>？"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff2"
  content: "铲除当前"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff3"
  content: "铲除所有"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff4"
  content: "确认铲除所有相连且未成熟的<Orange19F>{0}</>么？"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_8"
  content: "拥有外观即可享有属性加成"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_9"
  content: "该外观无属性加成"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_1"
  content: "宠物选择"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_2"
  content: "未拥有"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_3"
  content: "已拥有"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_4"
  content: "收回宠物"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_5"
  content: "放出宠物"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_6"
  content: "更换宠物"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_7"
  content: "宠物已放出"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_8"
  content: "宠物更换成功"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_9"
  content: "收回的宠物将不出现在农场内，确定要将宠物收回吗？"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_10"
  content: "宠物已收回"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_11"
  content: "<Orange19F>{0}</>后可改名"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_12"
  content: "<Orange19F>{0}</>可改名"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_13"
  content: "名字可用"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_14"
  content: "不可填写该名字"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_15"
  content: "含有中文、数字、英文以外的内容"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_16"
  content: "宠物名字超出限制，请修改"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_17"
  content: "宠物被呼唤过来咯~"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_18"
  content: "是否使用当前预览中的宠物及装饰？"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_19"
  content: "更名成功"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_1"
  content: "套装"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_2"
  content: "头饰"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_3"
  content: "颈饰"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_4"
  content: "身体"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_5"
  content: "已拥有"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_6"
  content: "穿戴"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_7"
  content: "脱下"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_8"
  content: "活动获得"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_9"
  content: "获得"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_10"
  content: "前往获得"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_11"
  content: "暂未拥有该宠物，无法穿戴"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_12"
  content: "已穿戴"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_13"
  content: "已脱下"
  switch: 1
}
rows {
  id: "Farmyard_Pet_Clothes_14"
  content: "敬请期待"
  switch: 1
}
rows {
  id: "Farmyard_PetNameReport"
  content: "由于违规行为，当前无法修改宠物呢称，截止日期为: {0}"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_4"
  content: "宠物祈愿"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_4_Text"
  content: "有人来你的农场祈愿时，宠物会有概率对你的田地进行祈愿。"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_5"
  content: "好感度"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_5_Text"
  content: "投喂宠物、和宠物进行互动，可以增加宠物的好感度哦。"
  switch: 1
}
rows {
  id: "Farmyard_Proficiency_Max"
  content: "熟练度:已满"
  switch: 1
}
rows {
  id: "FarmDroneSpecialPlants"
  content: "当前作物需要手动种植哦~"
  switch: 1
}
rows {
  id: "FarmHouseProcessorError"
  content: "加工器内有加工物，无法拿起"
  switch: 1
}
rows {
  id: "FarmHouseRevertError"
  content: "移开加工器并重新进入装修才可以缩小当前区域哦~"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_1"
  content: "限种{0}个"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext_1"
  content: "确定要获得该仙术吗"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_5"
  content: "当前正处于保护期，暂时不能偷取哦"
  switch: 1
}
rows {
  id: "Farmyard_TalentTab_1"
  content: "作物"
  switch: 1
}
rows {
  id: "Farmyard_TalentTab_2"
  content: "动物"
  switch: 1
}
rows {
  id: "Farmyard_TalentTab_3"
  content: "水产"
  switch: 1
}
rows {
  id: "Farmyard_TalentTab_4"
  content: "加工"
  switch: 1
}
rows {
  id: "Farmyard_TalentTab_5"
  content: "特殊"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icon"
  content: "需解锁"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icontoast_1"
  content: "需解锁{0}"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icontoast_2"
  content: "需要先达到{0}{1}级"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icon_1"
  content: "获得"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_2"
  content: "吞食周围作物的售价越高，霸王吞吞花升级越快\n霸王吞吞花不受周末双倍、丰收及大丰收等事件影响\n每次种植从1级开始，当前最高成长等级<Orange21F>{0}</>级"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_3"
  content: "成年期开始自动吞食周围成熟作物<Orange21F>(最多{0})</>\n成年期结束后进入<Orange21F>{1}</>保护期\n不受时光跳跃、缓时咒、妙手回春等仙术的影响"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_4"
  content: "周围{0}格"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_toast_1"
  content: "同时只能种植一株霸王吞吞花"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_IconLevel"
  content: "{0}级"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_37"
  content: "<img id=\"T_Farmyard_Icon_Exp\"></><Orange20F>{0}</>/小时"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icon_2"
  content: "未达成条件"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icontoast_3"
  content: "还没有达到条件哦"
  switch: 1
}
rows {
  id: "Farmyard_FishingReport_Text_13"
  content: "包含<Orange20F>%d</>条奇珍鱼，<Orange20F>%d</>条金冠稀世鱼，<Orange20F>%d</>个珍宝"
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_1"
  content: "<Orange24>{0}</>不足，请前往神农宝典获取"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_1"
  content: "成年期开始自动吞食周围成熟作物升级\n吞食的目标售价越高，成长越快\n吞吞花等级越高收获果实越多"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_2"
  content: "本次成长进度："
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_3"
  content: " <img id=\"T_Farmyard_Float_FarewellExp\" width=\"30\" height=\"30\"/>{0}/{1}"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_4"
  content: "当前收获："
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_5"
  content: "<img id=\"Icon_Farm_PA_OverlordFlower_001_B2\"></>{0}"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_6"
  content: "下一级收获："
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_7"
  content: "<img id=\"Icon_Farm_PA_OverlordFlower_001_B2\"></>{0}"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_8"
  content: "当前售价："
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_9"
  content: "<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_10"
  content: "<Orange19F>{0}</>后成年"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_11"
  content: "<Orange19F>{0}</>后成熟"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_12"
  content: "<Orange19F>{0}</><Gray19F>成年</>"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_13"
  content: "<Orange19F>{0}</><Gray19F>成熟</>"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_14"
  content: "已被吞食的作物将<Orange28>不会返还</>，确认要铲除作物吗？"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_State_15"
  content: "确认要铲除作物吗"
  switch: 1
}
rows {
  id: "Farmyard_AquariumExp_1"
  content: "已拿出<Orange24>{0}</>获得产出<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange24>{1}</>和储蓄的农场经验<img id=\"T_Farmyard_toast_Icon_Exp\"></><Orange24>{2}</>"
  switch: 1
}
rows {
  id: "FarmDroneAllSettingWrong"
  content: "请在设置按钮中更改无人机的工作内容"
  switch: 1
}
rows {
  id: "Farmyard_Packaging_Text_1"
  content: "当前需要星宝下载<Orange28>{0}</>的<Orange28>{1}</>，当前处于WiFi环境，是否立即下载？"
  switch: 1
}
rows {
  id: "Farmyard_Packaging_Text_2"
  content: "当前需要星宝下载<Orange28>{0}</>的<Orange28>{1}</>，当前处于流量环境，是否立即下载？"
  switch: 1
}
rows {
  id: "Farmyard_Packaging_Text_3"
  content: "资源正在下载中，请等待下载完成后再点击"
  switch: 1
}
rows {
  id: "Farmyard_Packaging_Text_4"
  content: "当前农场中有未下载资源，请先退出农场下载，下载完成后可再次进入该农场"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_20"
  content: "名字不能为空"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_21"
  content: "新名称和原名重复"
  switch: 1
}
rows {
  id: "Farmyard_Aquarium_36"
  content: "存取水生物过于频繁，请稍后再试"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext_2"
  content: "确定要学习{0}吗？"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext_3"
  content: "确定要学习{0}{1}级吗？"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext_4"
  content: "{0}{1}级"
  switch: 1
}
rows {
  id: "Farmyard_Talenttext_5"
  content: "{0}{1}级 <img id=\"T_FarmTalentSystem_ArrowTitle\"></> {2}级"
  switch: 1
}
rows {
  id: "Farmyard_Social_FishStolen"
  content: "，你获得<Orange19F>{0}</>农场经验"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_4"
  content: "无人机正在工作中"
  switch: 1
}
rows {
  id: "Farmyard_IntimacyMax"
  content: "亲密度:已满"
  switch: 1
}
rows {
  id: "Farmyard_Intro9"
  content: "宠物"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_1"
  content: "喂养"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_1_Text"
  content: "给宠物投喂充足的食物能保持它的活力。\n宠物饥饿时会趴下不动，不再守护农场，也不会陪主人玩耍。"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_2"
  content: "玩耍"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_2_Text"
  content: "宠物们逗趣又可爱，喜欢它们的话可以点击互动按钮跟它们玩耍哦。"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_3"
  content: "守护农场"
  switch: 1
}
rows {
  id: "Farmyard_Intro9_3_Text"
  content: "来农场大肆拿取农产品的人会被宠物盯上并赶走。\n如果这时你不想被好友的宠物攻击，可以扔出骨头吸引它的注意。"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_toast_2"
  content: "神农宝典尚未开启"
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_2"
  content: "农田经验提升"
  switch: 1
}
rows {
  id: "FarmBuilding_LevelTitle"
  content: "需要农场等级达到"
  switch: 1
}
rows {
  id: "HouseDecorationTips_450"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "Farmyard_NPCFishing_1"
  content: "伊芙无法前往当前已解锁的最高水层哦"
  switch: 1
}
rows {
  id: "Farmyard_NPCFishing_2"
  content: "伊芙只能帮助您获取之前水层的鱼，无法前往当前解锁的<Orange24F>最高水层</>；根据您当前解锁水层和伊芙前往水层的深度差距不同，单次获得的鱼的数量不同，差距越大，鱼越多。"
  switch: 1
}
rows {
  id: "HouseDecorationTips15"
  content: "农场小屋需要到达{0}级才能解锁"
  switch: 1
}
rows {
  id: "HouseDecorationTips_400"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmBuilding_check1"
  content: "需要将农场小屋升至%d级才能建造"
  switch: 1
}
rows {
  id: "FarmBuilding_check2"
  content: "建造小屋需要支付农场币，确定建造吗？"
  switch: 1
}
rows {
  id: "Farm_Fish_SellAll"
  content: "确定要出售所选农产品吗?\n（包括<Purple28>稀有</>、<Orange28>奇珍、金冠稀世</>水生物及<Orange28>珍宝</>）"
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_3"
  content: "当前大地精华不足\n请前往神农宝典里学习<Orange28>聚灵术</>或<Orange28>生灵术</>后获取"
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_4"
  content: "前往宝典"
  switch: 1
}
rows {
  id: "Farm_FishBook_Top_Click_Tip1"
  content: "请前往学习【%s】阶段的<Orange28>鱼群聚宝术</>"
  switch: 1
}
rows {
  id: "Farm_FishBook_Top_Click_Tip2"
  content: "主人尚未学习鱼群聚宝术"
  switch: 1
}
rows {
  id: "Farm_PlantNumLevelUp"
  content: "收获数量提升了！"
  switch: 1
}
rows {
  id: "Farm_AnimalNumLevelUp"
  content: "农产品收获数量提升了！"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icontoast_4"
  content: "需解锁{0}{1}级"
  switch: 1
}
rows {
  id: "Farmyard_Collection_8"
  content: "炫耀藏品时，不能与宠物互动"
  switch: 1
}
rows {
  id: "HouseDecorationTips_449"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "Farmyard_ShovelOff5"
  content: "已为您保留成熟作物"
  switch: 1
}
rows {
  id: "Farmyard_PackUp5"
  content: "已为您保留成熟动物"
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_5"
  content: "获得农场经验："
  switch: 1
}
rows {
  id: "Farmyard_OutputGround_Text_1"
  content: "牧场产量提升："
  switch: 1
}
rows {
  id: "Farmyard_ExpGround_Text_6"
  content: "农场经验提升："
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_1"
  content: "需要至少放入两只相同种类和重量的水生物"
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_2"
  content: "缺少进行融合的水生物，无法融合"
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_3"
  content: "暂未解锁"
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_4"
  content: "融合数量不足"
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_5"
  content: "确认将<Orange28>%d条%s级%s</>融合为<Orange28>%d条%s级%s</>?"
  switch: 1
}
rows {
  id: "Farmyard_FishComposition_6"
  content: "该水生物已是最高重量级，无法继续融合"
  switch: 1
}
rows {
  id: "Farm_FishBook_TopTip"
  content: "本层当前15级熟练度鱼数量：<Orange20F>%d条</>\n可在水族箱收取时产出：<img id=\"T_Farmyard_Icon_Exp\"></><Orange20F>%d</>/小时\n（鱼缸需非空状态）"
  switch: 1
}
rows {
  id: "FarmBuffPanel_1"
  content: "作物/动物/加工器产量翻倍，钓鱼重量提升，餐厅农场币收益翻倍"
  switch: 1
}
rows {
  id: "FarmBuffPanel_2"
  content: "<Orange20F>{0}</>后消失"
  switch: 1
}
rows {
  id: "FarmBuffPanel_3"
  content: "小于1分钟"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_SameAsOld"
  content: "新旧签名内容相同"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_NotValid"
  content: "签名非法，修改失败"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_TooLong"
  content: "输入文本过长"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_ChangeFailed"
  content: "签名修改失败"
  switch: 1
}
rows {
  id: "Farmyard_Villager_1"
  content: "请选择赠送的礼物"
  switch: 1
}
rows {
  id: "Farmyard_Villager_2"
  content: "选中的物品只会赠送1个"
  switch: 1
}
rows {
  id: "Farmyard_Villager_3"
  content: "只会送出1个"
  switch: 1
}
rows {
  id: "Farmyard_Villager_4"
  content: "好感度: %d / %d"
  switch: 1
}
rows {
  id: "Farmyard_Villager_5"
  content: "居民即将到访农场~敬请期待！"
  switch: 1
}
rows {
  id: "Farmyard_Villager_6"
  content: "居民来访45级才解锁哦"
  switch: 1
}
rows {
  id: "Farmyard_Collection_9"
  content: "当前状态无法拾取藏品哦"
  switch: 1
}
rows {
  id: "Farmyard_Villager_SettleText"
  content: "<Orange22F>{0}</><Gray22F>/{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_Gift_14"
  content: "丰收之神降下赐福，礼物三倍暴击"
  switch: 1
}
rows {
  id: "Farmyard_Gift_15"
  content: "丰收之神降下赐福，礼物十倍大暴击"
  switch: 1
}
rows {
  id: "CarWorkingTips1"
  content: "更多务农功能敬请期待哦~"
  switch: 1
}
rows {
  id: "CarWorkingTips2"
  content: "已开启小车的务农功能"
  switch: 1
}
rows {
  id: "CarWorkingTips3"
  content: "已关闭小车的务农功能"
  switch: 1
}
rows {
  id: "HouseDecorationTips20"
  content: "多多在农场中劳作吧，会有意想不到的藏品收获。"
  switch: 1
}
rows {
  id: "HouseDecorationTips21"
  content: "多多和宠物互动吧，它们会把赠礼送到你手上哦~"
  switch: 1
}
rows {
  id: "HouseDecorationTips22"
  content: "多多和居民培养感情吧，他们也会为你准备惊喜哦~"
  switch: 1
}
rows {
  id: "FarmBuilding_check3"
  content: "确认要购买<Orange28>{0}</>吗？购买后可以在农场小屋里装扮"
  switch: 1
}
rows {
  id: "FarmBuilding_check4"
  content: "建造"
  switch: 1
}
rows {
  id: "FarmBuilding_check5"
  content: "当前暂无居民入住，等居民搬进来后再来拜访吧"
  switch: 1
}
rows {
  id: "FarmBuilding_check6"
  content: "当前农场币"
  switch: 1
}
rows {
  id: "FarmBuilding_check7"
  content: "地板墙纸类家具每样购买数与可装修建筑数一致"
  switch: 1
}
rows {
  id: "FarmBuilding_check8"
  content: "加工器可购买数量达到上限"
  switch: 1
}
rows {
  id: "HouseDecorationTips_509"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmBuilding_check9"
  content: "空置地基"
  switch: 1
}
rows {
  id: "FarmBuilding_check10"
  content: "空置小屋"
  switch: 1
}
rows {
  id: "FarmBuilding_check11"
  content: "{0}的家"
  switch: 1
}
rows {
  id: "FarmBuilding_check12"
  content: "还是不要睡在这里了，我们换个地方休息怎么样~"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_1"
  content: "<Orange19F>可交易</>"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_2"
  content: "交易完毕"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_3"
  content: "<Orange19F>{0}</>后离开"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_4"
  content: "<Orange19F>小于一分钟</>后离开"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_12"
  content: "下小雪，<Orange20F>{0}</>雪停，下雪期间\n成熟，会有<Orange20F>{1}</>的安全保护期。"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_13"
  content: "下暴雪，<Orange20F>{0}</>雪停，下雪期间\n成熟，会有<Orange20F>{1}</>的安全保护期。"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_14"
  content: "冰冻预警"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_15"
  content: "停留时间过长会被冰冻"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_16"
  content: "冰冻预警，停留时间过长会被冰冻"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_17"
  content: "瑞雪兆丰年，作物感动到无以复加！产量提升了！"
  switch: 1
}
rows {
  id: "FarmElf_Status_Text_1"
  content: "正在<Yellow28F>赶往农场</>"
  switch: 1
}
rows {
  id: "FarmElf_Status_Text_2"
  content: "正在<Yellow28F>梳妆打扮</>"
  switch: 1
}
rows {
  id: "FarmElf_Status_Text_3"
  content: "<Yellow28F>休息中</>"
  switch: 1
}
rows {
  id: "FarmElf_Status_Text_4"
  content: "到达<Yellow28F>农场附近</>"
  switch: 1
}
rows {
  id: "FarmElf_Status_Text_5"
  content: "<Yellow28F>正在农场中</>"
  switch: 1
}
rows {
  id: "FarmStatue_Item_NotEnough"
  content: "召唤铃不足，神农后通过神像许愿有机会获得"
  switch: 1
}
rows {
  id: "FarmStatue_Grade_NotEnough"
  content: "神像功能20级才能解锁哦"
  switch: 1
}
rows {
  id: "FarmStatue_Wish_Hint"
  content: "神农玩家每日8点后可从农场币和经验中二选一许愿。\n许愿结果为“旺”时，可获得召唤铃。"
  switch: 1
}
rows {
  id: "FarmElf_Status_LevelNotEnough"
  content: "{0}级后可与这位神秘的精灵相遇哦"
  switch: 1
}
rows {
  id: "FarmElf_Item_Description"
  content: "使用后可立即召唤指定的农场精灵，农场等级高于100级后开启神像-许愿功能后可获得。"
  switch: 1
}
rows {
  id: "FarmElf_Doublecheck"
  content: "是否确认使用{0}个召唤铃立即召唤{1}到访农场？"
  switch: 1
}
rows {
  id: "FarmElf_Item_DiffHint"
  content: "精灵所处的状态不同，需要的召唤铃数量不同"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Disable"
  content: "当前{0}全部为空置或成熟状态，无法缩短生长时间。"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Hint_1"
  content: "当前{0}内有部分为<Orange28>空置</>或<Orange28>成熟</>状态，是否仍缩短{1}小时生长时间？"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Hint_Double_1"
  content: "当前{0}内有部分为<Orange28>空置</>或<Orange28>成熟</>状态，是否仍缩短{1}小时生长时间？\n<Orange28>（周末双倍期间消耗双倍时间）</>"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Hint_2"
  content: "是否使{0}时间加快{1}小时？"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Hint_Double_2"
  content: "是否使{0}时间加快{1}小时？\n<Orange28>（周末双倍期间消耗双倍时间）</>"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Enough"
  content: "不需要再多的时间就可以成熟啦"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_NotEnough"
  content: "当前没有可使用的缩短时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Hint"
  content: "每日8点可积攒%s小时，积攒满%s小时后不再积攒，记得及时使用。周末双倍期间消耗双倍时间。"
  switch: 1
}
rows {
  id: "FarmMagic_Level101_NotEnough"
  content: "农场等级高于100级后，可开启神农宝典并学习该仙术。"
  switch: 1
}
rows {
  id: "FarmMagic_Level101_Enough"
  content: "您当前已达到神农等级，无需开通月卡，可前往神农宝典学习该仙术。"
  switch: 1
}
rows {
  id: "FarmMagic_MonthCardOn_Hint"
  content: "您已开通月卡，可提前体验此神农仙术。"
  switch: 1
}
rows {
  id: "FarmMagic_Processor_None"
  content: "当前小屋内未放置加工器，无法缩短加工时间。"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_DoubleNotEnough"
  content: "当前处于周末双倍期间，需要消耗双倍时间。剩余可使用的时间不足{0}小时。"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_LayoutHint"
  content: "周末双倍期间消耗双倍时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_NotEnough"
  content: "时光沙漏数量不足"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Enough"
  content: "当前积攒时间已满，不可再增加"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Success"
  content: "时光沙漏使用成功，已增加仙术积攒时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Usehint1"
  content: "使用后拥有总时间：%s小时"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Amount"
  content: "当前时光沙漏：%s"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Hour"
  content: "%s小时"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Usehint2"
  content: "每消耗1个时光沙漏道具可获得0.1小时加速时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTimeItem_Usehint3"
  content: "使用后积攒的时间会超出上限，超出的部分不会被保留，是否确认使用？"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_NotEnough2"
  content: "当前可使用的时间不足0.5小时"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_NotEnough3"
  content: "没有更多可用的时间"
  switch: 1
}
rows {
  id: "FarmVillagersLeave1"
  content: "居民已被成功请离，居民小屋需要清扫，请先离开哦"
  switch: 1
}
rows {
  id: "Farmyard_Villager_7"
  content: "集合铃响了，空闲中的伙伴正在赶来"
  switch: 1
}
rows {
  id: "Farmyard_Villager_8"
  content: "集合铃响了，空闲中的伙伴正在赶来"
  switch: 2
}
rows {
  id: "Farmyard_OverlordFlower_Level"
  content: "满"
  switch: 1
}
rows {
  id: "Hotspring_Text_1"
  content: "温泉功能60级才能解锁哦"
  switch: 1
}
rows {
  id: "Hotspring_Text_2"
  content: "每天泡温泉时喝茶可获得一次增益\n温泉类型每周一上午8点统一刷新"
  switch: 1
}
rows {
  id: "Hotspring_Text_3"
  content: "今日温泉经验获取完毕"
  switch: 1
}
rows {
  id: "Hotspring_Text_4"
  content: "确定获取温泉增益\n【{0}】吗？\n\n温泉增益每天只能获取<Orange28>1</>次"
  switch: 1
}
rows {
  id: "Hotspring_Text_5"
  content: "确定将温泉增益替换成\n【{0}】吗？\n\n温泉增益每天只能获取<Orange28>1</>次"
  switch: 1
}
rows {
  id: "Hotspring_Text_6"
  content: "成功获得温泉增益{0}"
  switch: 1
}
rows {
  id: "Hotspring_Text_7"
  content: "您已获取该增益"
  switch: 1
}
rows {
  id: "Hotspring_Text_8"
  content: "此处不可泡温泉"
  switch: 1
}
rows {
  id: "Hotspring_Text_9"
  content: "每天只能获取一次温泉增益，请明天8点后再来"
  switch: 1
}
rows {
  id: "Hotspring_Text_10"
  content: "<Orange21F>{0}</>"
  switch: 1
}
rows {
  id: "Hotspring_Text_11"
  content: "<Orange28>{0}</>"
  switch: 1
}
rows {
  id: "Hotspring_Text_12"
  content: "温泉增益"
  switch: 1
}
rows {
  id: "Hotspring_Text_13"
  content: "对方是您的好友时才能泡温泉"
  switch: 1
}
rows {
  id: "Hotspring_Text_14"
  content: "温泉预留"
  switch: 1
}
rows {
  id: "Hotspring_Text_15"
  content: "温泉预留"
  switch: 1
}
rows {
  id: "Hotspring_Text_16"
  content: "温泉预留"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_25"
  content: "在加工器前无法售卖物品"
  switch: 1
}
rows {
  id: "Farmyard_Intro10"
  content: "居民"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_1"
  content: "邀请入住"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_1_Text"
  content: "居民每日8:00会到达码头，同居民对话，可邀请他在农场住下，\n但需要为他提前准备好可以入住的居民小屋。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_2"
  content: "居民小屋"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_2_Text"
  content: "在农场大树后的居住区，靠近空置的地基，可建造和升级居民小屋。\n升级后，住在小屋里的居民会送你更高品质的礼物。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_3"
  content: "请离"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_3_Text"
  content: "点击居民信息面板下方的“请离”按钮，可以让居民离开农场。\n但这是一个需要深思熟虑的操作哦。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_4"
  content: "好感度"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_4_Text"
  content: "和居民打招呼、送礼，都可以增加居民对你的好感，\n好感度提升，能获知居民的故事，了解这些故事可获得农场经验。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_5"
  content: "送礼"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_5_Text"
  content: "每天可以给一位居民送一次礼物，\n居民们喜爱的礼物各有不同，可以送不同种类的礼物试试看。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_6"
  content: "收礼"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_6_Text"
  content: "当居民头顶出现礼物图标时，和居民对话可以收到居民的赠礼。\n居民赠礼的品质受居民小屋等级、好感度和送礼时间间隔的影响。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_7"
  content: "呼唤"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_7_Text"
  content: "摇动集合铃，农场中空闲的居民和宠物都会被呼唤过来哦。\n找不到居民和宠物的时候可以摇动集合铃试试看。"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_8"
  content: "家具购买"
  switch: 1
}
rows {
  id: "Farmyard_Intro10_8_Text"
  content: "居民小屋内的家具都是居民们精心挑选的，\n如果喜欢的话，点击居民小屋内的家具便可以购买同款。"
  switch: 1
}
rows {
  id: "FarmyardVillagerLeaveDialogKey"
  content: "居民离开农场后，将清空TA的小屋，但保留好感度，仍要请离该居民吗？"
  switch: 1
}
rows {
  id: "FarmVillagersLeave2"
  content: "这位居民正在待客，等一下再试试吧"
  switch: 1
}
rows {
  id: "Farmyard_Villager_9"
  content: "请前往大树后的居住区建造新的小屋或腾出空屋"
  switch: 1
}
rows {
  id: "HouseDecorationTips_565"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "HouseDecorationTips_546"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmBuilding_check13"
  content: "{0}在布置这个房间，还需要一点时间 "
  switch: 1
}
rows {
  id: "Farmyard_CannotStartDialog"
  content: "当前状态无法发起对话哦"
  switch: 1
}
rows {
  id: "FarmyardVillagerReceiveGiftKey"
  content: "每日只能给一位居民送礼，是否继续？"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_5"
  content: "<Orange19F>{0}</>倍收购<Orange19F>{1}</>"
  switch: 1
}
rows {
  id: "Farmyard_SellLock_1"
  content: "已锁定物品不可出售。"
  switch: 1
}
rows {
  id: "Farmyard_SellLock_2"
  content: "已锁定物品无法选择。"
  switch: 1
}
rows {
  id: "Farmyard_SellLock_3"
  content: "点击锁定\\解锁物品\n上锁物品不会被出售"
  switch: 1
}
rows {
  id: "Farmyard_Villager_10"
  content: "小伙伴正在赶来"
  switch: 1
}
rows {
  id: "Farmyard_Villager_11"
  content: "小伙伴正在待客"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_1"
  content: "本周可使用"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_2"
  content: "每周一早8点恢复使用次数至上限"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_3"
  content: "当前农田里没有被拿取或吞食过的作物"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_4"
  content: "当前作物不能被拿取，受到神农百花妙手回春仙术保护"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_5"
  content: "确认使用妙手回春仙术吗？"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_UI_6"
  content: "作物受到神农百花妙手回春仙术保护，\n不能被拿取，收获或铲除后取消"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_Tips_1"
  content: "本周次数已耗尽，周一早8点恢复"
  switch: 1
}
rows {
  id: "FarmMagic_Revive_Tips_2"
  content: "农田里必须要有被拿取或吞食过的作物才能使用"
  switch: 1
}
rows {
  id: "Farmyard_OverlordFlower_6"
  content: "霸王吞吞花未达到下一级的经验值，已转化为农场币返还"
  switch: 1
}
rows {
  id: "Farmyard_FishCard_Overflow"
  content: "本次多出的鱼卡自动转化为{0}农场币"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Disable"
  content: "当前{0}全部为空置或成熟状态，无法推迟收获时间。"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Hint_1"
  content: "当前{0}内有部分为<Orange28>空置</>或<Orange28>成熟</>状态，是否仍推迟{1}小时收获时间？"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Hint_2"
  content: "是否使{0}时间推迟{1}小时？(推迟的时间不会产生额外收益)"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Hint_Restaurant"
  content: "是否使<Orange28>{0}贵宾到访</>时间推迟{1}小时？(推迟的时间不会产生额外收益)"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_NotEnough"
  content: "今天已使用过缓时咒了"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Text"
  content: "每日早8点恢复一次使用次数，每日可使用一次，使用次数不会积攒。"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_Processor_None"
  content: "当前小屋内未放置加工器，无法推迟加工时间。"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_VIP_Arrived"
  content: "贵宾已到访，先去接待贵宾吧"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_VIP_Empty"
  content: "当前没有预约贵宾，无法推迟"
  switch: 1
}
rows {
  id: "Farmyard_Social_22"
  content: "已屏蔽该好友，对方已无法为你祈福"
  switch: 1
}
rows {
  id: "Farmyard_Social_23"
  content: "已解除该好友的祈福屏蔽，对方可以为你祈福"
  switch: 1
}
rows {
  id: "Farmyard_Social_24"
  content: "当前农场无法被祈福"
  switch: 1
}
rows {
  id: "HouseDecorationTips_1066"
  content: "前往热购特色礼包获取"
  switch: 1
}
rows {
  id: "HouseDecorationTips_1072"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_22"
  content: "<Orange19F>{0}</>来过，"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_23"
  content: "投喂了<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_24"
  content: "你还不是农场主的好友哦~"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_25"
  content: "好友喂食记录"
  switch: 1
}
rows {
  id: "Farmyard_Pet_manage_26"
  content: "显示喂食记录"
  switch: 1
}
rows {
  id: "FarmUAVAnimals1"
  content: "无人机将在收获动物产物后自动投放已选择的库存动物哦"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check01"
  content: "保存图纸{0}/{1}"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check02"
  content: "读取图纸{0}/{1}"
  switch: 1
}
rows {
  id: "FarmBuilding_checkclick01"
  content: "确定要关闭点击购买家具的功能么？"
  switch: 1
}
rows {
  id: "FarmBuilding_checkclick02"
  content: "确定要开启点击购买家具的功能么？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check03"
  content: "包含<Yellow24FOutline>{0}</>个家具"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check04"
  content: "保存成功"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check05"
  content: "确定要删除{0}？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check06"
  content: "确定要用新图纸覆盖当前所选的图纸吗？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check07"
  content: "警告：读取的图纸会把原房间的布置替换，确认要读取该图纸吗？\n（请保存好当前屋内图纸再使用！）"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check08"
  content: "确认要保存该图纸吗？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check09"
  content: "已达到图纸保存数量上限"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check010"
  content: "发布失败"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check011"
  content: "读取确认"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check012"
  content: "离开确认"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check013"
  content: "你是否要放弃当前的编辑？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check014"
  content: "我的图纸{0}"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check015"
  content: "点击输入名称"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check016"
  content: "图纸改名"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check017"
  content: "输入内容不能为空"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check018"
  content: "点击输入描述"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check019"
  content: "字数超过限制，请重新修改"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check020"
  content: "我的图纸{0}编辑中"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check021"
  content: "发布图纸需要满足以下条件："
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check022"
  content: "购物车内物品清空"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check023"
  content: "限购物品达到上限"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check024"
  content: "加工器未清空"
  switch: 1
}
rows {
  id: "Farmyard_TalentText_Icontoast_5"
  content: "你还没有通灵兽哦"
  switch: 1
}
rows {
  id: "FarmKylin_Infor_EvolveFinishLocalTime"
  content: "<Orange19F>{0}</>进化完"
  switch: 1
}
rows {
  id: "FarmKylin_Infor_EvolveFinishCDTime"
  content: "<Orange19F>{0}</>后进化完"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_OpenEggDis"
  content: "首次孵化农场币不足"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_EvolveDis"
  content: "进化所需农场币不足"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_NeedLvDis"
  content: "进化需达到{0}{1}"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_ComfirmOpenEgg"
  content: "确定要孵化神秘的蛋吗？"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_EvolveCostTime"
  content: "进化需耗时<Orange21F>{0}</>，期间不积攒经验"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_EvolveFinishLocalTime"
  content: "<Orange24F>{0}</>进化完成，请耐心等待吧"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_EvolveFinishCDTime"
  content: "<Orange24F>{0}</>后进化完成，请耐心等待吧"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_AutoGetTotalExp"
  content: "已自动获得剩余灵力转化经验{0}"
  switch: 1
}
rows {
  id: "FarmKylin_OpTips_ExpNotFull"
  content: "灵力还未收集满，还不可领取哦"
  switch: 1
}
rows {
  id: "FarmKylin_Infor_ManaMaxTips"
  content: "<KylinOrange18F>灵力已达可积累最大值，请尽快收取</>"
  switch: 1
}
rows {
  id: "FarmKylin_Infor_ManaFuncTips"
  content: "<KylinOrange18F>通过收获动物集满灵力可领取经验</>"
  switch: 1
}
rows {
  id: "FarmKylin_Infor_ManaFuncTips_ExpFull"
  content: "<KylinOrange18F>灵力已满，可领取农场经验</>"
  switch: 1
}
rows {
  id: "FarmKylin_OpTips1_ExpInfo"
  content: "收获动物会根据收获周期获得相应灵力，丰收或大丰收时将获得更多灵力，<KylinOrange18F>丰收和大丰收倍率影响灵力值获取量</>"
  switch: 1
}
rows {
  id: "FarmKylin_EggToast"
  content: "感受到阵阵灵气，达到神农萤草就能见到它了！"
  switch: 1
}
rows {
  id: "FarmKylin_EggToast_101"
  content: "一个小生命将要苏醒，快孵化它吧！"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_EvolveNeedGainExp"
  content: "请收集满灵力领取一次经验再进化吧"
  switch: 1
}
rows {
  id: "FarmKylin_CallBack_1"
  content: "通灵兽被呼唤过来咯~"
  switch: 1
}
rows {
  id: "FarmKylin_CallBack_2"
  content: "请稍等一会再呼唤通灵兽吧"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_Hatching"
  content: "正在进化到{0}..."
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_HatchFinish"
  content: "{0}进化完成！"
  switch: 1
}
rows {
  id: "FarmKylin_LevelUp_ExpGain"
  content: "获得的总经验由通灵兽当前灵力转换、进化奖励经验组成。"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_18"
  content: "是否取消本次下雨期间的雷击保护"
  switch: 1
}
rows {
  id: "Farmyard_Weather_Text_19"
  content: "剩余可收获量/原产量"
  switch: 1
}
rows {
  id: "Farm_InteractNpc_6"
  content: "<Orange19F>{0}</>离开"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_26"
  content: "设置单个加工器加工的数量："
  switch: 1
}
rows {
  id: "Farmyard_ProcText_27"
  content: "{0}/{1}"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_28"
  content: "加工器工作中，无法操作"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_29"
  content: "加工作物容量提升"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_30"
  content: "加工动物容量提升"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_31"
  content: "加工作物获得农场经验提升"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_32"
  content: "加工动物获得农场经验提升 "
  switch: 1
}
rows {
  id: "Farmyard_ProcText_33"
  content: "当前已到最高级"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_34"
  content: "确认要购买并替换<Orange28>加工器</>吗？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_35"
  content: "确认要替换<Orange28>加工器</>吗？"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_36"
  content: "已达当前数量上限"
  switch: 1
}
rows {
  id: "Farmyard_ProcText_37"
  content: "被替换的加工器将会进入仓库"
  switch: 1
}
rows {
  id: "Farm_FishBook_Top_Click_Tip3"
  content: "只能在自己的农场学习神农宝典"
  switch: 1
}
rows {
  id: "LittleFoxTips_Text_1"
  content: "当前无法进行此操作"
  switch: 1
}
rows {
  id: "LittleFoxTips_Text_2"
  content: "小红狐的小窝正在建设中哦"
  switch: 1
}
rows {
  id: "LittleFoxTips_Text_3"
  content: "小红狐农场预留2"
  switch: 1
}
rows {
  id: "LittleFoxTips_Text_4"
  content: "小红狐农场预留3"
  switch: 1
}
rows {
  id: "LittleFoxTips_Text_5"
  content: "小红狐农场预留4"
  switch: 1
}
rows {
  id: "LittleFoxActionTips_1"
  content: "小红狐祈福不占用每日被祈福次数哦~"
  switch: 1
}
rows {
  id: "HouseDecorationTips_575"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_TextUse"
  content: "推迟%s收获时间"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_TextProcessorUse"
  content: "推迟%s加工时间"
  switch: 1
}
rows {
  id: "FarmMagic_IncreaseTime_TextRestaurantUse"
  content: "推迟%s贵宾到达时间"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_23"
  content: "还需{0}天月卡解锁"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_24"
  content: "已解锁"
  switch: 1
}
rows {
  id: "ReturnSquare_1"
  content: "返回广场"
  switch: 1
}
rows {
  id: "ReturnSquare_2"
  content: "确认返回广场？"
  switch: 1
}
rows {
  id: "FarmBuildingPlan_check025"
  content: "编辑图纸时不可超过已扩建上限"
  switch: 1
}
rows {
  id: "FarmPartyTip_1"
  content: "由于违规行为，当前无法创建派对，截止日期为: {0}"
  switch: 1
}
rows {
  id: "HouseDecorationTips_586"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "Farmyard_NPCFishing_3"
  content: "稀有及以上熟练度:{0}级"
  switch: 1
}
rows {
  id: "FarmCookNotOpen"
  content: "餐厅暂未开启"
  switch: 1
}
rows {
  id: "HouseDecorationTips_655"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "FarmParty_HomeName"
  content: "{0}的农场"
  switch: 1
}
rows {
  id: "FarmParty_HomeName_2"
  content: "{0}的小屋"
  switch: 1
}
rows {
  id: "FarmParty_HomeName_3"
  content: "{0}的居民家"
  switch: 1
}
rows {
  id: "FarmParty_HomeName_4"
  content: "{0}的餐厅"
  switch: 1
}
rows {
  id: "FarmParty_StateTip"
  content: "离开自己的农场或离线时，你的农场将不被显示在派对公告板中哦"
  switch: 1
}
rows {
  id: "FarmPublish_1"
  content: "发布"
  switch: 1
}
rows {
  id: "FarmParty_7"
  content: "输入关键字搜索"
  switch: 1
}
rows {
  id: "FarmParty_NotSearchTip"
  content: "未搜到匹配的农场，请重新尝试"
  switch: 1
}
rows {
  id: "FarmParty_PartyEmpty"
  content: "当前没有农场在开派对，请稍后再试试吧"
  switch: 1
}
rows {
  id: "FarmParty_PartyEmpty_2"
  content: "当前没有好友在开派对，请稍后再试试吧"
  switch: 1
}
rows {
  id: "FarmPartyTab_1"
  content: "派对"
  switch: 1
}
rows {
  id: "FarmPartyTab_2"
  content: "好友"
  switch: 1
}
rows {
  id: "FarmPublish_24"
  content: "描述文字不能为空"
  switch: 1
}
rows {
  id: "FarmPublish_3"
  content: "描述文字超过30"
  switch: 1
}
rows {
  id: "FarmParty_5"
  content: "审核中"
  switch: 1
}
rows {
  id: "FarmParty_2"
  content: "派对发布失败"
  switch: 1
}
rows {
  id: "FarmParty_4"
  content: "上传中"
  switch: 1
}
rows {
  id: "FarmParty_1"
  content: "派对发布成功"
  switch: 1
}
rows {
  id: "FarmParty_ClosePartyTip"
  content: "关闭后农场不会在派对列表中显示了，确定关闭吗?"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_1"
  content: "当前场景已改变，请注意重拍下照片哦"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_2"
  content: "发布派对需要达到农场等级{0}级"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_3"
  content: "当前设置农场访问权限为仅好友，确认发布吗？（可在发布界面右上角的设置按钮内修改）"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_4"
  content: "在居民家里不能发布派对打扰他们哦"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_5"
  content: "暂时不能使用该功能"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_6"
  content: "需要留在当前场景，派对面板中才会显示您的信息"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_7"
  content: "餐厅顾客太多了，不能举办派对哦"
  switch: 1
}
rows {
  id: "FarmParty_PublishTip_8"
  content: "剩余时间：{0}"
  switch: 1
}
rows {
  id: "Farmyard_MonthCard_22"
  content: "大款、富豪顾客结账时金额加倍"
  switch: 1
}
rows {
  id: "Farmyard_BuildingSkin_10"
  content: "座位已满"
  switch: 1
}
rows {
  id: "CookDataTips1"
  content: "该厨灶厨师做饭速度"
  switch: 1
}
rows {
  id: "CookDataTips2"
  content: "该餐位美食售价"
  switch: 1
}
rows {
  id: "CookDataTips3"
  content: "餐厅人气值"
  switch: 1
}
rows {
  id: "CookDataTips4"
  content: "结账时餐厅经验"
  switch: 1
}
rows {
  id: "CookDataTips5"
  content: "该厨灶可放置美食数量"
  switch: 1
}
rows {
  id: "CookDataTips6"
  content: "当前可摆放："
  switch: 1
}
rows {
  id: "CookDataTips7"
  content: "当前等级不可摆放"
  switch: 1
}
rows {
  id: "CookDecorationWaiterFull"
  content: "场景里服务员数量已满"
  switch: 1
}
rows {
  id: "CookDecorationCookFull"
  content: "场景里厨师数量已满"
  switch: 1
}
rows {
  id: "CookDecorationNoStove"
  content: "场景里没有足够的厨灶，无法放置厨师"
  switch: 1
}
rows {
  id: "CookDecorationNoSpace"
  content: "当前位置不可放置员工哦"
  switch: 1
}
rows {
  id: "CookQuickReplacement1"
  content: "购买替换成功，原有家具已放置到仓库内"
  switch: 1
}
rows {
  id: "CookQuickReplacement2"
  content: "确认要替换所有共%s个%s吗？您已拥有%s个，还需补齐%s农场币"
  switch: 1
}
rows {
  id: "CookQuickReplacement3"
  content: "确认要替换所有共%s个%s吗？共花费%s农场币"
  switch: 1
}
rows {
  id: "CookFurnitureChange1"
  content: "确认为当前的家具更换皮肤吗？"
  switch: 1
}
rows {
  id: "CookFurnitureChange2"
  content: "确认为场景里所有的家具更换外观吗？"
  switch: 1
}
rows {
  id: "CookFurnitureChange3"
  content: "已加入购物车"
  switch: 1
}
rows {
  id: "CookFurnitureChange4"
  content: "已装扮当前外观"
  switch: 1
}
rows {
  id: "CookDecorationIllegal1"
  content: "当前装修方案会影响餐厅运作哦，确认要发布吗？"
  switch: 1
}
rows {
  id: "CookDecorationPickUp1"
  content: "请先清空家具再收回背包吧~"
  switch: 1
}
rows {
  id: "CooRestaurantPublish1"
  content: "当前餐厅已进行装修，请重新进入餐厅~"
  switch: 1
}
rows {
  id: "CookStaffRest1"
  content: "确认要安排<Orange28>{0}</>休息么？该员工当前服务的客人会离开哦~"
  switch: 1
}
rows {
  id: "CookStaffRest2"
  content: "<Orange24>{0}</>休息了"
  switch: 1
}
rows {
  id: "CookStaffFired1"
  content: "确认要解雇<Orange28>{0}</>么？该员工当前服务的客人会离开哦~"
  switch: 1
}
rows {
  id: "CookStaffFired2"
  content: "<Orange24>{0}</>被解雇了"
  switch: 1
}
rows {
  id: "CookStaffOnDuty1"
  content: "点击选择要安排工作的员工"
  switch: 1
}
rows {
  id: "CookStaffOnDuty2"
  content: "<Orange24>{0}</>开始工作啦"
  switch: 1
}
rows {
  id: "CookStaffOnDuty3"
  content: "场景里没有空的位置了，请更换装修后再放置员工吧"
  switch: 1
}
rows {
  id: "CookStaffOnDuty4"
  content: "当前大堂工作员工已满员。"
  switch: 1
}
rows {
  id: "CookStaffEmployment1"
  content: "餐厅等级越高，越有可能招到高数值的员工"
  switch: 1
}
rows {
  id: "CookStaffEmployment2"
  content: "已招聘<Orange24>{0}</>"
  switch: 1
}
rows {
  id: "CookStaffEmployment3"
  content: "餐厅当前用不到这么多员工，确认招聘么？"
  switch: 1
}
rows {
  id: "CookStaffEmployment4"
  content: "你招聘的人太多啦！请开除一些人再招聘~"
  switch: 1
}
rows {
  id: "CookStaffEmployment5"
  content: "招聘"
  switch: 1
}
rows {
  id: "CookStaffEmployment6"
  content: "确认花费<Orange28>{0}</>农场币招聘<Orange28>{1}</>吗？"
  switch: 1
}
rows {
  id: "CookStaffEmployment7"
  content: "拥有农场币：{0}"
  switch: 1
}
rows {
  id: "CookStaffEmployment8"
  content: "免费刷新"
  switch: 1
}
rows {
  id: "CookStaffEmployment9"
  content: "刷新"
  switch: 1
}
rows {
  id: "CookStaffEmployment10"
  content: "今日可刷新"
  switch: 1
}
rows {
  id: "CookStaffEmployment11"
  content: "确认花费<Orange28>{0}</>农场币招聘\n<img id=\"T_FarmCook_Chef\"></>厨师<Orange28>{1}</>吗？"
  switch: 1
}
rows {
  id: "CookStaffEmployment12"
  content: "确认花费<Orange28>{0}</>农场币招聘\n<img id=\"T_FarmCook_Waiter\"></>服务员<Orange28>{1}</>吗？"
  switch: 1
}
rows {
  id: "CookStaffEmployment13"
  content: "当前可招聘最高品质为<CookGreen28H>优秀</>，可招聘星级为<Orange28H>{0}</><T_FarmCook_Star></>"
  switch: 1
}
rows {
  id: "CookStaffEmployment14"
  content: "当前可招聘最高品质为<CookBlue28H>高级</>，可招聘星级为<Orange28H>{0}</><T_FarmCook_Star></>"
  switch: 1
}
rows {
  id: "CookStaffEmployment15"
  content: "当前可招聘最高品质为<CookPurple28H>卓越</>，可招聘星级为<Orange28H>{0}</><T_FarmCook_Star></>"
  switch: 1
}
rows {
  id: "CookStaffEmployment16"
  content: "当前可招聘最高品质为<CookOrange281>专家</>，可招聘星级为<Orange28H>{0}</><T_FarmCook_Star></>"
  switch: 1
}
rows {
  id: "CookStaffEmployment17"
  content: "升级餐厅可能增加可摆放的厨灶数量"
  switch: 1
}
rows {
  id: "CookStaffEmployment18"
  content: "同时替换<Orange28>{0}</>上岗"
  switch: 1
}
rows {
  id: "CookStaffReplace"
  content: "确认要安排<Orange28>{0}</>休息么？该员工当前服务的客人会离开哦~"
  switch: 1
}
rows {
  id: "CookRestRoom"
  content: "休息室"
  switch: 1
}
rows {
  id: "CookLobby"
  content: "大堂"
  switch: 1
}
rows {
  id: "CookCookingAbility"
  content: "厨艺"
  switch: 1
}
rows {
  id: "CookServiceAbility"
  content: "服务"
  switch: 1
}
rows {
  id: "CookSpeedAbility"
  content: "速度"
  switch: 1
}
rows {
  id: "CookCharmAbility"
  content: "魅力"
  switch: 1
}
rows {
  id: "CookNoFrontDesk"
  content: "餐厅内未放置前台，请放置后再发布。"
  switch: 1
}
rows {
  id: "CookExpandTips1"
  content: "下一级扩建条件：餐厅等级%d级"
  switch: 1
}
rows {
  id: "CookExpandTips2"
  content: "家具设备"
  switch: 1
}
rows {
  id: "CookExpandTips3"
  content: "员工"
  switch: 1
}
rows {
  id: "CookExpandTips4"
  content: "前台"
  switch: 1
}
rows {
  id: "CookExpandTips5"
  content: "点评屏"
  switch: 1
}
rows {
  id: "CookExpandTips6"
  content: "餐位"
  switch: 1
}
rows {
  id: "CookExpandTips7"
  content: "厨灶"
  switch: 1
}
rows {
  id: "CookExpandTips8"
  content: "普通员工"
  switch: 1
}
rows {
  id: "CookDeskMoreThanOne"
  content: "前台家具只可以放置一个哦，请收起多余的前台再发布吧~"
  switch: 1
}
rows {
  id: "CookDecorationNoMoreFurniture"
  content: "当前家具已达到摆放数量上限"
  switch: 1
}
rows {
  id: "CookStaffAtLeastOne"
  content: "餐厅需要至少保留一个厨师和服务员才能正常运转哦"
  switch: 1
}
rows {
  id: "CookDecorationNewFurniture"
  content: "有新的功能家具可以摆放"
  switch: 1
}
rows {
  id: "CookDecorationNewExpand"
  content: "当前餐厅可扩建"
  switch: 1
}
rows {
  id: "HouseDecorationNewExpand"
  content: "当前小屋可扩建"
  switch: 1
}
rows {
  id: "CookDecorationNewProcessor"
  content: "有新的加工器可以摆放"
  switch: 1
}
rows {
  id: "CookExpandGuestTips1"
  content: "当前餐厅已进行扩建，请重新进入"
  switch: 1
}
rows {
  id: "CookDecorationTips1"
  content: "是否以当前方案装修餐厅？"
  switch: 1
}
rows {
  id: "CookGuestReEntry"
  content: "房主发布了餐厅装修，请重新拜访吧"
  switch: 1
}
rows {
  id: "CookStaffNotEnough"
  content: "当前在岗员工未满员，是否安排该员工上岗？"
  switch: 1
}
rows {
  id: "CookStaffGoToRestRoom"
  content: "招聘成功，当前在岗员工已满，可去休息室查看"
  switch: 1
}
rows {
  id: "CookStaffServe"
  content: "影响服务员对美食售价的加成"
  switch: 1
}
rows {
  id: "CookStaffCook"
  content: "影响厨师对美食售价的加成"
  switch: 1
}
rows {
  id: "CookStaffSpeedCook"
  content: "影响厨师烹饪美食速度"
  switch: 1
}
rows {
  id: "CookStaffSpeedServe"
  content: "影响服务员上菜与清洁速度"
  switch: 1
}
rows {
  id: "CookStaffCharm"
  content: "影响厨师和服务员对餐厅客流量的加成"
  switch: 1
}
rows {
  id: "DroneRestaurantTips1"
  content: "自动结算收款、补充食材及预约接待贵宾"
  switch: 1
}
rows {
  id: "DroneRestaurantTips2"
  content: "餐厅"
  switch: 1
}
rows {
  id: "DroneRestaurantTips3"
  content: "无人机开始前往餐厅工作"
  switch: 1
}
rows {
  id: "DroneRestaurantTips4"
  content: "成功邀请贵宾在餐厅内落座"
  switch: 1
}
rows {
  id: "DroneRestaurantTips5"
  content: "餐厅未解锁"
  switch: 1
}
rows {
  id: "DroneRestaurantTips6"
  content: "<Orange20F>{0}</>小时后倒到访，有<Orange20F>{0}</>小时保护时间"
  switch: 1
}
rows {
  id: "DroneRestaurantTips7"
  content: "已成功预约餐厅贵宾"
  switch: 1
}
rows {
  id: "DroneFishTankTips1"
  content: "无人机将优先收获鱼缸产出的农场币，随后针对鱼塘进行自动钓鱼"
  switch: 1
}
rows {
  id: "DroneRestaurantTips8"
  content: "餐厅预约贵宾功能15级后解锁哦~"
  switch: 1
}
rows {
  id: "DroneRestaurantTips9"
  content: "已为餐厅补充食材~"
  switch: 1
}
rows {
  id: "DroneRestaurantTips10"
  content: "餐厅没有可以操作的工作"
  switch: 1
}
rows {
  id: "CookQuickUpgrade1"
  content: "确认购买并替换成新的{0}吗？"
  switch: 1
}
rows {
  id: "CookQuickUpgrade2"
  content: "确认替换成新的{0}吗？"
  switch: 1
}
rows {
  id: "CookQuickUpgrade3"
  content: "餐位"
  switch: 1
}
rows {
  id: "CookQuickUpgrade4"
  content: "厨灶"
  switch: 1
}
rows {
  id: "CookQuickUpgrade5"
  content: "前台"
  switch: 1
}
rows {
  id: "CookQuickUpgrade6"
  content: "点评屏"
  switch: 1
}
rows {
  id: "CookQuickUpgrade7"
  content: "该餐位美食售价"
  switch: 1
}
rows {
  id: "CookQuickUpgrade8"
  content: "该厨灶可放置美食数量"
  switch: 1
}
rows {
  id: "CookQuickUpgrade9"
  content: "该厨灶厨师做饭速度"
  switch: 1
}
rows {
  id: "CookQuickUpgrade10"
  content: "餐厅人气值"
  switch: 1
}
rows {
  id: "CookQuickUpgrade11"
  content: "结账时餐厅经验"
  switch: 1
}
rows {
  id: "CookQuickUpgrade12"
  content: "替换所有"
  switch: 1
}
rows {
  id: "CookQuickUpgrade13"
  content: "替换当前"
  switch: 1
}
rows {
  id: "CookQuickUpgrade14"
  content: "确认替换<Orange28>{0}</>个<Orange28>{1}</>吗？您已拥有<Orange28>{2}</>个，还需补齐<Orange28>{3}</>农场币"
  switch: 1
}
rows {
  id: "CookQuickUpgrade15"
  content: "确认替换<Orange28>{0}</>个<Orange28>{1}</>吗？共花费<Orange28>{2}</>农场币"
  switch: 1
}
rows {
  id: "CookQuickUpgrade16"
  content: "确认花费<Orange28>{0}</>农场币替换当前的<Orange28>{1}</>吗？"
  switch: 1
}
rows {
  id: "CookQuickUpgrade17"
  content: "确认替换所有<Orange28>{0}</>个<Orange28>{1}</>吗？"
  switch: 1
}
rows {
  id: "CookQuickUpgrade18"
  content: "确认替换当前的<Orange28>{0}</>吗？"
  switch: 1
}
rows {
  id: "CookQuickUpgrade19"
  content: "升级替换"
  switch: 1
}
rows {
  id: "CookQuickUpgrade20"
  content: "被替换的家具将会进入仓库"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance1"
  content: "当前已装扮该款外观"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance2"
  content: "替换所有{0}个"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance3"
  content: "更换皮肤"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance4"
  content: "确认要更换成<Orange28>{0}</>的外观吗？"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance5"
  content: "替换当前"
  switch: 1
}
rows {
  id: "CookStaffRefreshLimit1"
  content: "今日剩余刷新次数只有{0}次了，请注意"
  switch: 1
}
rows {
  id: "DroneRestaurantTips11"
  content: "未选择预约单"
  switch: 1
}
rows {
  id: "CookRestFired"
  content: "确认要解雇<Orange28>{0}</>么？"
  switch: 1
}
rows {
  id: "CookStaffFull"
  content: "已达到可工作员工数量上限"
  switch: 1
}
rows {
  id: "CookFurnitureAppearance6"
  content: "可以使用已解锁的同类型家具皮肤"
  switch: 1
}
rows {
  id: "CookQuickUpgrade21"
  content: "升级替换成功~"
  switch: 1
}
rows {
  id: "CookStaffUpgrade"
  content: "餐厅{0}级可以再解锁{1}个员工位置"
  switch: 1
}
rows {
  id: "CookFurnitureUpgrade"
  content: "餐厅{0}级可以再解锁{1}个{2}摆放位置"
  switch: 1
}
rows {
  id: "CookStaffInterface1"
  content: "全部"
  switch: 1
}
rows {
  id: "CookStaffInterface2"
  content: "厨师"
  switch: 1
}
rows {
  id: "CookStaffInterface3"
  content: "服务员"
  switch: 1
}
rows {
  id: "CookStaffInterface4"
  content: "当前上岗厨师或服务员<Orange21F>{0}</>人"
  switch: 1
}
rows {
  id: "CookFrontDeskPurchase"
  content: "餐厅没有前台会停止运作哦，放下新的前台并发布后再来出售吧~"
  switch: 1
}
rows {
  id: "CookAssetsTips1"
  content: "厨灶不一定要放满，平衡好厨灶、厨师和服务员的数量才能获得最佳收益哦"
  switch: 1
}
rows {
  id: "Cook_UnlockStaff"
  content: "餐厅{0}级才能安排和雇佣员工哦~"
  switch: 1
}
rows {
  id: "Cook_UnlockVIP"
  content: "餐厅{0}级才能接受贵宾预约哦~"
  switch: 1
}
rows {
  id: "Cook_UnlockComment"
  content: "餐厅7级才能开启餐厅点评哦~"
  switch: 1
}
rows {
  id: "Cook_Prepare_Full_Selection"
  content: "菜单已满，请先卸下已有的美食，再来添加吧"
  switch: 1
}
rows {
  id: "Cook_Prepare_Clear_All_Dishes"
  content: "是否清空所有准备的美食？"
  switch: 1
}
rows {
  id: "Cook_Prepare_Selected_Not_Full"
  content: "请准备足够的美食后，再开始营业吧"
  switch: 1
}
rows {
  id: "Cook_Prepare_Close_And_Open"
  content: "将归还您还未售出的美食花费，并重新支付费用，是否确认以当前美食开始营业？"
  switch: 1
}
rows {
  id: "Cook_Prepare_Continue_Open"
  content: "是否确认花费<img id=\"T_Farmyard_Icon_Coin_01\"></>%s补充食材？\n（实际花费金额会随食材消耗而增加）"
  switch: 1
}
rows {
  id: "Cook_Prepare_Close"
  content: "终止经营后返还<img id=\"T_Farmyard_Icon_Coin_01\"></>%s，确定终止吗？"
  switch: 1
}
rows {
  id: "Cook_Prepare_Selected_Locked"
  content: "该美食还未解锁"
  switch: 1
}
rows {
  id: "Cook_Prepare_Star_Tip"
  content: "难度越高，所需厨师厨艺越高，否则会影响美食售价"
  switch: 1
}
rows {
  id: "Cook_Prepare_Time_Tip"
  content: "厨师速度和厨灶等级影响实际烹饪时间"
  switch: 1
}
rows {
  id: "Cook_Prepare_Add_Dish_Success"
  content: "成功补充了食材，可以再营业24小时啦"
  switch: 1
}
rows {
  id: "Cook_Prepare_Unlock"
  content: "餐厅{0}级可解锁第<Orange19F>{1}</>个菜单位"
  switch: 1
}
rows {
  id: "Cook_Prepare_1"
  content: "剩余美食可维持的营业时长，可通过补充食材延长时间"
  switch: 1
}
rows {
  id: "Cook_Prepare_2"
  content: "在这里选择更多美食"
  switch: 1
}
rows {
  id: "Cook_Prepare_3"
  content: "可售卖美食：<Red17F>{0}</>/<Gray19F>{1}</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_4"
  content: "可售卖美食：<Gray19F>{0}</>/<Gray19F>{1}</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_5"
  content: "你已解锁全部五个菜单位"
  switch: 1
}
rows {
  id: "Cook_Prepare_6"
  content: "刚开始营业哦，请过会儿再来补充食材吧"
  switch: 1
}
rows {
  id: "Cook_Prepare_7"
  content: "菜单更换后，备菜成本降低，\n将返还约<img id=\"T_Farmyard_Icon_Coin_01\"></>%s，是否确认开始经营？"
  switch: 1
}
rows {
  id: "Cook_Prepare_8"
  content: "菜单更换后，备菜成本提高，\n将扣除约<img id=\"T_Farmyard_Icon_Coin_01\"></>%s，是否确认开始经营？"
  switch: 1
}
rows {
  id: "Cook_Prepare_9"
  content: "你已经解锁了新的菜单位，\n请前往编辑菜单进行调整"
  switch: 1
}
rows {
  id: "Cook_Prepare_10"
  content: "编辑菜单"
  switch: 1
}
rows {
  id: "Cook_Prepare_11"
  content: "%s<Green21F>(精湛)</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_12"
  content: "%s<Green21F>(从容)</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_13"
  content: "%s<Orange21F>(稳定)</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_14"
  content: "%s<Orange21F>(吃力)</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_15"
  content: "%s<Red21F>(翻车)</>"
  switch: 1
}
rows {
  id: "Cook_Prepare_16"
  content: "需要%s级餐厅才能烹饪"
  switch: 1
}
rows {
  id: "Cook_Prepare_17"
  content: "是否花费<img id=\"T_Farmyard_Icon_Coin_01\"></>%s开始经营？"
  switch: 1
}
rows {
  id: "Cook_Prepare_18"
  content: "有新的美食解锁"
  switch: 1
}
rows {
  id: "Cook_Prepare_19"
  content: "请选择更多美食"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_1"
  content: "<Orange20F>{0}</>后到访，\n之后会有<Orange20F>{1}</>保护时间"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_2"
  content: "接单成功，请等待贵宾到来吧！"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_3"
  content: "是否确认取消预约？预约的贵宾将不会到访"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_4"
  content: "只能同时接受一单预约哦"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_5"
  content: "请先接待前一批贵宾，才能继续接受预约"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_6"
  content: "接受预约"
  switch: 1
}
rows {
  id: "Cook_VIP_Reservation_7"
  content: "取消预约"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_1"
  content: "暂时没有贵宾到访哦"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_2"
  content: "<Orange19F>{0}</><Gray19F>后到访</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_3"
  content: "到访后有<Orange19F>{0}</>保护时间"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_4"
  content: "<Orange19F>{0}</><Gray19F>后生气</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_5"
  content: "可被其他人邀请"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_Clock_1"
  content: "<Orange19F>{0}</><Gray19F>到访</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Reserved_Clock_2"
  content: "<Orange19F>{0}</><Gray19F>生气</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_1"
  content: "太多贵宾在你的餐厅等待啦，先去邀请他们落座吃饭吧"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_2"
  content: "等位中…"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_3"
  content: "<Red19F>可被其他人邀请</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_4"
  content: "是否取消保护期？"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_5"
  content: "邀请成功，请等待贵宾落座用餐"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_6"
  content: "当前没有菜可以提供，请先备菜开始经营吧"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_7"
  content: "找不到可以用餐的座位"
  switch: 1
}
rows {
  id: "Cook_VIP_Invite_8"
  content: "当前贵宾已被邀请"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_1"
  content: "太多贵宾在你的餐厅等待啦，先去邀请他们落座吃饭吧"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_2"
  content: "你的行为已经被注意到了，请过%s再来吧"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_3"
  content: "拉客需要消耗10次拿取次数，是否继续？"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_4"
  content: "次数不足，拉客需要消耗10次拿取次数"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_5"
  content: "<Orange19F>贵宾{0}</>"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_6"
  content: "当前贵宾在保护期，暂时无法偷取"
  switch: 1
}
rows {
  id: "Cook_VIP_Steal_7"
  content: "拉客成功，请回自己的餐厅邀请贵宾入座吧"
  switch: 1
}
rows {
  id: "Cook_Upgrade_1"
  content: "<Orange24>餐厅经验</>不足，顾客结账时会获得经验哦"
  switch: 1
}
rows {
  id: "Cook_Upgrade_2"
  content: "人气值影响实际客流量"
  switch: 1
}
rows {
  id: "Cook_Mainview_1"
  content: "{0}的餐厅"
  switch: 1
}
rows {
  id: "Cook_Mainview_2"
  content: "营业中"
  switch: 1
}
rows {
  id: "Cook_Mainview_3"
  content: "<TopInfoClose>休息中</>"
  switch: 1
}
rows {
  id: "Cook_Comment_1"
  content: "最新的10条点评平均得分"
  switch: 1
}
rows {
  id: "Cook_Comment_2"
  content: "（商家）"
  switch: 1
}
rows {
  id: "Cook_Comment_3"
  content: "超出字数上限，请修改后发送"
  switch: 1
}
rows {
  id: "Cook_Comment_4"
  content: "已达今日回复上限，请明天再来吧"
  switch: 1
}
rows {
  id: "Cook_Blocked_1"
  content: "该餐厅存在违规行为，暂时无法进入哦"
  switch: 1
}
rows {
  id: "Cook_Blocked_2"
  content: "您存在违规行为，截止{0}前无法进入餐厅哦"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_1"
  content: "营业中"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_2"
  content: "未营业"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_3"
  content: "待领取<img id=\"T_Farmyard_Icon_Coin_01\"></><Orange19H>%s</>"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_4"
  content: "经营剩余时间为%s"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_5"
  content: "<img id=\"T_Farmyard_Icon_Exp\"></>%s\n<img id=\"T_FarmCook_pop_img_like\"></>%s\n<img id=\"T_Farmyard_Icon_Coin_01\"></>%s"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_6"
  content: "暂无离店收益"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_7"
  content: "暂无可领取的离店收益"
  switch: 1
}
rows {
  id: "Farmyard_Villager_12"
  content: "每增加100好感度，居民会讲述TA的故事并附赠农场经验奖励"
  switch: 1
}
rows {
  id: "FarmPermission_6"
  content: "因农场主人设置，暂时无法留言哦"
  switch: 1
}
rows {
  id: "Cook_Steal_1"
  content: "您被赶走了，已返回自己的餐厅"
  switch: 1
}
rows {
  id: "Cook_Jump_1"
  content: "解锁餐厅玩法后，才能进入别人的餐厅"
  switch: 1
}
rows {
  id: "Cook_Jump_2"
  content: "当前玩家还未创建餐厅"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_SetTimeTitle"
  content: "签名显示时间"
  switch: 1
}
rows {
  id: "Cook_Panel_1"
  content: "闲置中…"
  switch: 1
}
rows {
  id: "Cook_Panel_2"
  content: "前往清理…"
  switch: 1
}
rows {
  id: "Cook_Panel_3"
  content: "清理<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_4"
  content: "拿取<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_5"
  content: "上菜<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_6"
  content: "烹饪<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_7"
  content: "等取<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_8"
  content: "等位中…"
  switch: 1
}
rows {
  id: "Cook_Panel_9"
  content: "前往餐位…"
  switch: 1
}
rows {
  id: "Cook_Panel_10"
  content: "点餐中…"
  switch: 1
}
rows {
  id: "Cook_Panel_11"
  content: "等待<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_12"
  content: "品尝<Orange19F>{0}</>"
  switch: 1
}
rows {
  id: "Cook_Panel_13"
  content: "用餐完毕"
  switch: 1
}
rows {
  id: "Cook_Panel_14"
  content: "生气离开"
  switch: 1
}
rows {
  id: "Cook_Panel_15"
  content: "口味"
  switch: 1
}
rows {
  id: "Cook_Panel_16"
  content: "服务"
  switch: 1
}
rows {
  id: "Cook_Panel_17"
  content: "<Red19F>可被其他人邀请</>"
  switch: 1
}
rows {
  id: "Cook_Panel_18"
  content: "无法上菜"
  switch: 1
}
rows {
  id: "Cook_Panel_19"
  content: "出门中"
  switch: 1
}
rows {
  id: "Cook_Panel_20"
  content: "<Orange19F>{0}</>后完成清理"
  switch: 1
}
rows {
  id: "Cook_Panel_21"
  content: "<Orange19F>{0}</>后完成烹饪"
  switch: 1
}
rows {
  id: "Cook_Panel_22"
  content: "<Orange19F>{0}</>后生气离开"
  switch: 1
}
rows {
  id: "Cook_Panel_23"
  content: "<Orange19F>{0}</>后生气离开"
  switch: 1
}
rows {
  id: "Cook_Loading_1"
  content: "金特效的大款顾客会付3倍餐费。"
  switch: 1
}
rows {
  id: "Cook_Loading_2"
  content: "彩特效的富豪顾客会付10倍餐费。"
  switch: 1
}
rows {
  id: "Cook_Loading_3"
  content: "餐厅门口排队的顾客最多4人。"
  switch: 1
}
rows {
  id: "Cook_Loading_4"
  content: "等座或等餐过久的顾客会生气离开。"
  switch: 1
}
rows {
  id: "Cook_Loading_5"
  content: "置菜位满了，厨师将暂停烹饪。"
  switch: 1
}
rows {
  id: "Cook_Loading_6"
  content: "周末餐厅收益双倍哦！"
  switch: 1
}
rows {
  id: "Cook_Loading_7"
  content: "高等级餐厅更易招到优质员工。"
  switch: 1
}
rows {
  id: "Cook_Loading_8"
  content: "优质员工的雇佣费更高。"
  switch: 1
}
rows {
  id: "Cook_Loading_9"
  content: "靠近前台可打开员工界面。"
  switch: 1
}
rows {
  id: "Cook_Loading_10"
  content: "在员工界面可安排员工工作或休息。"
  switch: 1
}
rows {
  id: "FarmRestaurant_check1"
  content: "功能未开放，敬请期待"
  switch: 1
}
rows {
  id: "FarmRestaurant_check2"
  content: "餐厅{0}级才能解锁哦"
  switch: 1
}
rows {
  id: "RestaurantUnlock"
  content: "餐厅{0}级才能解锁哦"
  switch: 1
}
rows {
  id: "CookDataTips9"
  content: "餐厅家具容纳数已达上限"
  switch: 1
}
rows {
  id: "Cook_Leave_1"
  content: "离线期间，餐厅经营收益将持续累积，可在前台领取离店收益。"
  switch: 1
}
rows {
  id: "Cook_Leave_2"
  content: "确认返回广场吗？离开后，餐厅将会继续经营，返回时可以在前台处领取离店收益"
  switch: 1
}
rows {
  id: "Cook_Screen_InitText"
  content: "输入文本(中文15个字）"
  switch: 1
}
rows {
  id: "Cook_Screen_HintText"
  content: "输入文本(中文15个字）"
  switch: 1
}
rows {
  id: "Cook_Screen_Title"
  content: "点击输入想要的内容"
  switch: 1
}
rows {
  id: "Cook_Screen_Normal"
  content: "欢迎光临"
  switch: 1
}
rows {
  id: "CookDataTips10"
  content: "可摆放更多功能家具"
  switch: 1
}
rows {
  id: "Cook_Loading_11"
  content: "在员工界面可招聘新员工。"
  switch: 1
}
rows {
  id: "Cook_Loading_12"
  content: "速度影响服务员或厨师的工作效率。"
  switch: 1
}
rows {
  id: "Cook_Loading_13"
  content: "推荐雇佣与烹饪难度匹配的厨师。"
  switch: 1
}
rows {
  id: "Cook_Loading_14"
  content: "可在前台收取离线收益。"
  switch: 1
}
rows {
  id: "Cook_Loading_15"
  content: "关门后顾客不再进店。"
  switch: 1
}
rows {
  id: "Cook_Loading_16"
  content: "关门不影响已进入餐厅的顾客。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11"
  content: "餐厅"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_1"
  content: "餐厅升级"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_1_Text"
  content: "获得足够的餐厅经验后，可消耗农场币进行餐厅升级。\n升级餐厅能解锁更多美食。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_2"
  content: "菜谱和备菜营业"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_2_Text"
  content: "靠近前台家具，点击菜单按钮，\n选择今日菜单上想要售卖的美食，开始经营餐厅吧。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_3"
  content: "离线收益"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_3_Text"
  content: "离线期间，餐厅经营收益将持续累积，可在前台领取离线收益。\n如果长时间不领取，下次进入餐厅时会自动领取哦。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_4"
  content: "员工管理"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_4_Text"
  content: "靠近前台家具，点击员工按钮，打开员工界面。\n可以在员工页面替换、休息、解雇和招聘员工。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_5"
  content: "雇佣员工"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_5_Text"
  content: "在员工界面中点击去招聘按钮，打开人才市场界面。\n可以在人才市场消耗农场币刷新员工列表，招聘心仪的员工。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_6"
  content: "贵宾预约"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_6_Text"
  content: "点击餐厅入口处的预约按钮，\n可以查看预约单并接受预约，接待贵宾可获得大量收益。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_7"
  content: "拉客"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_7_Text"
  content: "在好友餐厅里见到生气状态的贵宾，\n可以点击拉客按钮，招待该贵宾到自己的农场餐厅用餐。"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_8"
  content: "点评"
  switch: 1
}
rows {
  id: "Farmyard_Intro11_8_Text"
  content: "顾客进入餐厅后，会在各阶段对餐厅进行点评。\n不过别担心，点评分数不会影响餐厅的收益"
  switch: 1
}
rows {
  id: "CookReport_3"
  content: "由于违规行为，当前无法修改显示屏文本，截止日期为：{0}"
  switch: 1
}
rows {
  id: "Farmyard_Social_25"
  content: "已永久屏蔽该好友的\"可拿取\"提醒"
  switch: 1
}
rows {
  id: "Farmyard_Social_26"
  content: "已恢复该好友的\"可拿取\"提醒"
  switch: 1
}
rows {
  id: "Farmyard_Social_27"
  content: "还没有被祈福屏蔽的好友"
  switch: 1
}
rows {
  id: "Farmyard_Social_28"
  content: "还没有被提醒屏蔽的好友"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_ShowTimeCD"
  content: "剩余显示时间：{0}"
  switch: 1
}
rows {
  id: "HouseDecorationTips_669"
  content: "前往祈愿系统参与活动获取"
  switch: 1
}
rows {
  id: "Cook_MenuText1"
  content: "功能"
  switch: 1
}
rows {
  id: "Cook_MenuText2"
  content: "T_FarmyardHouse_Icon_Cook"
  switch: 1
}
rows {
  id: "HouseDecorationTips14"
  content: "先收起/移动放在墙面连接处的家具吧"
  switch: 1
}
rows {
  id: "CookDataTips11"
  content: "结账时美食售价"
  switch: 1
}
rows {
  id: "CookDataTips12"
  content: "顾客移动速度"
  switch: 1
}
rows {
  id: "Cook_ExpelLimit"
  content: "今天赶走太多人啦~明天再任性吧"
  switch: 1
}
rows {
  id: "Cook_Screen_Check"
  content: "内容修改成功"
  switch: 1
}
rows {
  id: "HomeBluePrint_16"
  content: "员工已被解雇，需要重新安排上岗"
  switch: 1
}
rows {
  id: "HomeBluePrint_17"
  content: "编辑图纸时不可招聘"
  switch: 1
}
rows {
  id: "HomeBluePrint_18"
  content: "编辑图纸时不可解雇"
  switch: 1
}
rows {
  id: "CookQuickUpgrade22"
  content: "顾客移动速度增加"
  switch: 1
}
rows {
  id: "CookQuickUpgrade23"
  content: "结账时美食售价"
  switch: 1
}
rows {
  id: "CookDecorationStaffFired"
  content: "解雇原场景员工后会影响餐厅收益哦，请发布后再解雇吧"
  switch: 1
}
rows {
  id: "CookRestroomNewCook"
  content: "有新的厨师来到了休息室，快去看看吧"
  switch: 1
}
rows {
  id: "CookStaffEmployment20"
  content: "餐厅{0}级后有概率刷新{1}品质员工"
  switch: 1
}
rows {
  id: "CookStaffEmployment21"
  content: "当前餐厅有概率刷新到所有品质的员工"
  switch: 1
}
rows {
  id: "Cook_FrontDesk_8"
  content: "离店或装修时依然产生收益"
  switch: 1
}
rows {
  id: "Cook_Prepare_20"
  content: "当前餐厅不在营业中，无法查看菜单"
  switch: 1
}
rows {
  id: "CookStaffGreen"
  content: "餐厅{0}级后有概率刷新出<CookGreen20F>熟练</>品质员工"
  switch: 1
}
rows {
  id: "CookStaffBlue"
  content: "餐厅{0}级后有概率刷新出<CookBlue20F>资深</>品质员工"
  switch: 1
}
rows {
  id: "CookStaffPurple"
  content: "餐厅{0}级后有概率刷新出<CookPurple20F>知名</>品质员工"
  switch: 1
}
rows {
  id: "CookStaffOrange"
  content: "餐厅{0}级后有概率刷新出<CookOrange20F>传奇</>品质员工"
  switch: 1
}
rows {
  id: "FarmDroneFishingTips"
  content: "如果勾选鱼缸和收鱼，将在收鱼后自动收取水族箱产出的农场币"
  switch: 1
}
rows {
  id: "Cook_Scene1"
  content: "餐厅暂未解锁"
  switch: 1
}
rows {
  id: "Cook_Scene2"
  content: "您已经在当前餐厅中了"
  switch: 1
}
rows {
  id: "CookStaffFiredAll"
  content: "确定要解雇这些员工吗？"
  switch: 1
}
rows {
  id: "Cook_Advice_1"
  content: "<PrepareSuggestion>菜单建议：</>"
  switch: 1
}
rows {
  id: "Cook_Advice_2"
  content: "不同的美食会有烹饪和清洁时长差异，请根据员工的速度选择合适的美食。"
  switch: 1
}
rows {
  id: "Cook_Advice_3"
  content: "<PrepareSuggestion>员工建议：</>"
  switch: 1
}
rows {
  id: "Cook_Advice_4"
  content: "员工魅力的提升可以增加餐厅的客流量，但同时也需要速度更快的员工进行接待，否则顾客就会等不及离开，请根据实际情况来平衡员工的魅力和速度吧。"
  switch: 1
}
rows {
  id: "Cook_Advice_5"
  content: "<PrepareSuggestion>布局建议：</>"
  switch: 1
}
rows {
  id: "Cook_Advice_6"
  content: "调整家具布局和员工位置，缩短顾客和服务员的移动距离，建议多观察餐厅实际运行情况并进行调整！"
  switch: 1
}
rows {
  id: "Cook_Advice_7"
  content: "由于每小时预估收益是模拟餐厅运营的结果，所以<Orange19F>在个别情况下可能会出现提高员工数值或人气反而增加超时离开顾客的情况</>，可通过“模拟详情”查看运营详情，建议通过调整员工和布局来进行优化！"
  switch: 1
}
rows {
  id: "Cook_Advice_8"
  content: "以下为30分钟模拟运营详情"
  switch: 1
}
rows {
  id: "Cook_Advice_9"
  content: "来访顾客："
  switch: 1
}
rows {
  id: "Cook_Advice_10"
  content: "结账顾客："
  switch: 1
}
rows {
  id: "Cook_Advice_11"
  content: "超时离开顾客："
  switch: 1
}
rows {
  id: "Cook_Advice_12"
  content: "剩余<Orange22F>%s人</>排队中或未完成用餐"
  switch: 1
}
rows {
  id: "Cook_Advice_13"
  content: "<Orange22F>%s人</>"
  switch: 1
}
rows {
  id: "Cook_Advice_14"
  content: "暂留备用"
  switch: 1
}
rows {
  id: "HouseDecorationTips26"
  content: "厨灶售卖即将开启"
  switch: 1
}
rows {
  id: "CookFiredChoose"
  content: "请选择需要解雇的员工"
  switch: 1
}
rows {
  id: "Cook_Appoint_1"
  content: "所有员工"
  switch: 1
}
rows {
  id: "Cook_Appoint_2"
  content: "仅刷厨师"
  switch: 1
}
rows {
  id: "Cook_Appoint_3"
  content: "仅刷服务员"
  switch: 1
}
rows {
  id: "CookRefreshTips1"
  content: "刷新费用发生变化"
  switch: 1
}
rows {
  id: "Cook_Prepare_21"
  content: "菜单"
  switch: 1
}
rows {
  id: "Cook_Prepare_22"
  content: "编辑菜单"
  switch: 1
}
rows {
  id: "Cook_Advice_15"
  content: "<PrepareSuggestion>餐厅运营情况：</>"
  switch: 1
}
rows {
  id: "Cook_Advice_16"
  content: "顾客超时离开："
  switch: 1
}
rows {
  id: "Cook_Advice_17"
  content: "厨师空闲时间："
  switch: 1
}
rows {
  id: "Cook_Advice_18"
  content: "服务员空闲时间："
  switch: 1
}
rows {
  id: "Farmsteer_Sow"
  content: "前往播种"
  switch: 1
}
rows {
  id: "Farmsteer_Watering"
  content: "前往浇水"
  switch: 1
}
rows {
  id: "Farmsteer_Harvest"
  content: "收获作物"
  switch: 1
}
rows {
  id: "Farmsteer_Sale"
  content: "前往售卖"
  switch: 1
}
rows {
  id: "Farmsteer_Upgrade"
  content: "升级小屋"
  switch: 1
}
rows {
  id: "Farmsteer_Reclamation"
  content: "开垦土地"
  switch: 1
}
rows {
  id: "HouseDecorationTips_696"
  content: "前往发现系统参与活动获取"
  switch: 1
}
rows {
  id: "Cook_Appoint_4"
  content: "指定刷新厨师/服务员，刷新价格会提升<Orange28>30%</>"
  switch: 1
}
rows {
  id: "FarmFriend_Signature_TimeLessHour"
  content: "小于{0}小时"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_1"
  content: "确定要解雇<CookPurple28F>知名</>员工<Orange28>{0}</>么？解雇返还<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信。"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_2"
  content: "确定要解雇<CookOrange28F>传奇</>员工<Orange28>{0}</>么？解雇返还<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信。"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_3"
  content: "确定要解雇<CookPurple28F>知名</>员工<Orange28>{0}</>么？解雇返还<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信，该员工当前服务的客人会离开哦~"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_4"
  content: "确定要解雇<CookPurple28F>知名</>员工<Orange28>{0}</>么？解雇返还<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信，该员工当前服务的客人会离开哦~"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_5"
  content: "确认要解雇<Orange28>{0}</>名<CookPurple28F>知名</>员工，<Orange28>{1}</>名<CookOrange28F>传奇</>员工吗？解雇返还<img id=\"T_FarmCook_Talent\"></><Orange24>{2}</>张人才推荐信。"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_6"
  content: "普通"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_7"
  content: "高级"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_8"
  content: "人才推荐信可用来刷新高级人才市场员工，解雇知名或传奇员工可获得"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_9"
  content: "人才推荐信不足，解雇知名或传奇员工可获得"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_10"
  content: "<Orange28>{0}</>是<CookPurple28F>知名</>员工，需要消耗<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信进行雇佣，确认是否雇佣？"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_11"
  content: "<Orange28>{0}</>是<CookOrange28F>传奇</>员工，需要消耗<img id=\"T_FarmCook_Talent\"></><Orange28>{1}</>封人才推荐信进行雇佣，确认是否雇佣？"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_12"
  content: "确定要刷新吗？刷新后，剩余的人才推荐信无法再雇佣一名高级人才"
  switch: 1
}
rows {
  id: "CookStaffFiredAll2"
  content: "这些员工被解雇了"
  switch: 1
}
rows {
  id: "DronePlan"
  content: "方案"
  switch: 1
}
rows {
  id: "Cook_Simulation_1"
  content: "成功切换为快速播放"
  switch: 1
}
rows {
  id: "Cook_Simulation_2"
  content: "成功切换为常速播放"
  switch: 1
}
rows {
  id: "Cook_Simulation_3"
  content: "确定要退出模拟详情吗？"
  switch: 1
}
rows {
  id: "Cook_Simulation_4"
  content: "餐厅30分钟模拟结算"
  switch: 1
}
rows {
  id: "Cook_Simulation_5"
  content: "结算"
  switch: 1
}
rows {
  id: "Cook_Simulation_6"
  content: "来访顾客："
  switch: 1
}
rows {
  id: "Cook_Simulation_7"
  content: "结账顾客："
  switch: 1
}
rows {
  id: "Cook_Simulation_8"
  content: "超时离开顾客："
  switch: 1
}
rows {
  id: "Cook_Simulation_9"
  content: "剩余<Orange22F>%s人</>排队中或未完成用餐"
  switch: 1
}
rows {
  id: "Cook_Simulation_10"
  content: "<Orange22F>%s人</>"
  switch: 1
}
rows {
  id: "Cook_SaveMenu_1"
  content: "请准备足够的美食再保存"
  switch: 1
}
rows {
  id: "Cook_SaveMenu_2"
  content: "保存成功"
  switch: 1
}
rows {
  id: "Cook_SaveMenu_3"
  content: "你还没有保存当前菜单，是否需要保存？"
  switch: 1
}
rows {
  id: "Cook_SaveMenu_4"
  content: "你还没有保存当前菜单，是否保存菜单并花费<img id=\"T_Farmyard_Icon_Coin_01\"></>%s开始经营？"
  switch: 1
}
rows {
  id: "Cook_Prepare_23"
  content: "每小时预估收益是模拟餐厅1小时运行的预估结果，\n餐厅中任何微小的变化都可能会产生顾客和员工的连锁反应，\n导致顾客接待情况有所波动，建议多尝试调整并以实际收益为准。"
  switch: 1
}
rows {
  id: "Cook_Prepare_24"
  content: "还未应用更换的美食，是否确认返回？"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_1"
  content: "当前没有贵宾在来访途中，无法缩短等待时间。"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_2"
  content: "贵宾已经到达餐厅，无法缩短等待时间，快去邀请贵宾入座用餐吧。"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_3"
  content: "是否使<Orange28>贵宾到访</>时间提前{0}小时，<Orange28>餐厅营业</>时间加速{1}小时?（{2}小时后结束营业，建议续菜后再使用）"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_4"
  content: "是否使<Orange28>贵宾到访</>时间提前{0}小时，<Orange28>餐厅营业</>时间加速{1}小时？"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_5"
  content: "是否使<Orange28>贵宾到访</>时间提前{0}小时，<Orange28>餐厅营业</>时间加速{1}小时?（{2}小时后结束营业，建议续菜后再使用）\n<Orange28>（周末双倍期间消耗双倍时间）</>"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_6"
  content: "是否使<Orange28>贵宾到访</>时间提前{0}小时，<Orange28>餐厅营业</>时间加速{1}小时？\n<Orange28>（周末双倍期间消耗双倍时间）</>"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_7"
  content: "缩短<Orange21F>贵宾</>到访时间\n缩短<Orange21F>餐厅</>营业时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_8"
  content: "缩短<Orange21F>贵宾</>到访时间"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_9"
  content: "不需要更多时间就可以到访啦"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_10"
  content: "餐厅达到10级并接受贵宾预约后，可使用仙术"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Cook_11"
  content: "餐厅达到10级并接受贵宾预约后，可使用仙术"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Flower_1"
  content: "同步加速霸王吞吞花"
  switch: 1
}
rows {
  id: "FarmMagic_ReduceTime_Flower_2"
  content: "当前农田内只有霸王吞吞花可以加速，请勾选后再确认使用哦"
  switch: 1
}
rows {
  id: "Farmyard_GodState_LeftTime"
  content: "本周剩余卖花次数: {0}"
  switch: 1
}
rows {
  id: "StealRecord_Type_0"
  content: "全部"
  switch: 1
}
rows {
  id: "StealRecord_Type_1"
  content: "作物"
  switch: 1
}
rows {
  id: "StealRecord_Type_2"
  content: "动物"
  switch: 1
}
rows {
  id: "StealRecord_Type_3"
  content: "鱼"
  switch: 1
}
rows {
  id: "StealRecord_Type_4"
  content: "餐厅"
  switch: 1
}
rows {
  id: "StealRecord_Type_5"
  content: "矿产"
  switch: 1
}
rows {
  id: "StealRecord_TotalText"
  content: "拿取总数：{0}次        拿取总价值：<img id=\"T_Farmyard_Icon_Coin_01\"></>{1}"
  switch: 1
}
rows {
  id: "StealRecord_Friend_1"
  content: "拿取价值：<img id=\"T_Farmyard_Icon_Coin_01\"></>{0}"
  switch: 1
}
rows {
  id: "StealRecord_None_1"
  content: "暂无记录"
  switch: 1
}
rows {
  id: "StealRecord_None_2"
  content: "暂无拿取记录"
  switch: 1
}
rows {
  id: "StealRecord_FishDetail"
  content: "其中包含<Orange21F>{0}</>条奇珍鱼，<Orange21F>{1}</>条金冠稀世鱼"
  switch: 1
}
rows {
  id: "StealRecord_Title_1"
  content: "本日"
  switch: 1
}
rows {
  id: "StealRecord_Title_2"
  content: "本周"
  switch: 1
}
rows {
  id: "StealRecord_Title_3"
  content: "本月"
  switch: 1
}
rows {
  id: "StealRecord_Running"
  content: "进行中"
  switch: 1
}
rows {
  id: "StealRecord_Ground_1"
  content: "<Orange24F>丰收</>"
  switch: 1
}
rows {
  id: "StealRecord_Ground_2"
  content: "<Orange24F>大丰收</>"
  switch: 1
}
rows {
  id: "StealRecord_Ground_3"
  content: "<Orange24F>金色</>"
  switch: 1
}
rows {
  id: "StealRecord_Ground_4"
  content: "<Orange24F>炫彩</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Head"
  content: "<Orange24F>{0}</>前你从<Orange24F>{1}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_1"
  content: "的{0}农田里拿取了{1}个<Orange24F>{2}</>，价值<Orange24F>{3}农场币</>。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_2"
  content: "的{0}牧场里拿取了{1}个<Orange24F>{2}</>，价值<Orange24F>{3}农场币</>。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_3"
  content: "的{0}鱼塘里捕获了{1}个{2}，价值<Orange24F>{3}农场币</>。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_4"
  content: "的{0}鱼塘里捕鱼失败，获得了<Orange24F>{1}农场币</>。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_5"
  content: "，卡包熟练度转换价值<Orange24F>{0}农场币</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_6"
  content: "的{0}鱼塘里捕获了{1}个{2}{3}。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_7"
  content: "的餐厅里带回了贵宾<Orange24F>{0}</>，价值<Orange24F>{1}农场币</>。"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_8"
  content: "暂无内容"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_9"
  content: "暂无内容"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Tail_10"
  content: "暂无内容"
  switch: 1
}
rows {
  id: "StealRecord_1"
  content: "{0}次"
  switch: 1
}
rows {
  id: "StealRecord_2"
  content: "记录"
  switch: 1
}
rows {
  id: "StealRecord_3"
  content: "日报"
  switch: 1
}
rows {
  id: "StealRecord_4"
  content: "周报"
  switch: 1
}
rows {
  id: "StealRecord_5"
  content: "月报"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_1"
  content: "<LightGray24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_2"
  content: "<Green24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_3"
  content: "<Blue24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_4"
  content: "<Purple24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_5"
  content: "<Orange24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_Fish_6"
  content: "<Orange24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_FishCard_1"
  content: "<Green24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_FishCard_2"
  content: "<Blue24F>{0}</>"
  switch: 1
}
rows {
  id: "StealRecord_DetailRecord_FishCard_3"
  content: "<Purple24F>{0}</>"
  switch: 1
}
rows {
  id: "Farmyard_Collection_10"
  content: "基础类"
  switch: 1
}
rows {
  id: "Farmyard_Collection_11"
  content: "活动类"
  switch: 1
}
rows {
  id: "ItemNum_Exp"
  content: "数量:{0}"
  switch: 1
}
rows {
  id: "ItemNum_Possess"
  content: "拥有:{0}"
  switch: 1
}
rows {
  id: "ItemType_1100"
  content: "货币"
  switch: 1
}
rows {
  id: "ItemType_1101"
  content: "作物产出物"
  switch: 1
}
rows {
  id: "ItemType_1103"
  content: "畜牧产出物"
  switch: 1
}
rows {
  id: "ItemType_1105"
  content: "鱼"
  switch: 1
}
rows {
  id: "ItemType_1109"
  content: "作物加工物"
  switch: 1
}
rows {
  id: "ItemType_1110"
  content: "畜牧加工物"
  switch: 1
}
rows {
  id: "ItemType_1117"
  content: "农场经验"
  switch: 1
}
rows {
  id: "ItemType_1119"
  content: "餐厅经验"
  switch: 1
}
rows {
  id: "ItemDesc_1100"
  content: "用于星宝农场的日常交易、使用及升级"
  switch: 1
}
rows {
  id: "ItemDesc_1101"
  content: "用于在蔬菜摊出售可获得农场币"
  switch: 1
}
rows {
  id: "ItemDesc_1103"
  content: "用于在牧场小铺出售可获得农场币"
  switch: 1
}
rows {
  id: "ItemDesc_1105"
  content: "用于在星宝农场出售可获得大量农场币"
  switch: 1
}
rows {
  id: "ItemDesc_1109"
  content: "用于在星宝农场出售可获得大量农场币"
  switch: 1
}
rows {
  id: "ItemDesc_1110"
  content: "用于在星宝农场出售可获得大量农场币"
  switch: 1
}
rows {
  id: "ItemDesc_1117"
  content: "用于星宝农场小屋升级"
  switch: 1
}
rows {
  id: "ItemDesc_1119"
  content: "用于星宝农场的餐厅升级"
  switch: 1
}
rows {
  id: "ItemTypeTips_1101"
  content: "作物"
  switch: 1
}
rows {
  id: "ItemTypeTips_1104"
  content: "动物"
  switch: 1
}
rows {
  id: "ItemTypeTips_1108"
  content: "家具"
  switch: 1
}
rows {
  id: "ItemTypeTips_1109"
  content: "加工物"
  switch: 1
}
rows {
  id: "ItemTypeTips_1110"
  content: "加工物"
  switch: 1
}
rows {
  id: "ItemTypeTips_1111"
  content: "收藏品"
  switch: 1
}
rows {
  id: "ItemTypeTips_1118"
  content: "美食"
  switch: 1
}
rows {
  id: "ItemTypeTips_1103"
  content: "动物"
  switch: 1
}
rows {
  id: "Cook_FarmLevel_Need"
  content: "农场等级需达到"
  switch: 1
}
rows {
  id: "Cook_FarmLevel_Finished"
  content: "农场等级已达到"
  switch: 1
}
rows {
  id: "Farmyard_PraySwitch_01"
  content: "农场主只允许祈福农田哦"
  switch: 1
}
rows {
  id: "Farmyard_PraySwitch_02"
  content: "农场主只允许祈福牧场哦"
  switch: 1
}
rows {
  id: "Cook_Leave_Exp"
  content: "餐厅离店经验已累积{0},前往前台领取餐厅经验后即可升级"
  switch: 1
}
rows {
  id: "Cook_Expect_Time"
  content: "预计升级到下一级需要:{0}"
  switch: 1
}
rows {
  id: "Cook_Please_Start"
  content: "请先开始营业吧！"
  switch: 1
}
rows {
  id: "ModelRoom_Text_1"
  content: "餐厅样板间"
  switch: 1
}
rows {
  id: "ModelRoom_Text_2"
  content: "热销样板间"
  switch: 1
}
rows {
  id: "ModelRoom_Text_3"
  content: "餐厅{0}级解锁"
  switch: 1
}
rows {
  id: "ModelRoom_Text_4"
  content: "确定要进入该样板间吗？"
  switch: 1
}
rows {
  id: "Cat_Text_1"
  content: "亲密度升满后，猫咪可被收养并留在农场。\n鱼塘解锁后猫咪还会帮忙捕鱼哦。"
  switch: 1
}
rows {
  id: "Cat_Text_UI_1"
  content: "期待一下猫咪的突然来访吧"
  switch: 1
}
rows {
  id: "Cat_Text_UI_2"
  content: "亲密度升满后可收养猫咪"
  switch: 1
}
rows {
  id: "Cat_Text_UI_3"
  content: "亲密度已满，快去收养猫咪吧"
  switch: 1
}
rows {
  id: "Cat_Text_2"
  content: "猫咪亲密度还不够，经常跟猫咪互动一下吧~"
  switch: 1
}
rows {
  id: "Cat_Text_3"
  content: "猫咪现在不想互动"
  switch: 1
}
rows {
  id: "Cook_Simulation_11"
  content: "顾客在排队时耐心耗尽，离开了"
  switch: 1
}
rows {
  id: "Cook_Simulation_12"
  content: "顾客在等菜时耐心耗尽，离开了"
  switch: 1
}
rows {
  id: "Cook_Simulation_13"
  content: "餐厅排队的人太多，顾客直接离开了"
  switch: 1
}
rows {
  id: "Cook_Simulation_14"
  content: "顾客在等菜时耐心耗尽，离开了"
  switch: 1
}
rows {
  id: "CookStaffAtLeastOne_2"
  content: "场景里没有员工，餐厅无法正常运转，确定要执行此操作吗？"
  switch: 1
}
rows {
  id: "ModelRoom_Text_5"
  content: "当前未选择收纳对象"
  switch: 1
}
rows {
  id: "CarWorkingTips4"
  content: "作物和地块不匹配，重新选择吧~"
  switch: 1
}
rows {
  id: "CookDecorationPlaceStaff1"
  content: "将员工放置在你需要的地方吧~"
  switch: 1
}
rows {
  id: "StealRecord_RestaurantDetail"
  content: "1次拉客消耗10次拿取次数"
  switch: 1
}
rows {
  id: "Cow_TaskTips"
  content: "完成阿花全部任务后可开启餐厅"
  switch: 1
}
rows {
  id: "Farmyard_CookVisitTips_01"
  content: "该餐厅的访客人数已达上限，请稍后访问"
  switch: 1
}
rows {
  id: "Farmyard_PraySwitch_03"
  content: "小红狐祈福不受该功能影响"
  switch: 1
}
rows {
  id: "Cook_Appoint_5"
  content: "当前刷新价格为基础价格的<Orange28>{0}</>倍是否继续刷新？"
  switch: 1
}
rows {
  id: "Cook_Appoint_6"
  content: "目前岗位已满，但可替换在岗员工"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_26"
  content: "显示分红记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_27"
  content: "<Orange19F>{0} </>离开，留下了剩余未领取的分红，总计"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_28"
  content: "精灵商人·星巴达"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_29"
  content: "分红记录"
  switch: 1
}
rows {
  id: "Farmyard_Fertilizer_30"
  content: "{0}农场币"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_13"
  content: "<Orange28>{0}</>名<CookPurple28F>知名</>员工"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_14"
  content: "<Orange28>{0}</>名<CookOrange28F>传奇</>员工"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_15"
  content: "解雇共可返还<img id=\"T_FarmCook_Talent\"></><Orange24>{0}</>封人才推荐信"
  switch: 1
}
rows {
  id: "Cook_SeniorTalents_16"
  content: "拥有人才推荐信：{0}"
  switch: 1
}
rows {
  id: "Cook_FoodInfo_1"
  content: "最短时长:%s秒  最长时长:%s秒\n厨师速度和厨灶等级可影响用时\n<Spacer1> </>\n表现评估从低到高分为5档:\n<Green21F>精湛</> <Green21F>从容</> <Orange21F>稳定</> <Orange21F>吃力</> <Red21F>翻车</> "
  switch: 1
}
rows {
  id: "Cook_FoodInfo_2"
  content: "最短时长:%s秒  最长时长:%s秒\n服务员速度可影响用时\n<Spacer1> </>\n表现评估从低到高分为5档:\n<Green21F>精湛</> <Green21F>从容</> <Orange21F>稳定</> <Orange21F>吃力</> <Red21F>翻车</> "
  switch: 1
}
rows {
  id: "Cook_Prepare_25"
  content: "熟练度：{0}/{1}"
  switch: 1
}
