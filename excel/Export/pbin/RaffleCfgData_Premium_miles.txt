com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_miles.xlsx sheet:奖池-至臻卡池
rows {
  poolId: 40000001
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 240
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 120
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 203
}
rows {
  poolId: 40000002
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 90
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 999
  }
  maxLimit: 999999
  dispersion {
    rewardRefreshPeriod: 90
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 5
  }
  mallVoucherId: 221
}
rows {
  poolId: 40000008
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      testWaterOptionNum: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      testWaterOptionNum: 2
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 240
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 120
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 203
  maxTestWaterLimit: 1
}
