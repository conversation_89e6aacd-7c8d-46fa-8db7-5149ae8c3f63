com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_主表.xlsx sheet:活动-翻牌卡池
rows {
  raffleId: 5201
  name: "蘑咕咕"
  startTime {
    seconds: 1703174400
  }
  endTime {
    seconds: 1704383999
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52010
    subPoolIds: 52011
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawCounts: 4
    poolDrawCounts: 7
    poolDrawWeights: 120
    poolDrawWeights: 100
    poolDrawWeights: 30
    poolDrawWeights: 10
    poolDrawWeights: 4
  }
  dailyLimit: 7
  maxLimit: 7
  textRuleId: 32
  lowestVersion: "0.6.1196.1"
  extraShow {
    cardRewardGroups: 1
    cardRewardGroups: 2
    cardRewardGroups: 4
    cardRewardGroups: 2
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 5
  }
  raffleTitle: "蘑咕咕的探索之路"
  raffleSubtitle: "走完全部格子，获得蘑咕咕套装！最低仅需<DrawRewardCardY22>1</>元！"
  showStartTime {
    seconds: 1703174400
  }
  showEndTime {
    seconds: 1704383999
  }
  isShow: true
}
rows {
  raffleId: 5203
  name: "轻羽仙子"
  startTime {
    seconds: 1717084800
  }
  endTime {
    seconds: 1718899199
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52030
    subPoolIds: 52031
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.2.100.1"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "古风少女的探索之路"
  raffleSubtitle: "走完全部格子，获得古风少女套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1717084800
  }
  showEndTime {
    seconds: 1718899199
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_KiteGirl_Popup"
}
rows {
  raffleId: 5204
  name: "雨荷仙子"
  startTime {
    seconds: 1720800000
  }
  endTime {
    seconds: 1722614399
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52040
    subPoolIds: 52041
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.7.122"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "雨荷仙子的探索之路"
  raffleSubtitle: "走完全部格子，获得雨荷仙子套装！最低仅需<DrawRewardCardY22>18</>星愿币！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1720800000
  }
  showEndTime {
    seconds: 1722614399
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_SummerPool_Popup"
}
rows {
  raffleId: 5205
  name: "沙海寻踪"
  startTime {
    seconds: 1724428800
  }
  endTime {
    seconds: 1725638399
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52050
    subPoolIds: 52051
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.12.128"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "沙海寻踪 探索之路"
  raffleSubtitle: "走完全部格子，获得最终套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1724428800
  }
  showEndTime {
    seconds: 1725638399
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_Anubis_Popup"
}
rows {
  raffleId: 5206
  name: "月半夜曲"
  startTime {
    seconds: 1732291200
  }
  endTime {
    seconds: 1734019199
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52060
    subPoolIds: 52061
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.26.111"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "星光乐师 探索之路"
  raffleSubtitle: "走完全部格子，获得最终套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1732291200
  }
  showEndTime {
    seconds: 1734019199
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_Nocturne_Popup"
}
rows {
  raffleId: 5207
  name: "春日精灵"
  startTime {
    seconds: 1736524800
  }
  endTime {
    seconds: 1737820799
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52070
    subPoolIds: 52071
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.37.104"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "春日精灵 探索之路"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1736524800
  }
  showEndTime {
    seconds: 1737820799
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_GardenGirl_Popup"
}
rows {
  raffleId: 5209
  name: "沙海寻踪"
  startTime {
    seconds: 1741276800
  }
  endTime {
    seconds: 1743695999
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52090
    subPoolIds: 52091
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.68.133"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "沙海寻踪 探索之路"
  raffleSubtitle: "走完全部格子，获得最终套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1741276800
  }
  showEndTime {
    seconds: 1743695999
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_Anubis_Popup"
}
rows {
  raffleId: 5210
  name: "墨影流光"
  startTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1748534399
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52100
    subPoolIds: 52101
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.78.99"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "墨影流光 探索之路"
  raffleSubtitle: "走完全部格子，获得最终套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_BlackGoldfish_Popup"
}
rows {
  raffleId: 5211
  name: "雨荷仙子"
  startTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1748534399
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52110
    subPoolIds: 52111
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.78.99"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "雨荷仙子的探索之路"
  raffleSubtitle: "走完全部格子，获得雨荷仙子套装！最低仅需<DrawRewardCardY22>18</>星愿币！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_SummerPool_Popup"
}
rows {
  raffleId: 5212
  name: "月华鹤影"
  startTime {
    seconds: 1750521600
  }
  endTime {
    seconds: 1752940799
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52120
    subPoolIds: 52121
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.78.99"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "白鹤少年的探索之路"
  raffleSubtitle: "走完全部格子，获得最终套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1750521600
  }
  showEndTime {
    seconds: 1752940799
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_WhiteCrane_Popup"
}
rows {
  raffleId: 5213
  name: "轻羽仙子"
  startTime {
    seconds: 1750521600
  }
  endTime {
    seconds: 1752940799
  }
  poolSelection {
    policy: RPP_OnSub
    poolId: 52130
    subPoolIds: 52131
    poolDrawCounts: 1
    poolDrawCounts: 2
    poolDrawCounts: 3
    poolDrawWeights: 180
    poolDrawWeights: 20
    poolDrawWeights: 10
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 153
  lowestVersion: "1.3.88.155"
  extraShow {
    cardRewardGroups: 12
    cardRewardGroups: 11
    cardRewardGroups: 10
    cardRewardGroups: 9
    cardRewardGroups: 8
    cardRewardGroups: 7
    cardRewardGroups: 6
    cardRewardGroups: 5
    cardRewardGroups: 4
    cardRewardGroups: 3
    cardRewardGroups: 2
    cardRewardGroups: 1
  }
  raffleTitle: "古风少女的探索之路"
  raffleSubtitle: "走完全部格子，获得古风少女套装！最低仅需<DrawRewardCardY22>1</>元！"
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 4
  viewIndex: 3
  viewIndex: 2
  viewIndex: 1
  showStartTime {
    seconds: 1750521600
  }
  showEndTime {
    seconds: 1752940799
  }
  isShow: true
  animType: 3
  relateUmgSetting: "1;UI_Lottery_KiteGirl_Popup"
}
