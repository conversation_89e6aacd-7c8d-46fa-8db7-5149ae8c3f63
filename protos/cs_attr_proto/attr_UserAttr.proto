syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "ResKeywords.proto";
import "attr_ABTestInfo.proto";
import "attr_AchievementInfo.proto";
import "attr_ActivityAddNewAidInfo.proto";
import "attr_ActivityCenter.proto";
import "attr_ActivitySquadDetail.proto";
import "attr_ActivitySquadInfo.proto";
import "attr_AiChatInfo.proto";
import "attr_AlbumInfo.proto";
import "attr_AlgoRecommendMatchInfo.proto";
import "attr_AppearanceRoad.proto";
import "attr_ArenaDailyVictoryRecord.proto";
import "attr_ArenaGameData.proto";
import "attr_ArenaSettings.proto";
import "attr_ArenaTipInfo.proto";
import "attr_AttrBirthdayData.proto";
import "attr_AttrClubInfo.proto";
import "attr_AttrDisplayBoardData.proto";
import "attr_AttrPermitInfo.proto";
import "attr_AttrRecentActivity.proto";
import "attr_AttrRewardRetrievalData.proto";
import "attr_AttrRoomInfo.proto";
import "attr_AttrSceneInfo.proto";
import "attr_BPInfo.proto";
import "attr_BagInfoDb.proto";
import "attr_BasicInfo.proto";
import "attr_BattleInfo.proto";
import "attr_BenefitCardInfo.proto";
import "attr_BubbleConfig.proto";
import "attr_ChaseGameData.proto";
import "attr_ChaseGameDataNoPublic.proto";
import "attr_ClientCache.proto";
import "attr_ClientLogColoring.proto";
import "attr_ClientPakInfo.proto";
import "attr_CocModeInfo.proto";
import "attr_CollectionInfos.proto";
import "attr_CombinationSettings.proto";
import "attr_CommonConditionGroup.proto";
import "attr_CommonGiftBuyInfo.proto";
import "attr_CommonLimitInfo.proto";
import "attr_CookInfo.proto";
import "attr_CupsInfo.proto";
import "attr_DailySumBuyTimesInfo.proto";
import "attr_DanceData.proto";
import "attr_DsUserDBInfo.proto";
import "attr_EntertainmentGuide.proto";
import "attr_ExchangeCenter.proto";
import "attr_FarmInfo.proto";
import "attr_FarmReturningInfo.proto";
import "attr_FireworksInfo.proto";
import "attr_FittingSlots.proto";
import "attr_FpsSettings.proto";
import "attr_FriendRecommendInfo.proto";
import "attr_GameModeReturnData.proto";
import "attr_GameTimesStatistics.proto";
import "attr_GameTvInfo.proto";
import "attr_GeneralRedDotInfo.proto";
import "attr_GuideStatistics.proto";
import "attr_GuidedDiscoverInfo.proto";
import "attr_HOKAttrInfo.proto";
import "attr_HOKSettings.proto";
import "attr_HouseInfo.proto";
import "attr_IdipTaskInfo.proto";
import "attr_IntellectualActivity.proto";
import "attr_Interaction.proto";
import "attr_ItaBagInfo.proto";
import "attr_ItemInfoDb.proto";
import "attr_ItemPackageLimit.proto";
import "attr_KeyValStr.proto";
import "attr_KvLL.proto";
import "attr_LabelInfo.proto";
import "attr_LastDressOutLookInfo.proto";
import "attr_LevelIllustration.proto";
import "attr_LevelRecord.proto";
import "attr_LimitTimeExperienceItem.proto";
import "attr_LobbyInfo.proto";
import "attr_LobbyMatchInfo.proto";
import "attr_MailCache.proto";
import "attr_MallInfo.proto";
import "attr_MallWishListPublic.proto";
import "attr_MarqueeNoticeInfo.proto";
import "attr_MatchStaticsDb.proto";
import "attr_MatchTypeHistory.proto";
import "attr_MatchUnlockInfo.proto";
import "attr_ModNote.proto";
import "attr_ModSettings.proto";
import "attr_Money.proto";
import "attr_MultiPlayerSquadInfo.proto";
import "attr_NR3E8RichInfo.proto";
import "attr_NewYearPilotInfo.proto";
import "attr_PartyInfo.proto";
import "attr_PasswordCodeData.proto";
import "attr_PixuiRedDotInfo.proto";
import "attr_PlayerBlessBagInfo.proto";
import "attr_PlayerEventData.proto";
import "attr_PlayerGameActionInfos.proto";
import "attr_PlayerGrayTagsInfo.proto";
import "attr_PlayerIAAData.proto";
import "attr_PlayerIAAInfo.proto";
import "attr_PlayerIdipInfo.proto";
import "attr_PlayerLevelEstimation.proto";
import "attr_PlayerPakPlayRecord.proto";
import "attr_PlayerProfileInfo.proto";
import "attr_PlayerPublicBasicInfo.proto";
import "attr_PlayerPublicEquipments.proto";
import "attr_PlayerPublicGameData.proto";
import "attr_PlayerPublicGameSettings.proto";
import "attr_PlayerPublicHistoryData.proto";
import "attr_PlayerPublicLiveStatus.proto";
import "attr_PlayerPublicProfileInfo.proto";
import "attr_PlayerPublicSceneData.proto";
import "attr_PlayerPublicSummaryInfo.proto";
import "attr_PlayerPushTopic.proto";
import "attr_PlayerRaffleGroupInfo.proto";
import "attr_PlayerRaffleInfo.proto";
import "attr_PlayerRankData.proto";
import "attr_PlayerSnsAcceptInfo.proto";
import "attr_PlayerSnsInvitationInfo.proto";
import "attr_PrayInfo.proto";
import "attr_PublicChatInfo.proto";
import "attr_QAInvestInfo.proto";
import "attr_QAInvestTag.proto";
import "attr_QQApplicationInfo.proto";
import "attr_RaffleBISeq.proto";
import "attr_RaffleStash.proto";
import "attr_RechargeInfo.proto";
import "attr_RecommendMatchType.proto";
import "attr_RedEnvelopeRainActivities.proto";
import "attr_RelationMapInfo.proto";
import "attr_ReturningInfoDb.proto";
import "attr_RewardCompensateInfo.proto";
import "attr_RewardNtfInfo.proto";
import "attr_RoguelikeExtraInfo.proto";
import "attr_RoguelikeInfo.proto";
import "attr_RoguelikeTalentData.proto";
import "attr_RoguelikeTaskData.proto";
import "attr_SafetyCheck.proto";
import "attr_SeasonInfo.proto";
import "attr_SeasonReview.proto";
import "attr_SecondaryPassword.proto";
import "attr_SelfMessageSlip.proto";
import "attr_ShareGiftInfo.proto";
import "attr_SpecRewardInfo.proto";
import "attr_SpecialFace.proto";
import "attr_StarPChatInfo.proto";
import "attr_StarPInfo.proto";
import "attr_StickFriendInfo.proto";
import "attr_StreamSetting.proto";
import "attr_SubscribeQqRobt.proto";
import "attr_TYCFpsSettings.proto";
import "attr_TargetEquipInfo.proto";
import "attr_TaskInfo.proto";
import "attr_TradingCardData.proto";
import "attr_UgcAccountInfo.proto";
import "attr_UgcActivityInfo.proto";
import "attr_UgcBuyGoodsInfo.proto";
import "attr_UgcCoPlayInfo.proto";
import "attr_UgcCollection.proto";
import "attr_UgcDailyStage.proto";
import "attr_UgcGrowUpInfo.proto";
import "attr_UgcMapSetInfo.proto";
import "attr_UgcMatchInfo.proto";
import "attr_UgcMiniGamePlayInfo.proto";
import "attr_UgcOpInfo.proto";
import "attr_UgcSlotInfo.proto";
import "attr_UgcStarWorld.proto";
import "attr_UserAllSeasonNoteBookAttr.proto";
import "attr_UserConcertData.proto";
import "attr_UserLabel.proto";
import "attr_WaitBattleInfo.proto";
import "attr_WelfareData.proto";
import "attr_WhiteListInfo.proto";
import "attr_WolfKillInfo.proto";
import "attr_WolfKillNewUserTipsInfo.proto";
import "attr_WolfKillRewardItem.proto";
import "attr_XiaoWoInfo.proto";
import "attr_XlsWhiteList.proto";

message proto_UserAttr {
    option (wea_attr_cls) = "com.tencent.wea.attr.UserAttr";
    // 玩家id
    optional int64 uid = 1;
    optional proto_PlayerProfileInfo playerProfileInfo = 2;
    // 基础信息
    optional proto_BasicInfo basicInfo = 3;
    // 事件数据
    optional proto_PlayerEventData playerEventData = 4;
    optional bool playerEventData_deleted = 2004;
    // 上架辅助信息
    optional proto_ActivityAddNewAidInfo activityAddNewAidInfo = 5;
    optional bool activityAddNewAidInfo_deleted = 2005;
    // 新活动中心
    optional proto_ActivityCenter activityCenter = 6;
    // 当前房间状态
    optional proto_AttrRoomInfo RoomInfo = 7;
    // 关系信息
    optional proto_RelationMapInfo relationMapInfo = 8;
    // 道具信息
    optional proto_ItemInfoDb itemInfo = 9;
    // 玩家可公开个人信息
    optional proto_PlayerPublicProfileInfo playerPublicProfileInfo = 10;
    // 背包信息
    optional proto_BagInfoDb bagInfo = 11;
    // 成就信息
    optional proto_AchievementInfo achievementInfo = 12;
    optional bool achievementInfo_deleted = 2012;
    // 玩家游戏货币
    optional proto_Money money = 13;
    // 商城信息
    optional proto_MallInfo mallInfo = 14;
    // 兑换中心
    optional proto_ExchangeCenter exchangeCenter = 16;
    // 图鉴
    repeated proto_CollectionInfos collectionInfo = 17;
    repeated com.tencent.wea.xlsRes.CollectionType collectionInfo_deleted = 2017;
    optional bool collectionInfo_is_cleared = 4017;
    // 任务关系
    optional proto_TaskInfo taskInfo = 18;
    optional bool taskInfo_deleted = 2018;
    // 服务器之间玩家公开摘要信息
    optional proto_PlayerPublicSummaryInfo playerPublicSummaryInfo = 19;
    optional bool playerPublicSummaryInfo_deleted = 2019;
    // 通关的关卡
    repeated int32 openedLevels = 20;
    repeated int32 openedLevels_deleted = 2020;
    optional bool openedLevels_is_cleared = 4020;
    // 战场信息
    optional proto_BattleInfo battleInfo = 21;
    // 游戏设置
    optional proto_ModSettings modSettings = 24;
    // 完成的引导任务ID
    repeated int32 guideTasks = 25;
    repeated int32 guideTasks_deleted = 2025;
    optional bool guideTasks_is_cleared = 4025;
    // 游戏数据备忘
    optional proto_ModNote modNote = 26;
    // 通用限制
    repeated proto_CommonLimitInfo commonLimit = 27;
    repeated com.tencent.wea.xlsRes.CommonLimitType commonLimit_deleted = 2027;
    optional bool commonLimit_is_cleared = 4027;
    // 公共聊天信息
    optional proto_PublicChatInfo publicChatInfo = 28;
    // 最后穿戴的装扮信息
    repeated proto_LastDressOutLookInfo lastDressOutLook = 29;
    repeated int32 lastDressOutLook_deleted = 2029;
    optional bool lastDressOutLook_is_cleared = 4029;
    // 当前公会信息
    optional proto_AttrClubInfo clubInfo = 30;
    // 当前通行证信息
    optional proto_AttrPermitInfo permitInfo = 31;
    // 位置穿戴信息
    repeated proto_TargetEquipInfo targetEquipInfo = 32;
    repeated int32 targetEquipInfo_deleted = 2032;
    optional bool targetEquipInfo_is_cleared = 4032;
    // 场景信息
    optional proto_AttrSceneInfo sceneInfo = 33;
    // ABTest信息
    repeated proto_ABTestInfo abTestInfo = 34;
    repeated int32 abTestInfo_deleted = 2034;
    optional bool abTestInfo_is_cleared = 4034;
    // 特殊奖励状态信息
    optional proto_SpecRewardInfo specReward = 35;
    // 权益卡
    optional proto_BenefitCardInfo benefitCardInfo = 36;
    // 礼包购买记录
    repeated proto_CommonGiftBuyInfo commonGiftBuyInfo = 37;
    repeated int64 commonGiftBuyInfo_deleted = 2037;
    optional bool commonGiftBuyInfo_is_cleared = 4037;
    // idip 任务记录
    optional proto_IdipTaskInfo idipTaskInfo = 38;
    optional bool idipTaskInfo_deleted = 2038;
    // 需要弹窗的奖励
    repeated proto_RewardNtfInfo rewardNtf = 39;
    repeated int32 rewardNtf_deleted = 2039;
    optional bool rewardNtf_is_cleared = 4039;
    optional proto_PlayerPublicEquipments playerPublicEquipments = 41;
    optional proto_PlayerPublicGameData playerPublicGameData = 42;
    optional proto_PlayerPublicGameSettings playerPublicGameSettings = 43;
    optional proto_PlayerPublicHistoryData playerPublicHistoryData = 44;
    optional proto_PlayerPublicLiveStatus playerPublicLiveStatus = 45;
    optional proto_PlayerPublicSceneData playerPublicSceneData = 46;
    // 解锁玩法
    repeated int32 unLockGameModeSet = 47;
    repeated int32 unLockGameModeSet_deleted = 2047;
    optional bool unLockGameModeSet_is_cleared = 4047;
    // 客户端云端存储
    repeated proto_ClientCache clientCacheData = 48;
    repeated int32 clientCacheData_deleted = 2048;
    optional bool clientCacheData_is_cleared = 4048;
    // 大厅信息
    optional proto_LobbyInfo lobbyInfo = 49;
    // 赛季信息
    optional proto_SeasonInfo seasonInfo = 50;
    // 排行信息
    optional proto_PlayerRankData rankData = 51;
    optional bool rankData_deleted = 2051;
    // 抽奖信息
    repeated proto_PlayerRaffleInfo raffleInfo = 52;
    repeated int32 raffleInfo_deleted = 2052;
    optional bool raffleInfo_is_cleared = 4052;
    // 活动小队
    optional proto_ActivitySquadInfo activitySquad = 53;
    // 关卡图鉴
    optional proto_LevelIllustration levelIllustration = 54;
    // 试衣间
    optional proto_FittingSlots fittingSlots = 55;
    // 交互表情动作槽位
    repeated proto_Interaction interactions = 56;
    repeated int32 interactions_deleted = 2056;
    optional bool interactions_is_cleared = 4056;
    // 充值和vip的一些信息
    optional proto_RechargeInfo rechargeInfo = 57;
    // ugc地图设置信息
    optional proto_UgcMapSetInfo ugcMapSetInfo = 58;
    // 我的留言
    optional proto_SelfMessageSlip selfMessageSlip = 59;
    optional bool selfMessageSlip_deleted = 2059;
    // 邮件缓存
    optional proto_MailCache mailCache = 61;
    optional bool mailCache_deleted = 2061;
    // 关卡记录，收集关卡关于赛季、玩法等维度的数据
    optional proto_LevelRecord levelRecord = 62;
    optional bool levelRecord_deleted = 2062;
    // 抽奖组信息
    repeated proto_PlayerRaffleGroupInfo raffleGroupInfo = 63;
    repeated int32 raffleGroupInfo_deleted = 2063;
    optional bool raffleGroupInfo_is_cleared = 4063;
    optional proto_PlayerPublicBasicInfo playerPublicBasicInfo = 64;
    // 问卷调查
    repeated proto_QAInvestInfo qaInvest = 65;
    repeated int32 qaInvest_deleted = 2065;
    optional bool qaInvest_is_cleared = 4065;
    // 匹配信息
    optional proto_MatchStaticsDb matchStatics = 66;
    // 个性化标签
    repeated proto_LabelInfo unLockLabels = 67;
    repeated int32 unLockLabels_deleted = 2067;
    optional bool unLockLabels_is_cleared = 4067;
    // 祈福数据
    optional proto_PrayInfo prayInfo = 68;
    // 问卷标签
    repeated proto_QAInvestTag qaInvestTag = 69;
    repeated int32 qaInvestTag_deleted = 2069;
    optional bool qaInvestTag_is_cleared = 4069;
    // 礼包限制计数
    repeated proto_ItemPackageLimit itemPackageLimit = 70;
    repeated int32 itemPackageLimit_deleted = 2070;
    optional bool itemPackageLimit_is_cleared = 4070;
    // ugc日常闯关
    optional proto_UgcDailyStage ugcDailyStage = 71;
    optional bool ugcDailyStage_deleted = 2071;
    // 小窝信息
    optional proto_XiaoWoInfo xiaoWoInfo = 72;
    // 派对
    optional proto_PartyInfo partyInfo = 73;
    // ugc粉丝订阅操作额外信息
    optional proto_UgcOpInfo ugcOpInfo = 74;
    // 未结算的battle
    repeated proto_WaitBattleInfo waitSettlementBattle = 75;
    repeated int64 waitSettlementBattle_deleted = 2075;
    optional bool waitSettlementBattle_is_cleared = 4075;
    // 玩家福袋信息
    optional proto_PlayerBlessBagInfo playerBlessBagInfo = 76;
    // ugc新版巡游
    optional proto_UgcStarWorld ugcStarWorld = 77;
    // 肉鸽玩法数据
    optional proto_RoguelikeInfo roguelikeInfo = 78;
    // (已废弃)肉鸽玩法额外数据
    optional proto_RoguelikeExtraInfo roguelikeExtraInfo = 79;
    // (已废弃)肉鸽玩法天赋数据
    optional proto_RoguelikeTalentData roguelikeTalentData = 80;
    // 多人小队
    optional proto_MultiPlayerSquadInfo multiPlayerSquad = 81;
    // ugc账号信息
    optional proto_UgcAccountInfo ugcAccountInfo = 82;
    // 开播设置 - 废弃
    optional proto_StreamSetting streamSetting = 83;
    // 分享邀请
    repeated proto_PlayerSnsInvitationInfo snsInvitationInfo = 84;
    repeated int64 snsInvitationInfo_deleted = 2084;
    optional bool snsInvitationInfo_is_cleared = 4084;
    // 助力记录
    repeated proto_PlayerSnsAcceptInfo snsInvitationAccepted = 85;
    repeated int32 snsInvitationAccepted_deleted = 2085;
    optional bool snsInvitationAccepted_is_cleared = 4085;
    // 烟花活动信息
    optional proto_FireworksInfo fireworksInfo = 86;
    // 红包雨活动
    optional proto_RedEnvelopeRainActivities redEnvelopeRainActs = 87;
    // 通用功能解锁条件进度保存
    repeated proto_CommonConditionGroup commonConditionAttr = 88;
    repeated com.tencent.wea.xlsRes.ContionGroupType commonConditionAttr_deleted = 2088;
    optional bool commonConditionAttr_is_cleared = 4088;
    // idip修改的数据
    optional proto_PlayerIdipInfo idipInfo = 89;
    // qq群应用相关信息
    optional proto_QQApplicationInfo qqApplicationInfo = 90;
    // DS中玩家需要存储的信息集合(不再使用，移动到了tcaplus中UserDsDBTable独立存储)
    repeated proto_DsUserDBInfo dsUserDBInfoMap = 91;
    repeated int32 dsUserDBInfoMap_deleted = 2091;
    optional bool dsUserDBInfoMap_is_cleared = 4091;
    // FPS玩法设置
    optional proto_FpsSettings fpsSettings = 92;
    optional bool fpsSettings_deleted = 2092;
    // (已废弃)肉鸽玩法任务数据
    optional proto_RoguelikeTaskData roguelikeTaskData = 93;
    optional bool roguelikeTaskData_deleted = 2093;
    // 专属拍脸数据
    repeated proto_SpecialFace specialFaceAttr = 94;
    repeated int32 specialFaceAttr_deleted = 2094;
    optional bool specialFaceAttr_is_cleared = 4094;
    // 全服跑马灯数据
    optional proto_MarqueeNoticeInfo marqueeNoticeInfo = 95;
    optional bool marqueeNoticeInfo_deleted = 2095;
    // 公益相关数据
    optional proto_WelfareData welfareData = 96;
    optional bool welfareData_deleted = 2096;
    // 回归数据
    optional proto_ReturningInfoDb returningInfo = 97;
    // 新年活动数据
    optional proto_NewYearPilotInfo newYearPilotInfo = 98;
    optional bool newYearPilotInfo_deleted = 2098;
    // 玩家数据修复标记
    repeated proto_KeyValStr fixTag = 99;
    repeated string fixTag_deleted = 2099;
    optional bool fixTag_is_cleared = 4099;
    // 脑力达人活动
    optional proto_IntellectualActivity intellectualActivity = 100;
    optional bool intellectualActivity_deleted = 2100;
    // tycFPS玩法设置
    repeated proto_TYCFpsSettings tycFpsSettings = 101;
    repeated int32 tycFpsSettings_deleted = 2101;
    optional bool tycFpsSettings_is_cleared = 4101;
    // UGC同游记录
    optional proto_UgcCoPlayInfo ugcCoPlayInfo = 102;
    optional bool ugcCoPlayInfo_deleted = 2102;
    // UGC合集
    optional proto_UgcCollection ugcCollection = 103;
    // 玩家近期活跃数据
    optional proto_AttrRecentActivity recentActivity = 104;
    optional bool recentActivity_deleted = 2104;
    // 智能npc对话信息
    optional proto_AiChatInfo aiChatInfo = 105;
    optional bool aiChatInfo_deleted = 2105;
    // 白名单信息
    repeated proto_WhiteListInfo whiteListInfo = 106;
    repeated int32 whiteListInfo_deleted = 2106;
    optional bool whiteListInfo_is_cleared = 4106;
    // 好友置顶信息
    repeated proto_StickFriendInfo stickFriends = 107;
    repeated int32 stickFriends_deleted = 2107;
    optional bool stickFriends_is_cleared = 4107;
    // ugc地图内购信息
    optional proto_UgcBuyGoodsInfo ugcBuyGoodsInfo = 108;
    // 玩家灰度标签信息
    optional proto_PlayerGrayTagsInfo grayTagsInfo = 109;
    // 用户标签信息
    repeated proto_UserLabel userLabels = 110;
    repeated int32 userLabels_deleted = 2110;
    optional bool userLabels_is_cleared = 4110;
    // 小队活动信息集合
    repeated proto_ActivitySquadDetail activitySquadDetails = 111;
    repeated int32 activitySquadDetails_deleted = 2111;
    optional bool activitySquadDetails_is_cleared = 4111;
    // 演唱会数据
    optional proto_UserConcertData concertData = 112;
    optional bool concertData_deleted = 2112;
    // ugc快捷方式 (全局)
    optional proto_UgcSlotInfo ugcSlotKeyInfo = 113;
    // 农场信息
    optional proto_FarmInfo farmInfo = 114;
    // 电视台信息
    optional proto_GameTvInfo gameTvInfo = 115;
    // 配置的白名单
    repeated proto_XlsWhiteList xlsWhiteList = 116;
    repeated string xlsWhiteList_deleted = 2116;
    optional bool xlsWhiteList_is_cleared = 4116;
    // 组合的设置
    optional proto_CombinationSettings combinationSettings = 117;
    // ugc星世界成长(星世界等级、活跃、徽章等)
    optional proto_UgcGrowUpInfo ugcGrowUpInfo = 118;
    // 气泡入口配置
    optional proto_BubbleConfig bubble = 119;
    // 相册
    optional proto_AlbumInfo album = 120;
    // 玩法模式-解锁
    repeated proto_MatchUnlockInfo matchUnlock = 121;
    repeated int32 matchUnlock_deleted = 2121;
    optional bool matchUnlock_is_cleared = 4121;
    // qq订阅机器人数据
    optional proto_SubscribeQqRobt qqbot = 122;
    // 限时娱乐推荐
    optional proto_RecommendMatchType recommendMatchType = 123;
    // 引导数据统计
    optional proto_GuideStatistics guideStatistics = 124;
    // 玩法模式-历史玩过
    optional proto_MatchTypeHistory matchTypeHistory = 125;
    // 好友推荐信息
    optional proto_FriendRecommendInfo friendRecommendInfo = 126;
    // IAA信息
    repeated proto_PlayerIAAInfo iaaInfo = 127;
    repeated int32 iaaInfo_deleted = 2127;
    optional bool iaaInfo_is_cleared = 4127;
    // 娱乐向导
    optional proto_EntertainmentGuide entertainmentGuide = 128;
    // ugc匹配信息
    optional proto_UgcMatchInfo ugcMatchInfo = 129;
    optional bool ugcMatchInfo_deleted = 2129;
    // 算法专属玩法推荐
    optional proto_AlgoRecommendMatchInfo algoRecommendMatchInfo = 130;
    // 奖励补领信息
    optional proto_RewardCompensateInfo rewardCompensate = 131;
    // 二级密码
    optional proto_SecondaryPassword secondaryPassword = 132;
    // 奖杯信息
    optional proto_CupsInfo cupsInfo = 133;
    // pixui红点信息
    repeated proto_PixuiRedDotInfo pixuiRedDot = 134;
    repeated int32 pixuiRedDot_deleted = 2134;
    optional bool pixuiRedDot_is_cleared = 4134;
    // 狼人交互表情动作槽位
    repeated proto_Interaction wolfKillInteractions = 135;
    repeated int32 wolfKillInteractions_deleted = 2135;
    optional bool wolfKillInteractions_is_cleared = 4135;
    // 用于存放不用放在PlayerPublic中的赛季手册数据
    optional proto_UserAllSeasonNoteBookAttr allSeasonNoteBookAttr = 136;
    // BP通行证信息
    repeated proto_BPInfo bpInfo = 137;
    repeated int32 bpInfo_deleted = 2137;
    optional bool bpInfo_is_cleared = 4137;
    // 抽奖BI推荐顺序
    optional proto_RaffleBISeq biSeq = 138;
    // 账号日志着色
    repeated proto_ClientLogColoring clientLogColoring = 139;
    repeated int64 clientLogColoring_deleted = 2139;
    optional bool clientLogColoring_is_cleared = 4139;
    // 解锁备战按钮
    repeated int32 unLockPreparationWidgetSet = 140;
    repeated int32 unLockPreparationWidgetSet_deleted = 2140;
    optional bool unLockPreparationWidgetSet_is_cleared = 4140;
    // 上次更新大版本的时间
    optional int64 lastUpdateMainVersionTimeMs = 141;
    // 需要同步的ugc活动数据
    optional proto_UgcActivityInfo ugcActivityInfo = 142;
    // 副玩法次数统计
    optional proto_GameTimesStatistics gameTimesStatistics = 143;
    // arena玩法game数据
    optional proto_ArenaGameData arenaGameData = 144;
    // IAA数据
    optional proto_PlayerIAAData iaaData = 145;
    // 农场小屋信息
    optional proto_HouseInfo houseInfo = 147;
    // 狼人一局获取的奖励物品，用于合并发送
    repeated proto_WolfKillRewardItem wolfKillSeasonRewardList = 148;
    repeated int64 wolfKillSeasonRewardList_deleted = 2148;
    optional bool wolfKillSeasonRewardList_is_cleared = 4148;
    // 客户端分包信息
    optional proto_ClientPakInfo clientPakInfo = 149;
    // 抽奖组暂存信息
    repeated proto_RaffleStash raffleStash = 150;
    repeated int64 raffleStash_deleted = 2150;
    optional bool raffleStash_is_cleared = 4150;
    // 狼人局内新手提示
    repeated proto_WolfKillNewUserTipsInfo wolfKillNewUserTips = 151;
    repeated int32 wolfKillNewUserTips_deleted = 2151;
    optional bool wolfKillNewUserTips_is_cleared = 4151;
    // 所有的开播信息
    repeated proto_StreamSetting streamSettingList = 152;
    repeated int32 streamSettingList_deleted = 2152;
    optional bool streamSettingList_is_cleared = 4152;
    // moba打赏记录
    optional proto_ArenaTipInfo arenaTipInfo = 153;
    // moba 设置
    optional proto_ArenaSettings arenaSettings = 154;
    // 已评价关卡ID集合
    optional proto_PlayerLevelEstimation playerLevelEstimation = 155;
    // hok玩法设置
    optional proto_HOKSettings hokSettings = 156;
    // 需要记录的玩家游戏内动作
    optional proto_PlayerGameActionInfos playerGameActionInfos = 157;
    // hok的属性信息
    optional proto_HOKAttrInfo hokAttrInfo = 158;
    // 发现引导信息
    optional proto_GuidedDiscoverInfo guidedDiscoverInfo = 159;
    // 奖励找回
    optional proto_AttrRewardRetrievalData rewardRetrieval = 160;
    // 商城心愿单
    optional proto_MallWishListPublic mallWishList = 161;
    // 历史解锁过的玩法
    repeated int32 unLockGameModeHistorySet = 162;
    repeated int32 unLockGameModeHistorySet_deleted = 2162;
    optional bool unLockGameModeHistorySet_is_cleared = 4162;
    // 时尚之路
    optional proto_AppearanceRoad appearanceRoad = 163;
    // 狼人杀的其他信息
    optional proto_WolfKillInfo wolfKillInfo = 164;
    // 卡牌数据
    optional proto_TradingCardData tradingCardData = 165;
    // 推送信息
    optional proto_PlayerPushTopic pushTopicInfo = 166;
    // 万松书院匹配信息
    optional proto_LobbyMatchInfo lobbyMatchInfo = 167;
    // coc玩法数据
    optional proto_CocModeInfo cocModeInfo = 168;
    // 生日数据
    optional proto_AttrBirthdayData birthdayData = 169;
    // 赛季回顾统计数据
    optional proto_SeasonReview seasonReview = 170;
    // 分享礼包数据
    optional proto_ShareGiftInfo shareGiftInfo = 171;
    // 农场回流活动管理
    optional proto_FarmReturningInfo farmReturningInfo = 172;
    // 大王玩法game数据
    optional proto_ChaseGameData chaseGameData = 173;
    // 需要安全性检查的结构
    optional proto_SafetyCheck safetyCheck = 174;
    // UGC小游戏专区游玩记录
    optional proto_UgcMiniGamePlayInfo ugcMiniGamePlayInfo = 175;
    optional bool ugcMiniGamePlayInfo_deleted = 2175;
    // 口令码任务数据
    repeated proto_PasswordCodeData passwordCodeDataList = 176;
    repeated int32 passwordCodeDataList_deleted = 2176;
    optional bool passwordCodeDataList_is_cleared = 4176;
    // 大富翁信息
    optional proto_NR3E8RichInfo richInfo = 177;
    // 通用红点数据
    repeated proto_GeneralRedDotInfo generalRedDotMap = 178;
    repeated int32 generalRedDotMap_deleted = 2178;
    optional bool generalRedDotMap_is_cleared = 4178;
    // 乐队舞台信息
    optional proto_DanceData danceData = 179;
    // 玩法回流数据
    optional proto_GameModeReturnData gameReturnModeData = 180;
    // 农场餐厅信息
    optional proto_CookInfo cookInfo = 181;
    // 大王玩法game数据,不存public
    optional proto_ChaseGameDataNoPublic chaseGameDataNoPublic = 182;
    // 限时体验道具
    optional proto_LimitTimeExperienceItem limitTimeExperienceItem = 183;
    // 啾灵数据
    optional proto_StarPInfo starPInfo = 184;
    optional bool starPInfo_deleted = 2184;
    // 啾灵聊天系统信息(适用元梦大厅&SP玩法)
    optional proto_StarPChatInfo starPChatInfo = 185;
    // 记录最近N天moba对战的胜利次数
    repeated proto_ArenaDailyVictoryRecord dailyVictoryRecord = 186;
    repeated int64 dailyVictoryRecord_deleted = 2186;
    optional bool dailyVictoryRecord_is_cleared = 4186;
    // 是否发送5v5新手体验卡 0：未发送  1：已发送
    optional int32 sendHokExperienceCard = 187;
    // 打卡手册历史购买次数最后一天前
    optional int32 upgradeCheckHistoryBuyTimes = 188;
    // 农场天天领总购买次数
    optional int32 farmDailySumBuyTimes = 189;
    // 打卡手册最后一天购买次数
    optional int32 upgradeCheckLastDayBuyTime = 190;
    // 闪电赛助威数据 KEY-阶段截止时间戳 value-周期内是否助威 0未助威 1已助威
    repeated proto_KvLL flashRaceCheerAi = 191;
    repeated int64 flashRaceCheerAi_deleted = 2191;
    optional bool flashRaceCheerAi_is_cleared = 4191;
    // 时装技能使用
    repeated int32 fashionSkilluse = 192;
    repeated int32 fashionSkilluse_deleted = 2192;
    optional bool fashionSkilluse_is_cleared = 4192;
    // 玩家最近x周玩法次数记录
    repeated proto_PlayerPakPlayRecord playerPakPlayRecord = 193;
    repeated int32 playerPakPlayRecord_deleted = 2193;
    optional bool playerPakPlayRecord_is_cleared = 4193;
    // 强制打开的玩法id
    repeated int32 forceOpenMatchType = 194;
    repeated int32 forceOpenMatchType_deleted = 2194;
    optional bool forceOpenMatchType_is_cleared = 4194;
    // 玩家登入过的平台
    repeated int32 playerLoginPlat = 195;
    repeated int32 playerLoginPlat_deleted = 2195;
    optional bool playerLoginPlat_is_cleared = 4195;
    // 痛包 itemUUID->ItaBagInfo
    repeated proto_ItaBagInfo itaBagData = 196;
    repeated int64 itaBagData_deleted = 2196;
    optional bool itaBagData_is_cleared = 4196;
    // 天天领总购买次数
    repeated proto_DailySumBuyTimesInfo dailyAwardSumBuyInfoMap = 197;
    repeated int32 dailyAwardSumBuyInfoMap_deleted = 2197;
    optional bool dailyAwardSumBuyInfoMap_is_cleared = 4197;
    // 推图展板
    optional proto_AttrDisplayBoardData displayBoard = 200;
}